<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,100分)- 拼接URL（Java & JS & Python）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>给定一个url前缀和url后缀,通过,分割 需要将其连接为一个完整的url</p> 
<ul><li>如果前缀结尾和后缀开头都没有/&#xff0c;需要自动补上/连接符</li><li>如果前缀结尾和后缀开头都为/&#xff0c;需要自动去重</li></ul> 
<p>约束&#xff1a;不用考虑前后缀URL不合法情况</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>url前缀(一个长度小于100的字符串)&#xff0c;url后缀(一个长度小于100的字符串)</p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0"><br /> 输出描述</h4> 
<p>拼接后的url</p> 
<p></p> 
<h4>用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:65px;">输入</td><td style="width:433px;">/acm,/bb</td></tr><tr><td style="width:65px;">输出</td><td style="width:433px;">/acm/bb</td></tr><tr><td style="width:65px;">说明</td><td style="width:433px;">无</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:65px;">输入</td><td style="width:434px;">/abc/,/bcd</td></tr><tr><td style="width:65px;">输出</td><td style="width:434px;">/abc/bcd</td></tr><tr><td style="width:65px;">说明</td><td style="width:434px;">无</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:63px;">输入</td><td style="width:435px;">/acd,bef</td></tr><tr><td style="width:63px;">输出</td><td style="width:435px;">/acd/bef</td></tr><tr><td style="width:63px;">说明</td><td style="width:435px;">无</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:63px;">输入</td><td style="width:435px;">,</td></tr><tr><td style="width:63px;">输出</td><td style="width:435px;">/</td></tr><tr><td style="width:63px;">说明</td><td style="width:435px;">无</td></tr></tbody></table> 
<p></p> 
<h4>题目解析</h4> 
<p>逻辑题&#xff0c;可以直接看代码实现了解逻辑。</p> 
<hr /> 
<p>20230813 本题Java解法中split函数比较难搞&#xff0c;对于下面情况按照&#34;,&#34;分割出来的字符串数组分别是&#xff1a;</p> 
<ul><li>&#34;,&#34;     &#61;&gt;   []</li><li>&#34;1,&#34;   &#61;&gt;   [&#34;1&#34;]</li><li>&#34;,1&#34;   &#61;&gt;   [&#34;&#34;, &#34;1&#34;]</li></ul> 
<p>我们需要注意处理下。</p> 
<p>JS和Python的split函数就要规矩的多&#xff0c;上面三种输入情况&#xff0c;都会返回一个长度为2的字符串数组。</p> 
<hr /> 
<p>更新解法使用了正则表达式&#xff0c;其中</p> 
<p>prefix前缀&#xff0c;需要将其结尾的多个/去除&#xff0c;使用正则 /&#43;$ 来匹配结尾的多个/</p> 
<p>suffix后缀&#xff0c;需要将其开头的多个/去除&#xff0c;使用正则 ^/&#43; 来匹配开头的多个/</p> 
<p>最后拼接 prefix &#43; &#34;/&#34; &#43; suffix 即可</p> 
<p></p> 
<p>对于python而言&#xff0c;有更简单的lstrip和rstrip方法&#xff0c;实现同样效果。</p> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.Scanner;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    String s &#61; sc.nextLine();

    String[] arr &#61; s.split(&#34;,&#34;);

    String prefix &#61; arr.length &gt; 0 &amp;&amp; arr[0].length() &gt; 0 ? arr[0] : &#34;/&#34;;
    String suffix &#61; arr.length &gt; 1 &amp;&amp; arr[1].length() &gt; 0 ? arr[1] : &#34;/&#34;;

    System.out.println(getResult(prefix, suffix));
  }

  public static String getResult(String prefix, String suffix) {
    prefix &#61; prefix.replaceAll(&#34;/&#43;$&#34;, &#34;&#34;);
    suffix &#61; suffix.replaceAll(&#34;^/&#43;&#34;, &#34;&#34;);
    return prefix &#43; &#34;/&#34; &#43; suffix;
  }
}
</code></pre> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JS算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.on(&#34;line&#34;, (line) &#61;&gt; {
  let [prefix, suffix] &#61; line.split(&#34;,&#34;);

  prefix &#61; prefix.replace(/\/&#43;$/, &#34;&#34;);
  suffix &#61; suffix.replace(/^\/&#43;/, &#34;&#34;);

  console.log(prefix &#43; &#34;/&#34; &#43; suffix);
});
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
prefix, suffix &#61; input().split(&#34;,&#34;)

prefix &#61; prefix.rstrip(&#34;/&#34;)
suffix &#61; suffix.lstrip(&#34;/&#34;)

print(prefix &#43; &#34;/&#34; &#43; suffix)
</code></pre>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>