<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,200分)- 任务最优调度（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>给定一个正整数数组表示待系统执行的任务列表&#xff0c;数组的每一个元素代表一个任务&#xff0c;元素的值表示该任务的类型。</p> 
<p>请计算执行完所有任务所需的最短时间。</p> 
<p>任务执行规则如下:</p> 
<ol><li>任务可以按任意顺序执行&#xff0c;且每个任务执行耗时间均为1个时间单位。</li><li>两个同类型的任务之间必须有长度为N个单位的冷却时间&#xff0c;比如N为2时&#xff0c;在时间K执行了类型3的任务&#xff0c;那么K&#43;1和K&#43;2两个时间不能执行类型3任务。</li><li>系统在任何一个单位时间内都可以执行一个任务&#xff0c;或者等待状态。</li></ol> 
<p>说明&#xff1a;数组最大长度为1000&#xff0c;数组最大值1000。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<ul><li>第一行记录一个用半角逗号分隔的数组&#xff0c;数组长度不超过1000&#xff0c;数组元素的值不超过1000&#xff0c;</li><li>第二行记录任务冷却时间&#xff0c;N为正整数&#xff0c;N&lt;&#61;100。</li></ul> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>输出为执行完所有任务所需的最短时间。</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">2,2,2,3<br /> 2</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">7</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">时间1&#xff1a;执行类型2任务。<br /> 时间2&#xff1a;执行类型3的任务&#xff08;因为冷却时间为2&#xff0c;所以时间2不能执行类型2的任务&#xff09;。<br /> 时间3&#xff1a;系统等待&#xff08;仍然在类型2的冷却时间&#xff09;。<br /> 时间4&#xff1a;执行类型2任务。<br /> 时间5&#xff1a;系统等待。<br /> 时间6&#xff1a;系统等待。<br /> 时间7&#xff1a;执行类型2任务。<br /> 因此总共耗时7。</td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>本题考察贪心算法。</p> 
<p></p> 
<p>我的解题思路如下&#xff1a;</p> 
<p>首先&#xff0c;我们统计出各种任务的数量&#xff0c;并找出数量最多的任务&#xff0c;比如题目用例中&#xff1a;</p> 
<ul><li>任务2的数量是&#xff1a;3个</li><li>任务3的数量是&#xff1a;1个</li></ul> 
<p>其中任务2的数量最多&#xff0c;有3个&#xff0c;我们假设k&#61;3&#xff0c;那么完成所有任务所需时间至少为&#xff1a;</p> 
<blockquote> 
 <p>(k - 1) * (n &#43; 1) &#43; 1</p> 
</blockquote> 
<p>画图示意如下&#xff1a;</p> 
<p><img alt="" height="278" src="https://img-blog.csdnimg.cn/990cd15986c84991a2939e25a584085a.png" width="519" /></p> 
<p>其中n为冷却时间&#xff0c;k为最多任务的数量。</p> 
<p>如果其他任务数量较少的话&#xff0c;可以直接在任务2的冷却时间中运行。比如题目用例运行图如下&#xff1a;</p> 
<p><img alt="" height="276" src="https://img-blog.csdnimg.cn/5304c2a959874a6bb680bf0496af94ce.png" width="416" /></p> 
<p>此时&#xff0c;总用时仍然为 (k - 1) * (n &#43; 1) &#43; 1。</p> 
<p></p> 
<p>理解了上面公式后&#xff0c;我们可以继续看下几种特殊情况&#xff1a;</p> 
<p>1、数量最多的任务有多个&#xff0c;比如用例</p> 
<blockquote> 
 <p>2,2,2,3,3,3</p> 
 <p>2</p> 
</blockquote> 
<p>此时画图示意如下&#xff1a;</p> 
<p><img alt="" height="272" src="https://img-blog.csdnimg.cn/aff15d3aacb349b892e2eee08f79bff5.png" width="467" /></p> 
<p>此时至少用时为&#xff1a; (k - 1) * (n &#43; 1) &#43; 2</p> 
<p></p> 
<p>再比如用例</p> 
<blockquote> 
 <p>2,2,2,3,3,3,4,4,4</p> 
 <p>2</p> 
</blockquote> 
<p><img alt="" height="278" src="https://img-blog.csdnimg.cn/1e8c1c7222914c948e6d2cd53a8aff9d.png" width="467" /></p> 
<p>此时至少用时为&#xff1a; (k - 1) * (n &#43; 1) &#43; 3</p> 
<p></p> 
<p>可以发现&#xff0c;如果数量最多的任务有多个&#xff0c;假设为p个&#xff0c;则此时至少用时公式应该为&#xff1a;</p> 
<blockquote> 
 <p><span style="color:#fe2c24;"> (k - 1) * (n &#43; 1) &#43; p</span></p> 
</blockquote> 
<p></p> 
<p>另外&#xff0c;还有一种特殊情况&#xff0c;如下用例</p> 
<blockquote> 
 <p>2,2,2,3,3,3,4,4,4,5,5,5</p> 
 <p>2</p> 
</blockquote> 
<p>此时任务5有两种执行策略&#xff1a;</p> 
<p>策略一如下&#xff1a;</p> 
<p><img alt="" height="276" src="https://img-blog.csdnimg.cn/297f375c79cd45b1baecbf7a42fd891d.png" width="366" /></p> 
<p> 策略二如下&#xff1a;</p> 
<p><img alt="" height="397" src="https://img-blog.csdnimg.cn/54ba1f93468f479ebea6adfe767dd02a.png" width="364" /></p> 
<p>很明显策略一更加省时。 因为策略一少了任务5的冷却时间。</p> 
<p>可以发现&#xff0c;此时策略一的用时就是&#xff1a;总任务数量&#xff08;每个任务执行耗时间均为1个时间单位&#xff09;</p> 
<p></p> 
<p>因此&#xff1a;</p> 
<ul><li>如果总任务数量 &gt;&#61; <span style="color:#fe2c24;"> (k - 1) * (n &#43; 1) &#43; p</span><span style="color:#0d0016;">&#xff0c;则最少用时就是总任务数量本身。</span></li><li><span style="color:#0d0016;">如果总任务数量 &lt; </span><span style="color:#fe2c24;"> (k - 1) * (n &#43; 1) &#43; p</span><span style="color:#0d0016;">&#xff0c;则最少用时就是</span><span style="color:#fe2c24;"> (k - 1) * (n &#43; 1) &#43; p</span><span style="color:#0d0016;">。</span></li></ul> 
<p>即取二者较大值。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const lines &#61; [];
rl.on(&#34;line&#34;, (line) &#61;&gt; {
  lines.push(line);

  if (lines.length &#61;&#61;&#61; 2) {
    const tasks &#61; lines[0].split(&#34;,&#34;).map(Number);
    const n &#61; lines[1] - 0;

    console.log(getResult(tasks, n));

    lines.length &#61; 0;
  }
});

function getResult(tasks, n) {
  const cnts &#61; {};

  for (let task of tasks) {
    cnts[task] ? cnts[task]&#43;&#43; : (cnts[task] &#61; 1);
  }

  // k表示:最多任务的数量
  // 比如2,2,2,3&#xff0c; 其中任务2数量最多&#xff0c;有3个&#xff0c;则k &#61; 3
  const k &#61; Math.max(...Object.values(cnts));

  // p表示:数量为k的任务个数
  // 比如2,2,2,3,3,3,4&#xff0c; 其中数量为3的任务有2个&#xff0c;分别是任务2&#xff0c;任务3&#xff0c;则p&#61;2
  let p &#61; 0;

  for (let task in cnts) {
    if (cnts[task] &#61;&#61; k) p&#43;&#43;;
  }

  return Math.max((k - 1) * (n &#43; 1) &#43; p, tasks.length);
}
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.Arrays;
import java.util.HashMap;
import java.util.Scanner;

public class Main {
  // 输入获取
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    int[] tasks &#61; Arrays.stream(sc.next().split(&#34;,&#34;)).mapToInt(Integer::parseInt).toArray();
    int n &#61; sc.nextInt();

    System.out.println(getResult(tasks, n));
  }

  // 算法入口
  public static int getResult(int[] tasks, int n) {
    HashMap&lt;Integer, Integer&gt; cnts &#61; new HashMap&lt;&gt;();

    for (int task : tasks) {
      cnts.put(task, cnts.getOrDefault(task, 0) &#43; 1);
    }

    // k表示:最多任务的数量
    // 比如2,2,2,3&#xff0c; 其中任务2数量最多&#xff0c;有3个&#xff0c;则k &#61; 3
    int k &#61; cnts.values().stream().max((a, b) -&gt; a - b).orElse(0);

    // p表示:数量为k的任务个数
    // 比如2,2,2,3,3,3,4&#xff0c; 其中数量为3的任务有2个&#xff0c;分别是任务2&#xff0c;任务3&#xff0c;则p&#61;2
    int p &#61; 0;
    for (Integer task : cnts.keySet()) {
      if (cnts.get(task) &#61;&#61; k) p&#43;&#43;;
    }

    return Math.max((k - 1) * (n &#43; 1) &#43; p, tasks.length);
  }
}
</code></pre> 
<p></p> 
<h4 style="background-color:transparent;">Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
tasks &#61; list(map(int, input().split(&#34;,&#34;)))
n &#61; int(input())


# 算法入口
def getResult():
    cnts &#61; {}

    for task in tasks:
        cnts[task] &#61; cnts.get(task, 0) &#43; 1

    # k表示: 最多任务的数量
    # 比如2, 2, 2, 3&#xff0c; 其中任务2数量最多&#xff0c;有3个&#xff0c;则k &#61; 3
    k &#61; max(cnts.values())

    # p表示: 数量为k的任务个数
    # 比如2, 2, 2, 3, 3, 3, 4&#xff0c; 其中数量为3的任务有2个&#xff0c;分别是任务2&#xff0c;任务3&#xff0c;则p &#61; 2
    p &#61; 0
    for task in cnts:
        if cnts[task] &#61;&#61; k:
            p &#43;&#61; 1

    return max((k - 1) * (n &#43; 1) &#43; p, len(tasks))


# 算法调用
print(getResult())
</code></pre> 
<h4>C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;

#define MAX(a, b) ((a) &gt; (b) ? (a) : (b))

#define MAX_SIZE 1001

int main() {
    // 一个正整数数组表示待系统执行的任务列表
    int tasks[MAX_SIZE];
    int tasks_size &#61; 0;

    while (scanf(&#34;%d&#34;, &amp;tasks[tasks_size&#43;&#43;])) {
        if (getchar() !&#61; &#39;,&#39;) break;
    }

    // 任务冷却时间
    int n;
    scanf(&#34;%d&#34;, &amp;n);

    // k表示:最多任务的数量
    // 比如2,2,2,3&#xff0c; 其中任务2数量最多&#xff0c;有3个&#xff0c;则k &#61; 3
    int k &#61; 0;

    int cnts[MAX_SIZE] &#61; {0};
    for (int i &#61; 0; i &lt; tasks_size; i&#43;&#43;) {
        int task &#61; tasks[i];
        cnts[task]&#43;&#43;;
        k &#61; MAX(k, cnts[task]);
    }

    // p表示:数量为k的任务个数
    // 比如2,2,2,3,3,3,4&#xff0c; 其中数量为3的任务有2个&#xff0c;分别是任务2&#xff0c;任务3&#xff0c;则p&#61;2
    int p &#61; 0;
    for (int i &#61; 0; i &lt; MAX_SIZE; i&#43;&#43;) {
        if (cnts[i] &#61;&#61; k) p&#43;&#43;;
    }

    printf(&#34;%d\n&#34;, MAX((k - 1) * (n &#43; 1) &#43; p, tasks_size));

    return 0;
}</code></pre>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>