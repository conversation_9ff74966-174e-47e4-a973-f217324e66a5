<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,200分)- 贪吃的猴子（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4>在线OJ刷题</h4> 
<p><a href="https://hydro.ac/d/HWOD2023/p/OD399" rel="nofollow" title="题目详情 - 贪吃的猴子 - Hydro">题目详情 - 贪吃的猴子 - Hydro</a></p> 
<p></p> 
<h4 id="main-toc">题目描述</h4> 
<p>一只贪吃的猴子&#xff0c;来到一个果园&#xff0c;发现许多串香蕉排成一行&#xff0c;每串香蕉上有若干根香蕉。每串香蕉的根数由数组numbers给出。</p> 
<p>猴子获取香蕉&#xff0c;每次都只能从行的开头或者末尾获取&#xff0c;并且只能获取N次&#xff0c;求猴子最多能获取多少根香蕉。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>第一行为数组numbers的长度</p> 
<p>第二行为数组numbers的值每个数字通过空格分开</p> 
<p>第三行输入为N&#xff0c;表示获取的次数</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>按照题目要求能获取的最大数值</p> 
<p></p> 
<h4>备注</h4> 
<ul><li>1 ≤ numbers.length ≤ 100000</li><li>1 ≤ numbers ≤ 100</li><li>1 ≤ N ≤ numbers.length</li></ul> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">7<br /> 1 2 2 7 3 6 1<br /> 3</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">10</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">第一次获取香蕉&#xff0c;无论是从行的开头或者末尾获取&#xff0c;得到的香蕉根数目为1, 但是&#xff0c;从行末尾获取能获取到最优的策略&#xff0c;后面可以直接得到香蕉根数目6和3。因此最终根数为1&#43;6&#43;3&#61;10</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">3<br /> 1 2 3<br /> 3</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">6</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">全部获取所有的香蕉&#xff0c;因此最终根数为1&#43;2&#43;3&#61;6</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">4<br /> 4 2 2 3<br /> 2</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">7</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">第一次获取香蕉为行的开头&#xff0c;第二次获取为行的末尾&#xff0c;因此最终根数为4&#43;3&#61;7</td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>本题我第一个思路是通过分支递归&#43;缓存优化求解</p> 
<p>但是经过测试&#xff0c;1 ≤ numbers.length ≤ 100000 数量级下&#xff0c;递归操作会StackOverflow&#xff0c;缓存cache数组占用的内存会超出限制。</p> 
<p></p> 
<p>后面思考了一下&#xff0c;无论我们怎么选&#xff0c;左边选择的&#xff0c;以及右边选择的&#xff0c;必然都是连续的&#xff0c;且是从头尾开始的连续&#xff0c;即不可出现下面情况&#xff1a;</p> 
<p><img alt="" height="129" src="https://img-blog.csdnimg.cn/direct/c39774779bc0457e8401ca07f95e205c.png" width="506" /></p> 
<p></p> 
<p>因此&#xff0c;本题其实可以简化为&#xff0c;将n次分解为左边选择的个数&#xff0c;以及右边选择的个数。</p> 
<p>以用例1画图示&#xff1a;</p> 
<p>初始时&#xff0c;假设左边选择了0个&#xff0c;右边选择了n&#61;3个&#xff0c;&#xff08;黄色部分代表选择&#xff09;&#xff1a;</p> 
<p><img alt="" height="198" src="https://img-blog.csdnimg.cn/direct/5d432a33b763476fb5e02cbf95cd5aba.png" width="492" /></p> 
<p>之后&#xff0c;左边选择1个&#xff0c;右边选择2个</p> 
<p><img alt="" height="198" src="https://img-blog.csdnimg.cn/direct/1092ca4fe466424297192c61d64526e4.png" width="493" /></p> 
<p>之后&#xff0c;左边选择2&#xff0c;右边选择1个</p> 
<p><img alt="" height="198" src="https://img-blog.csdnimg.cn/direct/1247c805fcd54766b0af057926c98e52.png" width="493" /></p> 
<p>最后&#xff0c;左边选择3个&#xff0c;右边选择0个</p> 
<p><img alt="" height="198" src="https://img-blog.csdnimg.cn/direct/77b91e71cec5422392f3cb96a54000cf.png" width="492" /></p> 
<p></p> 
<p>上面图示逻辑&#xff0c;我们除了需要计算初始时的leftSum和rightSum外&#xff0c;之后的状态均可以基于前一个状态求解&#xff0c;如下图所示</p> 
<p><img alt="" height="320" src="https://img-blog.csdnimg.cn/direct/d2d8ea4f000c4edd96b80163a10a9278.png" width="777" /></p> 
<p>更多细节请看下面代码和注释。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JS算法源码</h4> 
<pre><code class="language-javascript">const rl &#61; require(&#34;readline&#34;).createInterface({ input: process.stdin });
var iter &#61; rl[Symbol.asyncIterator]();
const readline &#61; async () &#61;&gt; (await iter.next()).value;

void (async function () {
  const len &#61; parseInt(await readline());
  const nums &#61; (await readline()).split(&#34; &#34;).map(Number);
  const n &#61; parseInt(await readline());

  console.log(getResult(len, nums, n));
})();

function getResult(len, nums, n) {
  // 初始时&#xff0c;左边选择0个&#xff0c;因此左边选择的香蕉数为 0
  let leftSum &#61; 0;

  // 初始时&#xff0c;右边选择n个&#xff0c;因此右边选择的香蕉数为 nums[len-n] ~ nums[len - 1] 这个n个元素之和
  let rightSum &#61; 0;
  for (let i &#61; len - n; i &lt; len; i&#43;&#43;) {
    rightSum &#43;&#61; nums[i];
  }

  // 如果选择数n &#61;&#61; len&#xff0c;即全选&#xff0c;此时直接返回初始rightSum
  if (len &#61;&#61; n) {
    return rightSum;
  }

  // 如果不是全选
  // sum记录当前选择结果
  let sum &#61; leftSum &#43; rightSum;
  // ans记录所有选择结果中最大的
  let ans &#61; sum;

  // l指向左边将要获得的&#xff0c;即左边获得一个
  let l &#61; 0;
  // r指向右边将要失去的&#xff0c;即右边失去一个
  let r &#61; len - n;

  while (l &lt; n) {
    sum &#43;&#61; nums[l&#43;&#43;] - nums[r&#43;&#43;];
    ans &#61; Math.max(ans, sum);
  }

  return ans;
}
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.Arrays;
import java.util.Scanner;

public class Main {

  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    int len &#61; Integer.parseInt(sc.nextLine());
    int[] nums &#61; Arrays.stream(sc.nextLine().split(&#34; &#34;)).mapToInt(Integer::parseInt).toArray();
    int n &#61; Integer.parseInt(sc.nextLine());

    System.out.println(getResult(len, nums, n));
  }

  public static int getResult(int len, int[] nums, int n) {
    // 初始时&#xff0c;左边选择0个&#xff0c;因此左边选择的香蕉数为 0
    int leftSum &#61; 0;

    // 初始时&#xff0c;右边选择n个&#xff0c;因此右边选择的香蕉数为 nums[len-n] ~ nums[len - 1] 这个n个元素之和
    int rightSum &#61; 0;
    for (int i &#61; len - n; i &lt; len; i&#43;&#43;) {
      rightSum &#43;&#61; nums[i];
    }

    // 如果选择数n &#61;&#61; len&#xff0c;即全选&#xff0c;此时直接返回初始rightSum
    if (len &#61;&#61; n) {
      return rightSum;
    }

    // 如果不是全选
    // sum记录当前选择结果
    int sum &#61; leftSum &#43; rightSum;
    // ans记录所有选择结果中最大的
    int ans &#61; sum;

    // l指向左边将要获得的&#xff0c;即左边获得一个
    int l &#61; 0;
    // r指向右边将要失去的&#xff0c;即右边失去一个
    int r &#61; len - n;

    while (l &lt; n) {
      sum &#43;&#61; nums[l&#43;&#43;] - nums[r&#43;&#43;];
      ans &#61; Math.max(ans, sum);
    }

    return ans;
  }
}
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
length &#61; int(input())
nums &#61; list(map(int, input().split()))
n &#61; int(input())


# 算法入口
def getResult():
    # 初始时&#xff0c;左边选择0个&#xff0c;因此左边选择的香蕉数为 0
    leftSum &#61; 0
    # 初始时&#xff0c;右边选择n个&#xff0c;因此右边选择的香蕉数为 nums[len-n] ~ nums[len - 1] 这个n个元素之和
    rightSum &#61; sum(nums[length - n:])

    # 如果选择数n &#61;&#61; len&#xff0c;即全选&#xff0c;此时直接返回初始rightSum
    if length &#61;&#61; n:
        return rightSum

    # 如果不是全选
    # sum记录当前选择结果
    sumV &#61; leftSum &#43; rightSum
    # ans记录所有选择结果中最大的
    ans &#61; sumV

    # l指向左边将要获得的&#xff0c;即左边获得一个
    l &#61; 0
    # r指向右边将要失去的&#xff0c;即右边失去一个
    r &#61; length - n

    while l &lt; n:
        sumV &#43;&#61; nums[l] - nums[r]
        ans &#61; max(ans, sumV)
        l &#43;&#61; 1
        r &#43;&#61; 1

    return ans


# 算法调用
print(getResult())
</code></pre> 
<p></p> 
<h4>C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;

#define MAX(a, b) ((a) &gt; (b) ? (a) : (b))

int getResult(int nums_len, const int nums[], int n) {
    // 初始时&#xff0c;左边选择0个&#xff0c;因此左边选择的香蕉数为 0
    int leftSum &#61; 0;

    // 初始时&#xff0c;右边选择n个&#xff0c;因此右边选择的香蕉数为 nums[len-n] ~ nums[len - 1] 这个n个元素之和
    int rightSum &#61; 0;
    for (int i &#61; nums_len - n; i &lt; nums_len; i&#43;&#43;) {
        rightSum &#43;&#61; nums[i];
    }

    // 如果选择数n &#61;&#61; len&#xff0c;即全选&#xff0c;此时直接返回初始rightSum
    if (nums_len &#61;&#61; n) {
        return rightSum;
    }

    // 如果不是全选
    // sum记录当前选择结果
    int sum &#61; leftSum &#43; rightSum;
    // ans记录所有选择结果中最大的
    int ans &#61; sum;

    // l指向左边将要获得的&#xff0c;即左边获得一个
    int l &#61; 0;
    // r指向右边将要失去的&#xff0c;即右边失去一个
    int r &#61; nums_len - n;

    while (l &lt; n) {
        sum &#43;&#61; nums[l&#43;&#43;] - nums[r&#43;&#43;];
        ans &#61; MAX(ans, sum);
    }

    return ans;
}

int main() {
    int nums_len;
    scanf(&#34;%d&#34;, &amp;nums_len);

    int nums[nums_len];
    for (int i &#61; 0; i &lt; nums_len; i&#43;&#43;) {
        scanf(&#34;%d&#34;, &amp;nums[i]);
    }

    int n;
    scanf(&#34;%d&#34;, &amp;n);

    printf(&#34;%d\n&#34;, getResult(nums_len, nums, n));

    return 0;
}</code></pre> 
<p></p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>