<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,200分)- 亲子游戏（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4>在线OJ刷题</h4> 
<p><a href="https://hydro.ac/d/HWOD2023/p/OD402" rel="nofollow" title="题目详情 - 亲子游戏 - Hydro">题目详情 - 亲子游戏 - Hydro</a></p> 
<p></p> 
<h4 id="main-toc">题目描述</h4> 
<p>宝宝和妈妈参加亲子游戏&#xff0c;在一个二维矩阵&#xff08;N*N&#xff09;的格子地图上&#xff0c;宝宝和妈妈抽签决定各自的位置&#xff0c;地图上每个格子有不同的糖果数量&#xff0c;部分格子有障碍物。</p> 
<p>游戏规则是妈妈必须在最短的时间&#xff08;每个单位时间只能走一步&#xff09;到达宝宝的位置&#xff0c;路上的所有糖果都可以拿走&#xff0c;不能走障碍物的格子&#xff0c;只能上下左右走。</p> 
<p>请问妈妈在最短到达宝宝位置的时间内最多拿到多少糖果&#xff08;优先考虑最短时间到达的情况下尽可能多拿糖果&#xff09;。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>第一行输入为 N&#xff0c;N 表示二维矩阵的大小</p> 
<p>之后 N 行&#xff0c;每行有 N 个值&#xff0c;表格矩阵每个位置的值&#xff0c;其中&#xff1a;</p> 
<ul><li>-3&#xff1a;妈妈</li><li>-2&#xff1a;宝宝</li><li>-1&#xff1a;障碍</li><li>≥0&#xff1a;糖果数&#xff08;0表示没有糖果&#xff0c;但是可以走&#xff09;</li></ul> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>输出妈妈在最短到达宝宝位置的时间内最多拿到多少糖果&#xff0c;行末无多余空格</p> 
<p></p> 
<h4>备注</h4> 
<p>地图最大 50*50</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">4<br /> 3 2 1 -3<br /> 1 -1 1 1<br /> 1 1 -1 2<br /> -2 1 2 3</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">9</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;"> <p><img alt="" height="190" src="https://img-blog.csdnimg.cn/direct/d0f7e9da4616475e90eeba5f0e381973.png" width="227" /></p> <p>此地图有两条最短路径可到达宝宝位置&#xff0c;绿色线和黄色线都是最短路径6步&#xff0c;但黄色拿到的糖果更多&#xff0c;9个。</p> </td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">4<br /> 3 2 1 -3<br /> -1 -1 1 1<br /> 1 1 -1 2<br /> -2 1 -1 3</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">-1</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;"> <p><img alt="" height="194" src="https://img-blog.csdnimg.cn/direct/e5d33f0e804246ed84d3798b4f4070d9.png" width="274" /></p> <p>此地图妈妈无法到达宝宝位置</p> </td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>本题需要我们优先找到妈妈到宝宝的最短路径&#xff0c;如果存在多条最短路径的话&#xff0c;则选择其中能拿到最多糖果数的路径。</p> 
<p></p> 
<p>那么如何求解妈妈到宝宝的最短路径呢&#xff1f;</p> 
<p>其实很简单&#xff0c;就是单纯的BFS按层扩散&#xff0c;比如下图所示&#xff1a;</p> 
<p>此时将妈妈位置作为源点&#xff0c;开始按层扩散</p> 
<p><img alt="" height="264" src="https://img-blog.csdnimg.cn/direct/80ed14257ec54b92bbaeb3aafe58021d.png" width="294" /></p> 
<p>扩散到第一层</p> 
<p><img alt="" height="262" src="https://img-blog.csdnimg.cn/direct/8a5531c948d541a78a01375918357830.png" width="294" /></p> 
<p>扩散第二层</p> 
<p><img alt="" height="261" src="https://img-blog.csdnimg.cn/direct/f9fe82a98a0c407484f78d09bad59cb4.png" width="293" /></p> 
<p>扩散到第三层</p> 
<p><img alt="" height="260" src="https://img-blog.csdnimg.cn/direct/fc4771e9ff794342ac96453945609978.png" width="293" /></p> 
<p>扩散到第四层</p> 
<p><img alt="" height="260" src="https://img-blog.csdnimg.cn/direct/be65e3580a8e4e5ba77a317bf34da7fb.png" width="291" /></p> 
<p>扩散到第五层&#xff0c;此时扩散到了宝宝位置&#xff0c;也就是说妈妈到宝宝位置的最短距离是五步。</p> 
<p><img alt="" height="261" src="https://img-blog.csdnimg.cn/direct/874d7b225c1b406abdb61f0320c46259.png" width="290" /></p> 
<p>即扩散的层数&#xff0c;就是步数距离。</p> 
<p></p> 
<p>那么如何在扩散过程中&#xff0c;统计宝宝能获得的糖果数呢&#xff1f;</p> 
<p>此时我们可以定义一个糖果矩阵&#xff0c;初始时糖果矩阵都为0</p> 
<p><img alt="" height="266" src="https://img-blog.csdnimg.cn/direct/b7992fd93e2b4c27add4d60289e43dd1.png" width="673" /></p> 
<p></p> 
<p>扩散到第一层</p> 
<p><img alt="" height="270" src="https://img-blog.csdnimg.cn/direct/b99e809968c345d79d452a90a9b74f44.png" width="679" /></p> 
<p>此时第一层上各点拿到的糖果数 &#61; 扩散源点的糖果数 &#43; 自身位置已有的糖果数</p> 
<p></p> 
<p>扩散到第二层</p> 
<p><img alt="" height="309" src="https://img-blog.csdnimg.cn/direct/af2d681891cc4c568612875ae468c56d.png" width="650" /></p> 
<p>此时&#xff0c;我们发现&#xff0c;绿色框的点对应的糖果数很容易求解 &#61; 扩散源点的糖果数 &#43; 自身位置已有的糖果数。</p> 
<p>但是红色框点&#xff0c;他可以被两个扩散源点同时扩散到&#xff0c;此时我们应该保留能带来较大糖果数的扩散源点的扩散结果</p> 
<p><img alt="" height="260" src="https://img-blog.csdnimg.cn/direct/0e8d733f8436410587792ad751d1b735.png" width="641" /></p> 
<p></p> 
<p>扩散到第三层</p> 
<p><img alt="" height="299" src="https://img-blog.csdnimg.cn/direct/b25881e758194e71a85d5c8c49ed393a.png" width="657" /></p> 
<p></p> 
<p>扩散到第四层</p> 
<p><img alt="" height="285" src="https://img-blog.csdnimg.cn/direct/848dbdb8359e4981b31adfdc872cc53d.png" width="663" /></p> 
<p></p> 
<p>扩散到第五层时&#xff0c;宝宝位置只会被一个源点扩散到&#xff0c;即下图红框点&#xff0c;即此时宝宝在最短距离路径下&#xff0c;最多可获得24个糖果</p> 
<p><img alt="" height="306" src="https://img-blog.csdnimg.cn/direct/e17ba57a55614227850b6bfdca3caa4b.png" width="694" /></p> 
<p></p> 
<p>并且&#xff0c;一旦BFS扩散层到达了宝宝所在位置&#xff0c;则等待此层扩散完&#xff0c;就可以停止BFS逻辑。</p> 
<p>因为&#xff0c;继续扩散到后续层&#xff0c;虽然也可能到达宝宝位置&#xff0c;但是路径已经不是最短的了。</p> 
<p></p> 
<p>上面逻辑实现时&#xff0c;比较难的是&#xff1a;如何实现按层扩散&#xff1f;</p> 
<p>此时我们可以借助candy矩阵&#xff0c;我们可以发现在上面扩散过程中&#xff0c;新的层的candy值都为0&#xff0c;因此当一个源点开始扩散&#xff0c;扩散到的新位置的candy值&#xff1a;</p> 
<ul><li>如果candy值为0的话&#xff0c;则说明当前点是新层的点&#xff0c;此时我们将该点加入到新层的BFS队列中</li><li>如果candy值不为0&#xff0c;则说明该点可能是新层&#xff0c;也可能是老层&#xff0c;此时我们只做更新该点candy值动作&#xff08;可能会改掉老层点的candy值&#xff0c;但是不会有影响&#xff0c;因为一层套一层&#xff0c;老层点的candy值改变不会影响隔了一层得下一层点&#xff09;</li></ul> 
<blockquote> 
 <p>注意&#xff1a;本题输入矩阵中得各点糖果数可能为0&#xff0c;因此这里不能根据扩散点的candy值为0来确定当前扩散点是不是处于新层。</p> 
 <p></p> 
 <p>我们应该将candy矩阵各元素初始化为-1&#xff0c;然后扩散点根据candy值是否为-1&#xff0c;来判断是否处于新层</p> 
</blockquote> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JS算法源码</h4> 
<pre><code class="language-javascript">const rl &#61; require(&#34;readline&#34;).createInterface({ input: process.stdin });
var iter &#61; rl[Symbol.asyncIterator]();
const readline &#61; async () &#61;&gt; (await iter.next()).value;

void (async function () {
  const n &#61; parseInt(await readline());

  const candy &#61; new Array(n).fill(0).map(() &#61;&gt; new Array(n).fill(-1));

  let queue &#61; [];

  const matrix &#61; [];
  for (let i &#61; 0; i &lt; n; i&#43;&#43;) {
    matrix.push((await readline()).split(&#34; &#34;).map(Number));

    for (let j &#61; 0; j &lt; n; j&#43;&#43;) {
      // 妈妈的位置
      if (matrix[i][j] &#61;&#61; -3) {
        candy[i][j] &#61; 0;
        queue.push([i, j]);
      }
    }
  }

  // 记录题解
  let ans &#61; -1;

  const offsets &#61; [
    [-1, 0],
    [1, 0],
    [0, -1],
    [0, 1],
  ];

  // bfs 按层扩散
  while (queue.length &gt; 0) {
    // 记录当前扩散层的点
    const newQueue &#61; [];

    // 当前层是否有宝宝所在的点
    let flag &#61; false;

    // 源点坐标
    for (let [x, y] of queue) {
      // 向四个方向扩散
      for (let [offsetX, offsetY] of offsets) {
        // 当前扩散点坐标
        const newX &#61; x &#43; offsetX;
        const newY &#61; y &#43; offsetY;

        // 当前扩散点坐标越界&#xff0c;或者扩散点是墙&#xff0c;则无法扩散
        if (
          newX &lt; 0 ||
          newX &gt;&#61; n ||
          newY &lt; 0 ||
          newY &gt;&#61; n ||
          matrix[newX][newY] &#61;&#61; -1
        )
          continue;

        // 当前扩散点坐标对应的糖果数量为-1&#xff0c;说明对应扩散点坐标位置还没有加入到当前扩散层
        if (candy[newX][newY] &#61;&#61; -1) {
          newQueue.push([newX, newY]); // 加入当前扩散层
        }

        // 当前扩散点可能会被多个源点扩散到&#xff0c;因此比较保留扩散过程中带来的较大糖果数
        // candy[newX][newY] 记录的是当前扩散点获得的糖果数
        // candy[x][y] &#43; Math.max(0, matrix[newX][newY]) 记录的是从源点(x,y)带来的糖果数 &#43; (newX,newY)位置原本的糖果数
        candy[newX][newY] &#61; Math.max(
          candy[newX][newY],
          candy[x][y] &#43; Math.max(0, matrix[newX][newY])
        );

        // 如果当前扩散点是宝宝位置&#xff0c;则可以停止后续层级的bfs扩散&#xff0c;因为已经找到宝宝的最短路径长度&#xff08;即扩散层数&#xff09;
        if (matrix[newX][newY] &#61;&#61; -2) {
          ans &#61; candy[newX][newY];
          flag &#61; true;
        }
      }
    }

    // 已经找到去宝宝位置的最短路径和最大糖果数&#xff0c;则终止bfs
    if (flag) break;

    // 否则继续
    queue &#61; newQueue;
  }

  console.log(ans);
})();
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.LinkedList;
import java.util.Scanner;

public class Main {
  static int n;
  static int[][] matrix;
  static int[][] candy;
  static int[][] offsets &#61; {<!-- -->{1, 0}, {0, -1}, {-1, 0}, {0, 1}};

  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    n &#61; sc.nextInt();

    matrix &#61; new int[n][n];
    candy &#61; new int[n][n];

    LinkedList&lt;Integer&gt; queue &#61; new LinkedList&lt;&gt;();

    for (int i &#61; 0; i &lt; n; i&#43;&#43;) {
      for (int j &#61; 0; j &lt; n; j&#43;&#43;) {
        candy[i][j] &#61; -1;

        matrix[i][j] &#61; sc.nextInt();
        // 妈妈的位置
        if (matrix[i][j] &#61;&#61; -3) {
          candy[i][j] &#61; 0;
          queue.add(i * n &#43; j); // 二维坐标一维化
        }
      }
    }

    // 记录题解
    int ans &#61; -1;

    // bfs 按层扩散
    while (queue.size() &gt; 0) {
      // 记录当前扩散层的点
      LinkedList&lt;Integer&gt; newQueue &#61; new LinkedList&lt;&gt;();

      // 当前层是否有宝宝所在的点
      boolean flag &#61; false;

      for (int pos : queue) {
        // 源点坐标
        int x &#61; pos / n;
        int y &#61; pos % n;

        // 向四个方向扩散
        for (int[] offset : offsets) {
          // 当前扩散点坐标
          int newX &#61; x &#43; offset[0];
          int newY &#61; y &#43; offset[1];

          // 当前扩散点坐标越界&#xff0c;或者扩散点是墙&#xff0c;则无法扩散
          if (newX &lt; 0 || newX &gt;&#61; n || newY &lt; 0 || newY &gt;&#61; n || matrix[newX][newY] &#61;&#61; -1) continue;

          // 当前扩散点坐标对应的糖果数量为-1&#xff0c;说明对应扩散点坐标位置还没有加入到当前扩散层
          if (candy[newX][newY] &#61;&#61; -1) {
            newQueue.addLast(newX * n &#43; newY); // 加入当前扩散层
          }

          // 当前扩散点可能会被多个源点扩散到&#xff0c;因此比较保留扩散过程中带来的较大糖果数
          // candy[newX][newY] 记录的是当前扩散点获得的糖果数
          // candy[x][y] &#43; Math.max(0, matrix[newX][newY]) 记录的是从源点(x,y)带来的糖果数 &#43; (newX,newY)位置原本的糖果数
          candy[newX][newY] &#61;
              Math.max(candy[newX][newY], candy[x][y] &#43; Math.max(0, matrix[newX][newY]));

          // 如果当前扩散点是宝宝位置&#xff0c;则可以停止后续层级的bfs扩散&#xff0c;因为已经找到宝宝的最短路径长度&#xff08;即扩散层数&#xff09;
          if (matrix[newX][newY] &#61;&#61; -2) {
            ans &#61; candy[newX][newY];
            flag &#61; true;
          }
        }
      }

      // 已经找到去宝宝位置的最短路径和最大糖果数&#xff0c;则终止bfs
      if (flag) break;

      // 否则继续
      queue &#61; newQueue;
    }

    System.out.println(ans);
  }
}
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
n &#61; int(input())

queue &#61; []

candy &#61; [[-1] * n for _ in range(n)]

matrix &#61; []
for i in range(n):
    matrix.append(list(map(int, input().split())))

    for j in range(n):
        # 妈妈的位置
        if matrix[i][j] &#61;&#61; -3:
            candy[i][j] &#61; 0
            queue.append((i, j))

offsets &#61; ((0, -1), (0, 1), (-1, 0), (1, 0))


# 算法入口
def bfs():
    global queue

    # 记录题解
    ans &#61; -1

    # bfs 按层扩散
    while len(queue) &gt; 0:
        # 记录当前扩散层的点
        newQueue &#61; []

        # 当前层是否有宝宝所在的点
        flag &#61; False

        # 源点坐标
        for x, y in queue:
            # 向四个方向扩散
            for offsetX, offsetY in offsets:
                # 当前扩散点坐标
                newX &#61; x &#43; offsetX
                newY &#61; y &#43; offsetY

                # 当前扩散点坐标越界&#xff0c;或者扩散点是墙&#xff0c;则无法扩散
                if newX &lt; 0 or newX &gt;&#61; n or newY &lt; 0 or newY &gt;&#61; n or matrix[newX][newY] &#61;&#61; -1:
                    continue

                # 当前扩散点坐标对应的糖果数量为-1&#xff0c;说明对应扩散点坐标位置还没有加入到当前扩散层
                if candy[newX][newY] &#61;&#61; -1:
                    newQueue.append((newX, newY))  # 加入当前扩散层

                # 当前扩散点可能会被多个源点扩散到&#xff0c;因此比较保留扩散过程中带来的较大糖果数
                # candy[newX][newY] 记录的是当前扩散点获得的糖果数
                # candy[x][y] &#43; max(0, matrix[newX][newY]) 记录的是从源点(x,y)带来的糖果数 &#43; (newX,newY)位置原本的糖果数
                candy[newX][newY] &#61; max(candy[newX][newY], candy[x][y] &#43; max(0, matrix[newX][newY]))

                # 如果当前扩散点是宝宝位置&#xff0c;则可以停止后续层级的bfs扩散&#xff0c;因为已经找到宝宝的最短路径长度&#xff08;即扩散层数&#xff09;
                if matrix[newX][newY] &#61;&#61; -2:
                    ans &#61; candy[newX][newY]
                    flag &#61; True

        # 已经找到去宝宝位置的最短路径和最大糖果数&#xff0c;则终止bfs
        if flag:
            break

        # 否则继续
        queue &#61; newQueue

    return ans


# 算法调用
print(bfs())
</code></pre> 
<p></p> 
<h4>C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;

#define MAX(a, b) ((a) &gt; (b) ? (a) : (b))

typedef struct ListNode {
    int ele;
    struct ListNode *next;
} ListNode;

typedef struct LinkedList {
    int size;
    ListNode *head;
    ListNode *tail;
} LinkedList;

LinkedList *new_LinkedList() {
    LinkedList *link &#61; (LinkedList *) malloc(sizeof(LinkedList));
    link-&gt;size &#61; 0;
    link-&gt;head &#61; NULL;
    link-&gt;tail &#61; NULL;
    return link;
}

void addLast_LinkedList(LinkedList *link, int ele) {
    ListNode *node &#61; (ListNode *) malloc(sizeof(ListNode));
    node-&gt;ele &#61; ele;
    node-&gt;next &#61; NULL;

    if (link-&gt;size &#61;&#61; 0) {
        link-&gt;head &#61; node;
        link-&gt;tail &#61; node;
    } else {
        link-&gt;tail-&gt;next &#61; node;
        link-&gt;tail &#61; node;
    }

    link-&gt;size&#43;&#43;;
}

int main() {
    int n;
    scanf(&#34;%d&#34;, &amp;n);

    int matrix[n][n];
    int candy[n][n];

    LinkedList *queue &#61; new_LinkedList();

    for (int i &#61; 0; i &lt; n; i&#43;&#43;) {
        for (int j &#61; 0; j &lt; n; j&#43;&#43;) {
            candy[i][j] &#61; -1;

            scanf(&#34;%d&#34;, &amp;matrix[i][j]);
            // 妈妈的位置
            if (matrix[i][j] &#61;&#61; -3) {
                candy[i][j] &#61; 0;
                addLast_LinkedList(queue, i * n &#43; j); // 二维坐标一维化
            }
        }
    }

    int offsets[4][2] &#61; {<!-- -->{-1, 0},
                         {1,  0},
                         {0,  -1},
                         {0,  1}};

    // 记录题解
    int ans &#61; -1;

    // bfs 按层扩散
    while (queue-&gt;size &gt; 0) {
        // 记录当前扩散层的点
        LinkedList *newQueue &#61; new_LinkedList();

        // 当前层是否有宝宝所在的点
        int flag &#61; 0;

        ListNode *cur &#61; queue-&gt;head;
        while (cur !&#61; NULL) {
            // 源点坐标
            int x &#61; cur-&gt;ele / n;
            int y &#61; cur-&gt;ele % n;

            // 向四个方向扩散
            for (int i &#61; 0; i &lt; 4; i&#43;&#43;) {
                // 当前扩散点坐标
                int newX &#61; x &#43; offsets[i][0];
                int newY &#61; y &#43; offsets[i][1];

                // 当前扩散点坐标越界&#xff0c;或者扩散点是墙&#xff0c;则无法扩散
                if (newX &lt; 0 || newX &gt;&#61; n || newY &lt; 0 || newY &gt;&#61; n || matrix[newX][newY] &#61;&#61; -1) continue;

                // 当前扩散点坐标对应的糖果数量为-1&#xff0c;说明对应扩散点坐标位置还没有加入到当前扩散层
                if (candy[newX][newY] &#61;&#61; -1) {
                    addLast_LinkedList(newQueue, newX * n &#43; newY);
                }

                // 当前扩散点可能会被多个源点扩散到&#xff0c;因此比较保留扩散过程中带来的较大糖果数
                // candy[newX][newY] 记录的是当前扩散点获得的糖果数
                // candy[x][y] &#43; remain 记录的是从源点(x,y)带来的糖果数 &#43; (newX,newY)位置原本的糖果数
                int remain &#61; MAX(0, matrix[newX][newY]);
                candy[newX][newY] &#61; MAX(candy[newX][newY], candy[x][y] &#43; remain);

                // 如果当前扩散点是宝宝位置&#xff0c;则可以停止后续层级的bfs扩散&#xff0c;因为已经找到宝宝的最短路径长度&#xff08;即扩散层数&#xff09;
                if (matrix[newX][newY] &#61;&#61; -2) {
                    ans &#61; candy[newX][newY];
                    flag &#61; 1;
                }
            }

            cur &#61; cur-&gt;next;
        }

        // 已经找到去宝宝位置的最短路径和最大糖果数&#xff0c;则终止bfs
        if (flag) break;

        // 否则继续
        queue &#61; newQueue;
    }

    printf(&#34;%d\n&#34;, ans);

    return 0;
}</code></pre> 
<p></p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>