<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(B卷,200分)- 书籍叠放（Java & JS & Python）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>书籍的长、宽都是整数对应 (l,w)。如果书A的长宽度都比B长宽大时&#xff0c;则允许将B排列放在A上面。现在有一组规格的书籍&#xff0c;书籍叠放时要求书籍不能做旋转&#xff0c;请计算最多能有多少个规格书籍能叠放在一起。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>输入&#xff1a;books &#61; [[20,16],[15,11],[10,10],[9,10]]</p> 
<p>说明&#xff1a;总共4本书籍&#xff0c;第一本长度为20宽度为16&#xff1b;第二本书长度为15宽度为11&#xff0c;依次类推&#xff0c;最后一本书长度为9宽度为10.</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>输出&#xff1a;3</p> 
<p>说明: 最多3个规格的书籍可以叠放到一起, 从下到上依次为: [20,16],[15,11],[10,10]</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">[[20,16],[15,11],[10,10],[9,10]]</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">3</td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>本题就是<a href="https://blog.csdn.net/qfc_128220/article/details/127941451?csdn_share_tail&#61;%7B%22type%22%3A%22blog%22%2C%22rType%22%3A%22article%22%2C%22rId%22%3A%22127941451%22%2C%22source%22%3A%22qfc_128220%22%7D" title="LeetCode - 354 俄罗斯套娃信封问题_伏城之外的博客-CSDN博客">LeetCode - 354 俄罗斯套娃信封问题_伏城之外的博客-CSDN博客</a></p> 
<p>可以采用耐心排序&#43;二分查找&#xff0c;实现O(nlgn)时间复杂度的算法。</p> 
<p>具体解题思路请看上面博客。</p> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.*;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    String input &#61; sc.nextLine();

    // (?&lt;&#61;]),(?&#61;\[) 正则表达式含义是&#xff1a;找这样一个逗号&#xff0c;前面跟着]&#xff0c;后面跟着[
    // 其中(?&lt;&#61;) 表示前面跟着
    // 其中(?&#61;) 表示后面跟着
    Integer[][] books &#61;
        Arrays.stream(input.substring(1, input.length() - 1).split(&#34;(?&lt;&#61;]),(?&#61;\\[)&#34;))
            .map(
                s -&gt;
                    Arrays.stream(s.substring(1, s.length() - 1).split(&#34;,&#34;))
                        .map(Integer::parseInt)
                        .toArray(Integer[]::new))
            .toArray(Integer[][]::new);

    System.out.println(getResult(books));
  }

  public static int getResult(Integer[][] books) {
    // 长度升序&#xff0c;若长度相同&#xff0c;则宽度降序
    Arrays.sort(books, (a, b) -&gt; Objects.equals(a[0], b[0]) ? b[1] - a[1] : a[0] - b[0]);
    Integer[] widths &#61; Arrays.stream(books).map(book -&gt; book[1]).toArray(Integer[]::new);
    return getMaxLIS(widths);
  }

  // 最长递增子序列
  public static int getMaxLIS(Integer[] nums) {
    //  dp数组元素dp[i]含义是&#xff1a;长度为i&#43;1的最优子序列的尾数
    ArrayList&lt;Integer&gt; dp &#61; new ArrayList&lt;&gt;();
    dp.add(nums[0]);

    for (int i &#61; 1; i &lt; nums.length; i&#43;&#43;) {
      if (nums[i] &gt; dp.get(dp.size() - 1)) {
        dp.add(nums[i]);
        continue;
      }

      if (nums[i] &lt; dp.get(0)) {
        dp.set(0, nums[i]);
        continue;
      }

      int idx &#61; Collections.binarySearch(dp, nums[i]);
      if (idx &lt; 0) dp.set(-idx - 1, nums[i]);
    }

    return dp.size();
  }
}
</code></pre> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.on(&#34;line&#34;, (line) &#61;&gt; {
  const books &#61; JSON.parse(line);
  console.log(getMaxStackCount(books));
});

function getMaxStackCount(books) {
  // 长度升序&#xff0c;若长度相同&#xff0c;则宽度降序
  const widths &#61; books
    .sort((a, b) &#61;&gt; (a[0] &#61;&#61;&#61; b[0] ? b[1] - a[1] : a[0] - b[0]))
    .map((book) &#61;&gt; book[1]);

  return getMaxLIS(widths);
}

// 最长递增子序列
function getMaxLIS(nums) {
  // dp数组元素dp[i]含义是&#xff1a;长度为i&#43;1的最优子序列的尾数
  const dp &#61; [nums[0]];

  for (let i &#61; 1; i &lt; nums.length; i&#43;&#43;) {
    if (nums[i] &gt; dp[dp.length - 1]) {
      dp.push(nums[i]);
      continue;
    }

    if (nums[i] &lt; dp[0]) {
      dp[0] &#61; nums[i];
      continue;
    }

    const idx &#61; binarySearch(dp, nums[i]);
    if (idx &lt; 0) dp[-idx - 1] &#61; nums[i];
  }

  return dp.length;
}

// 二分查找
function binarySearch(arr, key) {
  let low &#61; 0;
  let high &#61; arr.length - 1;

  while (low &lt;&#61; high) {
    let mid &#61; (low &#43; high) &gt;&gt;&gt; 1;
    let midVal &#61; arr[mid];

    if (key &gt; midVal) {
      low &#61; mid &#43; 1;
    } else if (key &lt; midVal) {
      high &#61; mid - 1;
    } else {
      return mid;
    }
  }
  return -(low &#43; 1);
}
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
books &#61; eval(input())


# 二分查找
def binarySearch(arr, key):
    low &#61; 0
    high &#61; len(arr) - 1

    while low &lt;&#61; high:
        mid &#61; (low &#43; high) &gt;&gt; 1
        midVal &#61; arr[mid]

        if key &gt; midVal:
            low &#61; mid &#43; 1
        elif key &lt; midVal:
            high &#61; mid - 1
        else:
            return mid

    return -(low &#43; 1)


# 最长递增子序列
def getMaxLIS(nums):
    # dp数组元素dp[i]含义是&#xff1a;长度为i&#43;1的最优子序列的尾数
    dp &#61; [nums[0]]

    for i in range(1, len(nums)):
        if nums[i] &gt; dp[-1]:
            dp.append(nums[i])
            continue

        if nums[i] &lt; dp[0]:
            dp[0] &#61; nums[i]
            continue

        idx &#61; binarySearch(dp, nums[i])
        if idx &lt; 0:
            dp[-idx - 1] &#61; nums[i]

    return len(dp)


# 算法入口
def getResult(books):
    # 长度升序&#xff0c;若长度相同&#xff0c;则宽度降序
    books.sort(key&#61;lambda x: (x[0], -x[1]))
    widths &#61; list(map(lambda x: x[1], books))
    return getMaxLIS(widths)


# 算法调用
print(getResult(books))
</code></pre> 
<p> </p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>