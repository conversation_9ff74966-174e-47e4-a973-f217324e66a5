<!doctype html><html><head><meta charset='UTF-8'><meta name='viewport' content='width=device-width initial-scale=1'><link href='https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext' rel='stylesheet' type='text/css' /><style type='text/css'>html {overflow-x: initial !important;}:root { --bg-color: #ffffff; --text-color: #333333; --select-text-bg-color: #B5D6FC; --select-text-font-color: auto; --monospace: "Lucida Console",Consolas,"Courier",monospace; --title-bar-height: 20px; }.mac-os-11 { --title-bar-height: 28px; }html { font-size: 14px; background-color: var(--bg-color); color: var(--text-color); font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; -webkit-font-smoothing: antialiased; }h1, h2, h3, h4, h5 { white-space: pre-wrap; }body { margin: 0px; padding: 0px; height: auto; inset: 0px; font-size: 1rem; line-height: 1.42857; overflow-x: hidden; background: inherit; }iframe { margin: auto; }a.url { word-break: break-all; }a:active, a:hover { outline: 0px; }.in-text-selection, ::selection { text-shadow: none; background: var(--select-text-bg-color); color: var(--select-text-font-color); }#write { margin: 0px auto; height: auto; width: inherit; word-break: normal; overflow-wrap: break-word; position: relative; white-space: normal; overflow-x: visible; padding-top: 36px; }#write.first-line-indent p { text-indent: 2em; }#write.first-line-indent li p, #write.first-line-indent p * { text-indent: 0px; }#write.first-line-indent li { margin-left: 2em; }.for-image #write { padding-left: 8px; padding-right: 8px; }body.typora-export { padding-left: 30px; padding-right: 30px; }.typora-export .footnote-line, .typora-export li, .typora-export p { white-space: pre-wrap; }.typora-export .task-list-item input { pointer-events: none; }@media screen and (max-width: 500px) {  body.typora-export { padding-left: 0px; padding-right: 0px; }  #write { padding-left: 20px; padding-right: 20px; }}#write li > figure:last-child { margin-bottom: 0.5rem; }#write ol, #write ul { position: relative; }img { max-width: 100%; vertical-align: middle; image-orientation: from-image; }button, input, select, textarea { color: inherit; font: inherit; }input[type="checkbox"], input[type="radio"] { line-height: normal; padding: 0px; }*, ::after, ::before { box-sizing: border-box; }#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p, #write pre { width: inherit; }#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p { position: relative; }p { line-height: inherit; }h1, h2, h3, h4, h5, h6 { break-after: avoid-page; break-inside: avoid; orphans: 4; }p { orphans: 4; }h1 { font-size: 2rem; }h2 { font-size: 1.8rem; }h3 { font-size: 1.6rem; }h4 { font-size: 1.4rem; }h5 { font-size: 1.2rem; }h6 { font-size: 1rem; }.md-math-block, .md-rawblock, h1, h2, h3, h4, h5, h6, p { margin-top: 1rem; margin-bottom: 1rem; }.hidden { display: none; }.md-blockmeta { color: rgb(204, 204, 204); font-weight: 700; font-style: italic; }a { cursor: pointer; }sup.md-footnote { padding: 2px 4px; background-color: rgba(238, 238, 238, 0.7); color: rgb(85, 85, 85); border-radius: 4px; cursor: pointer; }sup.md-footnote a, sup.md-footnote a:hover { color: inherit; text-transform: inherit; text-decoration: inherit; }#write input[type="checkbox"] { cursor: pointer; width: inherit; height: inherit; }figure { overflow-x: auto; margin: 1.2em 0px; max-width: calc(100% + 16px); padding: 0px; }figure > table { margin: 0px; }thead, tr { break-inside: avoid; break-after: auto; }thead { display: table-header-group; }table { border-collapse: collapse; border-spacing: 0px; width: 100%; overflow: auto; break-inside: auto; text-align: left; }table.md-table td { min-width: 32px; }.CodeMirror-gutters { border-right: 0px; background-color: inherit; }.CodeMirror-linenumber { user-select: none; }.CodeMirror { text-align: left; }.CodeMirror-placeholder { opacity: 0.3; }.CodeMirror pre { padding: 0px 4px; }.CodeMirror-lines { padding: 0px; }div.hr:focus { cursor: none; }#write pre { white-space: pre-wrap; }#write.fences-no-line-wrapping pre { white-space: pre; }#write pre.ty-contain-cm { white-space: normal; }.CodeMirror-gutters { margin-right: 4px; }.md-fences { font-size: 0.9rem; display: block; break-inside: avoid; text-align: left; overflow: visible; white-space: pre; background: inherit; position: relative !important; }.md-fences-adv-panel { width: 100%; margin-top: 10px; text-align: center; padding-top: 0px; padding-bottom: 8px; overflow-x: auto; }#write .md-fences.mock-cm { white-space: pre-wrap; }.md-fences.md-fences-with-lineno { padding-left: 0px; }#write.fences-no-line-wrapping .md-fences.mock-cm { white-space: pre; overflow-x: auto; }.md-fences.mock-cm.md-fences-with-lineno { padding-left: 8px; }.CodeMirror-line, twitterwidget { break-inside: avoid; }svg { break-inside: avoid; }.footnotes { opacity: 0.8; font-size: 0.9rem; margin-top: 1em; margin-bottom: 1em; }.footnotes + .footnotes { margin-top: 0px; }.md-reset { margin: 0px; padding: 0px; border: 0px; outline: 0px; vertical-align: top; background: 0px 0px; text-decoration: none; text-shadow: none; float: none; position: static; width: auto; height: auto; white-space: nowrap; cursor: inherit; -webkit-tap-highlight-color: transparent; line-height: normal; font-weight: 400; text-align: left; box-sizing: content-box; direction: ltr; }li div { padding-top: 0px; }blockquote { margin: 1rem 0px; }li .mathjax-block, li p { margin: 0.5rem 0px; }li blockquote { margin: 1rem 0px; }li { margin: 0px; position: relative; }blockquote > :last-child { margin-bottom: 0px; }blockquote > :first-child, li > :first-child { margin-top: 0px; }.footnotes-area { color: rgb(136, 136, 136); margin-top: 0.714rem; padding-bottom: 0.143rem; white-space: normal; }#write .footnote-line { white-space: pre-wrap; }@media print {  body, html { border: 1px solid transparent; height: 99%; break-after: avoid; break-before: avoid; font-variant-ligatures: no-common-ligatures; }  #write { margin-top: 0px; border-color: transparent !important; padding-top: 0px !important; padding-bottom: 0px !important; }  .typora-export * { -webkit-print-color-adjust: exact; }  .typora-export #write { break-after: avoid; }  .typora-export #write::after { height: 0px; }  .is-mac table { break-inside: avoid; }  #write > p:nth-child(1) { margin-top: 0px; }  .typora-export-show-outline .typora-export-sidebar { display: none; }  figure { overflow-x: visible; }}.footnote-line { margin-top: 0.714em; font-size: 0.7em; }a img, img a { cursor: pointer; }pre.md-meta-block { font-size: 0.8rem; min-height: 0.8rem; white-space: pre-wrap; background: rgb(204, 204, 204); display: block; overflow-x: hidden; }p > .md-image:only-child:not(.md-img-error) img, p > img:only-child { display: block; margin: auto; }#write.first-line-indent p > .md-image:only-child:not(.md-img-error) img { left: -2em; position: relative; }p > .md-image:only-child { display: inline-block; width: 100%; }#write .MathJax_Display { margin: 0.8em 0px 0px; }.md-math-block { width: 100%; }.md-math-block:not(:empty)::after { display: none; }.MathJax_ref { fill: currentcolor; }[contenteditable="true"]:active, [contenteditable="true"]:focus, [contenteditable="false"]:active, [contenteditable="false"]:focus { outline: 0px; box-shadow: none; }.md-task-list-item { position: relative; list-style-type: none; }.task-list-item.md-task-list-item { padding-left: 0px; }.md-task-list-item > input { position: absolute; top: 0px; left: 0px; margin-left: -1.2em; margin-top: calc(1em - 10px); border: none; }.math { font-size: 1rem; }.md-toc { min-height: 3.58rem; position: relative; font-size: 0.9rem; border-radius: 10px; }.md-toc-content { position: relative; margin-left: 0px; }.md-toc-content::after, .md-toc::after { display: none; }.md-toc-item { display: block; color: rgb(65, 131, 196); }.md-toc-item a { text-decoration: none; }.md-toc-inner:hover { text-decoration: underline; }.md-toc-inner { display: inline-block; cursor: pointer; }.md-toc-h1 .md-toc-inner { margin-left: 0px; font-weight: 700; }.md-toc-h2 .md-toc-inner { margin-left: 2em; }.md-toc-h3 .md-toc-inner { margin-left: 4em; }.md-toc-h4 .md-toc-inner { margin-left: 6em; }.md-toc-h5 .md-toc-inner { margin-left: 8em; }.md-toc-h6 .md-toc-inner { margin-left: 10em; }@media screen and (max-width: 48em) {  .md-toc-h3 .md-toc-inner { margin-left: 3.5em; }  .md-toc-h4 .md-toc-inner { margin-left: 5em; }  .md-toc-h5 .md-toc-inner { margin-left: 6.5em; }  .md-toc-h6 .md-toc-inner { margin-left: 8em; }}a.md-toc-inner { font-size: inherit; font-style: inherit; font-weight: inherit; line-height: inherit; }.footnote-line a:not(.reversefootnote) { color: inherit; }.reversefootnote { font-family: ui-monospace, sans-serif; }.md-attr { display: none; }.md-fn-count::after { content: "."; }code, pre, samp, tt { font-family: var(--monospace); }kbd { margin: 0px 0.1em; padding: 0.1em 0.6em; font-size: 0.8em; color: rgb(36, 39, 41); background: rgb(255, 255, 255); border: 1px solid rgb(173, 179, 185); border-radius: 3px; box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px, rgb(255, 255, 255) 0px 0px 0px 2px inset; white-space: nowrap; vertical-align: middle; }.md-comment { color: rgb(162, 127, 3); opacity: 0.6; font-family: var(--monospace); }code { text-align: left; vertical-align: initial; }a.md-print-anchor { white-space: pre !important; border-width: initial !important; border-style: none !important; border-color: initial !important; display: inline-block !important; position: absolute !important; width: 1px !important; right: 0px !important; outline: 0px !important; background: 0px 0px !important; text-decoration: initial !important; text-shadow: initial !important; }.os-windows.monocolor-emoji .md-emoji { font-family: "Segoe UI Symbol", sans-serif; }.md-diagram-panel > svg { max-width: 100%; }[lang="flow"] svg, [lang="mermaid"] svg { max-width: 100%; height: auto; }[lang="mermaid"] .node text { font-size: 1rem; }table tr th { border-bottom: 0px; }video { max-width: 100%; display: block; margin: 0px auto; }iframe { max-width: 100%; width: 100%; border: none; }.highlight td, .highlight tr { border: 0px; }mark { background: rgb(255, 255, 0); color: rgb(0, 0, 0); }.md-html-inline .md-plain, .md-html-inline strong, mark .md-inline-math, mark strong { color: inherit; }.md-expand mark .md-meta { opacity: 0.3 !important; }mark .md-meta { color: rgb(0, 0, 0); }@media print {  .typora-export h1, .typora-export h2, .typora-export h3, .typora-export h4, .typora-export h5, .typora-export h6 { break-inside: avoid; }}.md-diagram-panel .messageText { stroke: none !important; }.md-diagram-panel .start-state { fill: var(--node-fill); }.md-diagram-panel .edgeLabel rect { opacity: 1 !important; }.md-fences.md-fences-math { font-size: 1em; }.md-fences-advanced:not(.md-focus) { padding: 0px; white-space: nowrap; border: 0px; }.md-fences-advanced:not(.md-focus) { background: inherit; }.typora-export-show-outline .typora-export-content { max-width: 1440px; margin: auto; display: flex; flex-direction: row; }.typora-export-sidebar { width: 300px; font-size: 0.8rem; margin-top: 80px; margin-right: 18px; }.typora-export-show-outline #write { --webkit-flex: 2; flex: 2 1 0%; }.typora-export-sidebar .outline-content { position: fixed; top: 0px; max-height: 100%; overflow: hidden auto; padding-bottom: 30px; padding-top: 60px; width: 300px; }@media screen and (max-width: 1024px) {  .typora-export-sidebar, .typora-export-sidebar .outline-content { width: 240px; }}@media screen and (max-width: 800px) {  .typora-export-sidebar { display: none; }}.outline-content li, .outline-content ul { margin-left: 0px; margin-right: 0px; padding-left: 0px; padding-right: 0px; list-style: none; overflow-wrap: anywhere; }.outline-content ul { margin-top: 0px; margin-bottom: 0px; }.outline-content strong { font-weight: 400; }.outline-expander { width: 1rem; height: 1.42857rem; position: relative; display: table-cell; vertical-align: middle; cursor: pointer; padding-left: 4px; }.outline-expander::before { content: ""; position: relative; font-family: Ionicons; display: inline-block; font-size: 8px; vertical-align: middle; }.outline-item { padding-top: 3px; padding-bottom: 3px; cursor: pointer; }.outline-expander:hover::before { content: ""; }.outline-h1 > .outline-item { padding-left: 0px; }.outline-h2 > .outline-item { padding-left: 1em; }.outline-h3 > .outline-item { padding-left: 2em; }.outline-h4 > .outline-item { padding-left: 3em; }.outline-h5 > .outline-item { padding-left: 4em; }.outline-h6 > .outline-item { padding-left: 5em; }.outline-label { cursor: pointer; display: table-cell; vertical-align: middle; text-decoration: none; color: inherit; }.outline-label:hover { text-decoration: underline; }.outline-item:hover { border-color: rgb(245, 245, 245); background-color: var(--item-hover-bg-color); }.outline-item:hover { margin-left: -28px; margin-right: -28px; border-left: 28px solid transparent; border-right: 28px solid transparent; }.outline-item-single .outline-expander::before, .outline-item-single .outline-expander:hover::before { display: none; }.outline-item-open > .outline-item > .outline-expander::before { content: ""; }.outline-children { display: none; }.info-panel-tab-wrapper { display: none; }.outline-item-open > .outline-children { display: block; }.typora-export .outline-item { padding-top: 1px; padding-bottom: 1px; }.typora-export .outline-item:hover { margin-right: -8px; border-right: 8px solid transparent; }.typora-export .outline-expander::before { content: "+"; font-family: inherit; top: -1px; }.typora-export .outline-expander:hover::before, .typora-export .outline-item-open > .outline-item > .outline-expander::before { content: "−"; }.typora-export-collapse-outline .outline-children { display: none; }.typora-export-collapse-outline .outline-item-open > .outline-children, .typora-export-no-collapse-outline .outline-children { display: block; }.typora-export-no-collapse-outline .outline-expander::before { content: "" !important; }.typora-export-show-outline .outline-item-active > .outline-item .outline-label { font-weight: 700; }.md-inline-math-container mjx-container { zoom: 0.95; }mjx-container { break-inside: avoid; }.md-alert.md-alert-note { border-left-color: rgb(9, 105, 218); }.md-alert.md-alert-important { border-left-color: rgb(130, 80, 223); }.md-alert.md-alert-warning { border-left-color: rgb(154, 103, 0); }.md-alert.md-alert-tip { border-left-color: rgb(31, 136, 61); }.md-alert.md-alert-caution { border-left-color: rgb(207, 34, 46); }.md-alert { padding: 0px 1em; margin-bottom: 16px; color: inherit; border-left: 0.25em solid rgb(0, 0, 0); }.md-alert-text-note { color: rgb(9, 105, 218); }.md-alert-text-important { color: rgb(130, 80, 223); }.md-alert-text-warning { color: rgb(154, 103, 0); }.md-alert-text-tip { color: rgb(31, 136, 61); }.md-alert-text-caution { color: rgb(207, 34, 46); }.md-alert-text { font-size: 0.9rem; font-weight: 700; }.md-alert-text svg { fill: currentcolor; position: relative; top: 0.125em; margin-right: 1ch; overflow: visible; }.md-alert-text-container::after { content: attr(data-text); text-transform: capitalize; pointer-events: none; margin-right: 1ch; }.CodeMirror { height: auto; }.CodeMirror.cm-s-inner { background: inherit; }.CodeMirror-scroll { overflow: auto hidden; z-index: 3; }.CodeMirror-gutter-filler, .CodeMirror-scrollbar-filler { background-color: rgb(255, 255, 255); }.CodeMirror-gutters { border-right: 1px solid rgb(221, 221, 221); background: inherit; white-space: nowrap; }.CodeMirror-linenumber { padding: 0px 3px 0px 5px; text-align: right; color: rgb(153, 153, 153); }.cm-s-inner .cm-keyword { color: rgb(119, 0, 136); }.cm-s-inner .cm-atom, .cm-s-inner.cm-atom { color: rgb(34, 17, 153); }.cm-s-inner .cm-number { color: rgb(17, 102, 68); }.cm-s-inner .cm-def { color: rgb(0, 0, 255); }.cm-s-inner .cm-variable { color: rgb(0, 0, 0); }.cm-s-inner .cm-variable-2 { color: rgb(0, 85, 170); }.cm-s-inner .cm-variable-3 { color: rgb(0, 136, 85); }.cm-s-inner .cm-string { color: rgb(170, 17, 17); }.cm-s-inner .cm-property { color: rgb(0, 0, 0); }.cm-s-inner .cm-operator { color: rgb(152, 26, 26); }.cm-s-inner .cm-comment, .cm-s-inner.cm-comment { color: rgb(170, 85, 0); }.cm-s-inner .cm-string-2 { color: rgb(255, 85, 0); }.cm-s-inner .cm-meta { color: rgb(85, 85, 85); }.cm-s-inner .cm-qualifier { color: rgb(85, 85, 85); }.cm-s-inner .cm-builtin { color: rgb(51, 0, 170); }.cm-s-inner .cm-bracket { color: rgb(153, 153, 119); }.cm-s-inner .cm-tag { color: rgb(17, 119, 0); }.cm-s-inner .cm-attribute { color: rgb(0, 0, 204); }.cm-s-inner .cm-header, .cm-s-inner.cm-header { color: rgb(0, 0, 255); }.cm-s-inner .cm-quote, .cm-s-inner.cm-quote { color: rgb(0, 153, 0); }.cm-s-inner .cm-hr, .cm-s-inner.cm-hr { color: rgb(153, 153, 153); }.cm-s-inner .cm-link, .cm-s-inner.cm-link { color: rgb(0, 0, 204); }.cm-negative { color: rgb(221, 68, 68); }.cm-positive { color: rgb(34, 153, 34); }.cm-header, .cm-strong { font-weight: 700; }.cm-del { text-decoration: line-through; }.cm-em { font-style: italic; }.cm-link { text-decoration: underline; }.cm-error { color: red; }.cm-invalidchar { color: red; }.cm-constant { color: rgb(38, 139, 210); }.cm-defined { color: rgb(181, 137, 0); }div.CodeMirror span.CodeMirror-matchingbracket { color: rgb(0, 255, 0); }div.CodeMirror span.CodeMirror-nonmatchingbracket { color: rgb(255, 34, 34); }.cm-s-inner .CodeMirror-activeline-background { background: inherit; }.CodeMirror { position: relative; overflow: hidden; }.CodeMirror-scroll { height: 100%; outline: 0px; position: relative; box-sizing: content-box; background: inherit; }.CodeMirror-sizer { position: relative; }.CodeMirror-gutter-filler, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-vscrollbar { position: absolute; z-index: 6; display: none; outline: 0px; }.CodeMirror-vscrollbar { right: 0px; top: 0px; overflow: hidden; }.CodeMirror-hscrollbar { bottom: 0px; left: 0px; overflow: auto hidden; }.CodeMirror-scrollbar-filler { right: 0px; bottom: 0px; }.CodeMirror-gutter-filler { left: 0px; bottom: 0px; }.CodeMirror-gutters { position: absolute; left: 0px; top: 0px; padding-bottom: 10px; z-index: 3; overflow-y: hidden; }.CodeMirror-gutter { white-space: normal; height: 100%; box-sizing: content-box; padding-bottom: 30px; margin-bottom: -32px; display: inline-block; }.CodeMirror-gutter-wrapper { position: absolute; z-index: 4; background: 0px 0px !important; border: none !important; }.CodeMirror-gutter-background { position: absolute; top: 0px; bottom: 0px; z-index: 4; }.CodeMirror-gutter-elt { position: absolute; cursor: default; z-index: 4; }.CodeMirror-lines { cursor: text; }.CodeMirror pre { border-radius: 0px; border-width: 0px; background: 0px 0px; font-family: inherit; font-size: inherit; margin: 0px; white-space: pre; overflow-wrap: normal; color: inherit; z-index: 2; position: relative; overflow: visible; }.CodeMirror-wrap pre { overflow-wrap: break-word; white-space: pre-wrap; word-break: normal; }.CodeMirror-code pre { border-right: 30px solid transparent; width: fit-content; }.CodeMirror-wrap .CodeMirror-code pre { border-right: none; width: auto; }.CodeMirror-linebackground { position: absolute; inset: 0px; z-index: 0; }.CodeMirror-linewidget { position: relative; z-index: 2; overflow: auto; }.CodeMirror-wrap .CodeMirror-scroll { overflow-x: hidden; }.CodeMirror-measure { position: absolute; width: 100%; height: 0px; overflow: hidden; visibility: hidden; }.CodeMirror-measure pre { position: static; }.CodeMirror div.CodeMirror-cursor { position: absolute; visibility: hidden; border-right: none; width: 0px; }.CodeMirror div.CodeMirror-cursor { visibility: hidden; }.CodeMirror-focused div.CodeMirror-cursor { visibility: inherit; }.cm-searching { background: rgba(255, 255, 0, 0.4); }span.cm-underlined { text-decoration: underline; }span.cm-strikethrough { text-decoration: line-through; }.cm-tw-syntaxerror { color: rgb(255, 255, 255); background-color: rgb(153, 0, 0); }.cm-tw-deleted { text-decoration: line-through; }.cm-tw-header5 { font-weight: 700; }.cm-tw-listitem:first-child { padding-left: 10px; }.cm-tw-box { border-style: solid; border-right-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-color: inherit; border-top-width: 0px !important; }.cm-tw-underline { text-decoration: underline; }@media print {  .CodeMirror div.CodeMirror-cursor { visibility: hidden; }}:root {    --side-bar-bg-color: #fafafa;    --control-text-color: #777;}@include-when-export url(https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext);/* open-sans-regular - latin-ext_latin */  /* open-sans-italic - latin-ext_latin */    /* open-sans-700 - latin-ext_latin */    /* open-sans-700italic - latin-ext_latin */  html {    font-size: 16px;    -webkit-font-smoothing: antialiased;}body {    font-family: "Open Sans","Clear Sans", "Helvetica Neue", Helvetica, Arial, 'Segoe UI Emoji', sans-serif;    color: rgb(51, 51, 51);    line-height: 1.6;}#write {    max-width: 860px;  	margin: 0 auto;  	padding: 30px;    padding-bottom: 100px;}@media only screen and (min-width: 1400px) {	#write {		max-width: 1024px;	}}@media only screen and (min-width: 1800px) {	#write {		max-width: 1200px;	}}#write > ul:first-child,#write > ol:first-child{    margin-top: 30px;}a {    color: #4183C4;}h1,h2,h3,h4,h5,h6 {    position: relative;    margin-top: 1rem;    margin-bottom: 1rem;    font-weight: bold;    line-height: 1.4;    cursor: text;}h1:hover a.anchor,h2:hover a.anchor,h3:hover a.anchor,h4:hover a.anchor,h5:hover a.anchor,h6:hover a.anchor {    text-decoration: none;}h1 tt,h1 code {    font-size: inherit;}h2 tt,h2 code {    font-size: inherit;}h3 tt,h3 code {    font-size: inherit;}h4 tt,h4 code {    font-size: inherit;}h5 tt,h5 code {    font-size: inherit;}h6 tt,h6 code {    font-size: inherit;}h1 {    font-size: 2.25em;    line-height: 1.2;    border-bottom: 1px solid #eee;}h2 {    font-size: 1.75em;    line-height: 1.225;    border-bottom: 1px solid #eee;}/*@media print {    .typora-export h1,    .typora-export h2 {        border-bottom: none;        padding-bottom: initial;    }    .typora-export h1::after,    .typora-export h2::after {        content: "";        display: block;        height: 100px;        margin-top: -96px;        border-top: 1px solid #eee;    }}*/h3 {    font-size: 1.5em;    line-height: 1.43;}h4 {    font-size: 1.25em;}h5 {    font-size: 1em;}h6 {   font-size: 1em;    color: #777;}p,blockquote,ul,ol,dl,table{    margin: 0.8em 0;}li>ol,li>ul {    margin: 0 0;}hr {    height: 2px;    padding: 0;    margin: 16px 0;    background-color: #e7e7e7;    border: 0 none;    overflow: hidden;    box-sizing: content-box;}li p.first {    display: inline-block;}ul,ol {    padding-left: 30px;}ul:first-child,ol:first-child {    margin-top: 0;}ul:last-child,ol:last-child {    margin-bottom: 0;}blockquote {    border-left: 4px solid #dfe2e5;    padding: 0 15px;    color: #777777;}blockquote blockquote {    padding-right: 0;}table {    padding: 0;    word-break: initial;}table tr {    border: 1px solid #dfe2e5;    margin: 0;    padding: 0;}table tr:nth-child(2n),thead {    background-color: #f8f8f8;}table th {    font-weight: bold;    border: 1px solid #dfe2e5;    border-bottom: 0;    margin: 0;    padding: 6px 13px;}table td {    border: 1px solid #dfe2e5;    margin: 0;    padding: 6px 13px;}table th:first-child,table td:first-child {    margin-top: 0;}table th:last-child,table td:last-child {    margin-bottom: 0;}.CodeMirror-lines {    padding-left: 4px;}.code-tooltip {    box-shadow: 0 1px 1px 0 rgba(0,28,36,.3);    border-top: 1px solid #eef2f2;}.md-fences,code,tt {    border: 1px solid #e7eaed;    background-color: #f8f8f8;    border-radius: 3px;    padding: 0;    padding: 2px 4px 0px 4px;    font-size: 0.9em;}code {    background-color: #f3f4f4;    padding: 0 2px 0 2px;}.md-fences {    margin-bottom: 15px;    margin-top: 15px;    padding-top: 8px;    padding-bottom: 6px;}.md-task-list-item > input {  margin-left: -1.3em;}@media print {    html {        font-size: 13px;    }    pre {        page-break-inside: avoid;        word-wrap: break-word;    }}.md-fences {	background-color: #f8f8f8;}#write pre.md-meta-block {	padding: 1rem;    font-size: 85%;    line-height: 1.45;    background-color: #f7f7f7;    border: 0;    border-radius: 3px;    color: #777777;    margin-top: 0 !important;}.mathjax-block>.code-tooltip {	bottom: .375rem;}.md-mathjax-midline {    background: #fafafa;}#write>h3.md-focus:before{	left: -1.5625rem;	top: .375rem;}#write>h4.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}#write>h5.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}#write>h6.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}.md-image>.md-meta {    /*border: 1px solid #ddd;*/    border-radius: 3px;    padding: 2px 0px 0px 4px;    font-size: 0.9em;    color: inherit;}.md-tag {    color: #a7a7a7;    opacity: 1;}.md-toc {     margin-top:20px;    padding-bottom:20px;}.sidebar-tabs {    border-bottom: none;}#typora-quick-open {    border: 1px solid #ddd;    background-color: #f8f8f8;}#typora-quick-open-item {    background-color: #FAFAFA;    border-color: #FEFEFE #e5e5e5 #e5e5e5 #eee;    border-style: solid;    border-width: 1px;}/** focus mode */.on-focus-mode blockquote {    border-left-color: rgba(85, 85, 85, 0.12);}header, .context-menu, .megamenu-content, footer{    font-family: "Segoe UI", "Arial", sans-serif;}.file-node-content:hover .file-node-icon,.file-node-content:hover .file-node-open-state{    visibility: visible;}.mac-seamless-mode #typora-sidebar {    background-color: #fafafa;    background-color: var(--side-bar-bg-color);}.mac-os #write{    caret-color: AccentColor;}.md-lang {    color: #b4654d;}/*.html-for-mac {    --item-hover-bg-color: #E6F0FE;}*/#md-notification .btn {    border: 0;}.dropdown-menu .divider {    border-color: #e5e5e5;    opacity: 0.4;}.ty-preferences .window-content {    background-color: #fafafa;}.ty-preferences .nav-group-item.active {    color: white;    background: #999;}.menu-item-container a.menu-style-btn {    background-color: #f5f8fa;    background-image: linear-gradient( 180deg , hsla(0, 0%, 100%, 0.8), hsla(0, 0%, 100%, 0)); }</style><title>【华为OD-E卷 -31 异常的打卡记录100分（python、java、c++、js、c）】</title></head><body class='typora-export os-windows typora-export-show-outline typora-export-no-collapse-outline'><div class='typora-export-content'><div class="typora-export-sidebar"><div class="outline-content"><li class="outline-item-wrapper outline-h2"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#注意点此-链接-其他算法题目详细解法">注意：点此 链接 其他算法题目详细解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h2"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#投稿交流错误反馈-v">投稿、交流、错误反馈 +V：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#"></a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#华为od-e卷---异常的打卡记录100分pythonjavacjsc）">【华为OD-E卷 - 异常的打卡记录100分（python、java、c++、js、c）】</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#题目">题目</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h3"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入描述">输入描述</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h3"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出描述">输出描述</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h3"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#备注">备注</a></div><ul class="outline-children"></ul></li></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例">用例</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h4"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例一">用例一：</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入">输入：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出">输出：</a></div><ul class="outline-children"></ul></li></ul></li><li class="outline-item-wrapper outline-h4"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例二">用例二：</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入-2">输入：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出-2">输出：</a></div><ul class="outline-children"></ul></li></ul></li><li class="outline-item-wrapper outline-h4"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例三">用例三：</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入-3">输入：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出-3">输出：</a></div><ul class="outline-children"></ul></li></ul></li></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#python解法">python解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#java解法">java解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#c解法">C++解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#c解法-2">C解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#js解法">JS解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#注意">注意：</a></div><ul class="outline-children"></ul></li></div></div><div id='write'  class=''><h2 id='注意点此-链接-其他算法题目详细解法'><a href='https://blog.csdn.net/codeclimb/category_12842753.html?spm=1001.2014.3001.5482'><span>注意：点此 链接 其他算法题目详细解法</span></a></h2><h2 id='投稿交流错误反馈-v'><span>投稿、交流、错误反馈 +V：</span></h2><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="http"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="http"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-error">locally-attached</span></span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h1></h1><h1 id='华为od-e卷---异常的打卡记录100分pythonjavacjsc）'><span>【华为OD-E卷 - 异常的打卡记录100分（python、java、c++、js、c）】</span></h1><h1 id='题目'><span>题目</span></h1><p><strong><span>考勤记录是分析和考核职工工作时间利用情况的原始依据，也是计算职工工资的原始依据，为了正确地计算职工工资和监督工资基金使用情况，公司决定对员工的手机打卡记录进行异常排查。</span><span>如果出现以下两种情况，则认为打卡异常：</span><span>实际设备号与注册设备号不一样 或者，同一个员工的两个打卡记录的时间小于60分钟并且打卡距离超过5km。 给定打卡记录的字符串数组 clockRecords（每个打卡记录组成为：工号;时间（分钟）;打卡距离（km）;实际设备号;注册设备号），返回其中异常的打卡记录（按输入顺序输出）</span></strong></p><h3 id='输入描述'><span>输入描述</span></h3><ul><li><p><span>第一行输入为N，表示打卡记录数；</span></p></li></ul><p><span>之后的N行为打卡记录，每一行为一条打卡记录。</span></p><p>&nbsp;</p><h3 id='输出描述'><span>输出描述</span></h3><ul><li><p><span>输出异常的打卡记录</span></p></li></ul><h3 id='备注'><span>备注</span></h3><ul><li><p><span>clockRecords长度 ≤ 1000 clockRecords[i] 格式：{id},{time},{distance},{actualDeviceNumber},{registeredDeviceNumber} id由6位数字组成 time由整数组成，范围为0 ~ 1000 distance由整数组成，范围为0 ~100 actualDeviceNumber与registeredDeviceNumber由思维大写字母组成</span></p></li></ul><h1 id='用例'><span>用例</span></h1><h4 id='用例一'><span>用例一：</span></h4><h5 id='输入'><span>输入：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">2</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">100000,10,1,ABCD,ABCD</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">100000,50,10,ABCD,ABCD</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 69px;"></div><div class="CodeMirror-gutters" style="display: none; height: 69px;"></div></div></div></pre><h5 id='输出'><span>输出：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">100000,10,1,ABCD,ABCD;100000,50,10,ABCD,ABCD</span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h4 id='用例二'><span>用例二：</span></h4><h5 id='输入-2'><span>输入：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">2</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">100000,10,1,ABCD,ABCD</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">100001,80,10,ABCE,ABCE</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 69px;"></div><div class="CodeMirror-gutters" style="display: none; height: 69px;"></div></div></div></pre><h5 id='输出-2'><span>输出：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">null</span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h4 id='用例三'><span>用例三：</span></h4><h5 id='输入-3'><span>输入：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">2</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">100000,10,1,ABCD,ABCD</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">100000,80,10,ABCE,ABCD</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 69px;"></div><div class="CodeMirror-gutters" style="display: none; height: 69px;"></div></div></div></pre><h5 id='输出-3'><span>输出：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">100000,80,10,ABCE,ABCD</span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h1 id='python解法'><span>python解法</span></h1><ul><li><p><span>解题思路：</span></p></li><li><p><span>数据结构选择：</span></p></li></ul><p><span>使用 emp 字典来存储每个员工的最后一次打卡信息。键为员工 ID (eid)，值为三元组 (最后一次打卡时间, 最后一次打卡地点, 当前记录的索引)。</span><span>使用 result 集合来存储异常记录的索引。</span><span>遍历记录：</span></p><p><span>对于每条记录，首先判断报告地址 (ad) 是否与打卡地址 (rd) 一致，如果不一致，则这条记录是异常的，加入到 result 集合中。</span><span>然后，检查该员工的前一次打卡记录，若满足打卡时间差小于 60 分钟且打卡地点相差超过 5 米的条件，则认为这两次打卡是异常的，当前记录与之前的记录都应该加入异常记录集合中。</span><span>输出结果：</span></p><p><span>若存在异常记录，则返回按要求格式化的结果，返回所有异常记录的信息，按照异常记录的索引升序排列。</span><span>如果没有异常记录，返回 &quot;null&quot;。</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="python" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="python"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><span><span>​</span>x</span></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment"># 输入读取</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">n</span> <span class="cm-operator">=</span> <span class="cm-builtin">int</span>(<span class="cm-builtin">input</span>()) &nbsp;<span class="cm-comment"># 输入记录数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">cr</span> <span class="cm-operator">=</span> [<span class="cm-builtin">input</span>().<span class="cm-property">split</span>(<span class="cm-string">","</span>) <span class="cm-keyword">for</span> <span class="cm-variable">_</span> <span class="cm-keyword">in</span> <span class="cm-builtin">range</span>(<span class="cm-variable">n</span>)] &nbsp;<span class="cm-comment"># 读取n条记录，存储为列表，每条记录是一个列表</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">def</span> <span class="cm-def">find_abnormal</span>(<span class="cm-variable">cr</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">emp</span> <span class="cm-operator">=</span> {} &nbsp;<span class="cm-comment"># 用来存储每个员工的最后一次打卡信息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">result</span> <span class="cm-operator">=</span> <span class="cm-builtin">set</span>() &nbsp;<span class="cm-comment"># 用来存储异常记录的索引</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 遍历所有的打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> <span class="cm-variable">i</span> <span class="cm-keyword">in</span> <span class="cm-builtin">range</span>(<span class="cm-builtin">len</span>(<span class="cm-variable">cr</span>)):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 从当前记录中提取员工ID、时间、地点等信息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">eid</span>, <span class="cm-variable">t</span>, <span class="cm-variable">d</span>, <span class="cm-variable">ad</span>, <span class="cm-variable">rd</span> <span class="cm-operator">=</span> <span class="cm-variable">cr</span>[<span class="cm-variable">i</span>]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 1. 判断报告地址与打卡地址是否不一致</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> <span class="cm-variable">ad</span> <span class="cm-operator">!=</span> <span class="cm-variable">rd</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">result</span>.<span class="cm-property">add</span>(<span class="cm-variable">i</span>) &nbsp;<span class="cm-comment"># 若不一致，加入异常记录集</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 2. 检查是否存在两次打卡时间小于60分钟且地点相差超过5米的情况</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> <span class="cm-variable">eid</span> <span class="cm-keyword">in</span> <span class="cm-variable">emp</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">last_t</span>, <span class="cm-variable">last_d</span>, <span class="cm-variable">idx</span> <span class="cm-operator">=</span> <span class="cm-variable">emp</span>[<span class="cm-variable">eid</span>] &nbsp;<span class="cm-comment"># 获取该员工上次的打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 判断时间差小于60分钟且地点差异大于5米</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> <span class="cm-builtin">int</span>(<span class="cm-variable">t</span>) <span class="cm-operator">-</span> <span class="cm-variable">last_t</span> <span class="cm-operator">&lt;</span> <span class="cm-number">60</span> <span class="cm-keyword">and</span> <span class="cm-builtin">abs</span>(<span class="cm-builtin">int</span>(<span class="cm-variable">d</span>) <span class="cm-operator">-</span> <span class="cm-variable">last_d</span>) <span class="cm-operator">&gt;</span> <span class="cm-number">5</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">result</span>.<span class="cm-property">add</span>(<span class="cm-variable">i</span>) &nbsp;<span class="cm-comment"># 当前记录异常</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">result</span>.<span class="cm-property">add</span>(<span class="cm-variable">idx</span>) &nbsp;<span class="cm-comment"># 上次记录异常</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 更新该员工的打卡信息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">emp</span>[<span class="cm-variable">eid</span>] <span class="cm-operator">=</span> (<span class="cm-builtin">int</span>(<span class="cm-variable">t</span>), <span class="cm-builtin">int</span>(<span class="cm-variable">d</span>), <span class="cm-variable">i</span>) &nbsp;<span class="cm-comment"># 存储最新的时间、地点和当前记录的索引</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 如果有异常记录，按照记录索引升序排列并输出</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">if</span> <span class="cm-variable">result</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-string">";"</span>.<span class="cm-property">join</span>(<span class="cm-string">","</span>.<span class="cm-property">join</span>(<span class="cm-variable">cr</span>[<span class="cm-variable">i</span>]) <span class="cm-keyword">for</span> <span class="cm-variable">i</span> <span class="cm-keyword">in</span> <span class="cm-builtin">sorted</span>(<span class="cm-variable">result</span>)) &nbsp;<span class="cm-comment"># 格式化输出异常记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-string">"null"</span> &nbsp;<span class="cm-comment"># 若无异常记录，返回 "null"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment"># 输出结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-builtin">print</span>(<span class="cm-variable">find_abnormal</span>(<span class="cm-variable">cr</span>))</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 898px;"></div><div class="CodeMirror-gutters" style="display: none; height: 898px;"></div></div></div></pre><h1 id='java解法'><span>java解法</span></h1><ul><li><p><span>解题思路</span></p></li><li><p><span>此题要求对员工的打卡记录进行分析，找出异常记录。异常的判断标准有两个：</span></p></li></ul><p><span>打卡地址与报告地址不一致，这条记录是异常的。</span><span>打卡时间差小于 60 分钟且地点差异超过 5 米，这两条记录是异常的。</span><span>解题步骤：</span><span>数据存储：</span></p><p><span>empData：一个哈希表，用于存储每个员工的打卡记录。键是员工 ID（eid），值是该员工所有的打卡记录列表。</span><span>errorIdx：一个集合，用于存储异常记录的索引。</span><span>解析记录：</span></p><p><span>对于每条记录，首先检查报告地址与打卡地址是否一致。如果不一致，将该记录的索引加入异常记录集合 errorIdx。</span><span>然后，遍历每个员工的记录，检查相邻的打卡记录，如果时间差小于 60 分钟且地点差异超过 5 米，则认为这两次打卡是异常的，添加到异常记录集合。</span><span>排序和输出：</span></p><p><span>对每个员工的记录按照时间进行排序，然后进行时间差和地点差的检查。</span><span>如果有异常记录，按要求格式化输出异常记录；如果没有，输出 &quot;null&quot;。</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="java" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="java"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">java</span>.<span class="cm-variable">util</span>.<span class="cm-operator">*</span>;</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">public</span> <span class="cm-keyword">class</span> <span class="cm-def">Main</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">public</span> <span class="cm-keyword">static</span> <span class="cm-variable-3">void</span> <span class="cm-variable">main</span>(<span class="cm-variable-3">String</span>[] <span class="cm-variable">args</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">Scanner</span> <span class="cm-variable">input</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">Scanner</span>(<span class="cm-variable">System</span>.<span class="cm-variable">in</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">n</span> <span class="cm-operator">=</span> <span class="cm-variable">input</span>.<span class="cm-variable">nextInt</span>(); &nbsp;<span class="cm-comment">// 读取打卡记录的数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">String</span>[][] <span class="cm-variable">logs</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable-3">String</span>[<span class="cm-variable">n</span>][]; &nbsp;<span class="cm-comment">// 用于存储所有打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 读取所有打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">n</span>; <span class="cm-variable">i</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">logs</span>[<span class="cm-variable">i</span>] <span class="cm-operator">=</span> <span class="cm-variable">input</span>.<span class="cm-variable">next</span>().<span class="cm-variable">split</span>(<span class="cm-string">","</span>); &nbsp;<span class="cm-comment">// 每条记录按逗号分割</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">System</span>.<span class="cm-variable">out</span>.<span class="cm-variable">println</span>(<span class="cm-variable">findAnomalies</span>(<span class="cm-variable">logs</span>)); &nbsp;<span class="cm-comment">// 调用方法找出异常记录并输出</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 查找异常打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">public</span> <span class="cm-keyword">static</span> <span class="cm-variable-3">String</span> <span class="cm-variable">findAnomalies</span>(<span class="cm-variable-3">String</span>[][] <span class="cm-variable">logs</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">HashMap</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">String</span>, <span class="cm-variable">List</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">String</span>[]<span class="cm-operator">&gt;&gt;</span> <span class="cm-variable">empData</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">HashMap</span><span class="cm-operator">&lt;&gt;</span>(); &nbsp;<span class="cm-comment">// 存储每个员工的打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">Set</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">Integer</span><span class="cm-operator">&gt;</span> <span class="cm-variable">errorIdx</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">TreeSet</span><span class="cm-operator">&lt;&gt;</span>(); &nbsp;<span class="cm-comment">// 存储异常记录的索引</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 处理所有记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">logs</span>.<span class="cm-variable">length</span>; <span class="cm-variable">i</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">String</span>[] <span class="cm-variable">log</span> <span class="cm-operator">=</span> <span class="cm-variable">Arrays</span>.<span class="cm-variable">copyOf</span>(<span class="cm-variable">logs</span>[<span class="cm-variable">i</span>], <span class="cm-variable">logs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">length</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>); &nbsp;<span class="cm-comment">// 复制记录并扩展，添加记录的索引</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">log</span>[<span class="cm-variable">log</span>.<span class="cm-variable">length</span> <span class="cm-operator">-</span> <span class="cm-number">1</span>] <span class="cm-operator">=</span> <span class="cm-variable-3">String</span>.<span class="cm-variable">valueOf</span>(<span class="cm-variable">i</span>); &nbsp;<span class="cm-comment">// 添加当前记录的索引</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 1. 如果报告地址与打卡地址不一致，加入异常记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-operator">!</span><span class="cm-variable">log</span>[<span class="cm-number">3</span>].<span class="cm-variable">equals</span>(<span class="cm-variable">log</span>[<span class="cm-number">4</span>])) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">errorIdx</span>.<span class="cm-variable">add</span>(<span class="cm-variable">i</span>); &nbsp;<span class="cm-comment">// 将当前记录索引加入异常记录集合</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 2. 将打卡记录按照员工 ID 存入 empData</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">empData</span>.<span class="cm-variable">computeIfAbsent</span>(<span class="cm-variable">log</span>[<span class="cm-number">0</span>], <span class="cm-variable">k</span> <span class="cm-operator">-&gt;</span> <span class="cm-keyword">new</span> <span class="cm-variable">ArrayList</span><span class="cm-operator">&lt;&gt;</span>()).<span class="cm-variable">add</span>(<span class="cm-variable">log</span>); &nbsp;<span class="cm-comment">// 按员工 ID 存储记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 遍历每个员工的打卡记录，查找符合异常条件的记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">String</span> <span class="cm-variable">key</span> : <span class="cm-variable">empData</span>.<span class="cm-variable">keySet</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">List</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">String</span>[]<span class="cm-operator">&gt;</span> <span class="cm-variable">records</span> <span class="cm-operator">=</span> <span class="cm-variable">empData</span>.<span class="cm-variable">get</span>(<span class="cm-variable">key</span>); &nbsp;<span class="cm-comment">// 获取当前员工的打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">records</span>.<span class="cm-variable">sort</span>(<span class="cm-variable">Comparator</span>.<span class="cm-variable">comparingInt</span>(<span class="cm-variable">a</span> <span class="cm-operator">-&gt;</span> <span class="cm-variable-3">Integer</span>.<span class="cm-variable">parseInt</span>(<span class="cm-variable">a</span>[<span class="cm-number">1</span>]))); &nbsp;<span class="cm-comment">// 按照时间排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 检查每两条记录之间的时间差和地点差</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">records</span>.<span class="cm-variable">size</span>(); <span class="cm-variable">i</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">t1</span> <span class="cm-operator">=</span> <span class="cm-variable-3">Integer</span>.<span class="cm-variable">parseInt</span>(<span class="cm-variable">records</span>.<span class="cm-variable">get</span>(<span class="cm-variable">i</span>)[<span class="cm-number">1</span>]); &nbsp;<span class="cm-comment">// 第一条记录的时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">d1</span> <span class="cm-operator">=</span> <span class="cm-variable-3">Integer</span>.<span class="cm-variable">parseInt</span>(<span class="cm-variable">records</span>.<span class="cm-variable">get</span>(<span class="cm-variable">i</span>)[<span class="cm-number">2</span>]); &nbsp;<span class="cm-comment">// 第一条记录的地点</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 与之后的记录进行比较</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">j</span> <span class="cm-operator">=</span> <span class="cm-variable">i</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>; <span class="cm-variable">j</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">records</span>.<span class="cm-variable">size</span>(); <span class="cm-variable">j</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">t2</span> <span class="cm-operator">=</span> <span class="cm-variable-3">Integer</span>.<span class="cm-variable">parseInt</span>(<span class="cm-variable">records</span>.<span class="cm-variable">get</span>(<span class="cm-variable">j</span>)[<span class="cm-number">1</span>]); &nbsp;<span class="cm-comment">// 第二条记录的时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">d2</span> <span class="cm-operator">=</span> <span class="cm-variable-3">Integer</span>.<span class="cm-variable">parseInt</span>(<span class="cm-variable">records</span>.<span class="cm-variable">get</span>(<span class="cm-variable">j</span>)[<span class="cm-number">2</span>]); &nbsp;<span class="cm-comment">// 第二条记录的地点</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果时间差大于等于60分钟，则跳出循环</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">t2</span> <span class="cm-operator">-</span> <span class="cm-variable">t1</span> <span class="cm-operator">&gt;=</span> <span class="cm-number">60</span>) <span class="cm-keyword">break</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果时间差小于60分钟，且地点差超过5米，认为两条记录异常</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">Math</span>.<span class="cm-variable">abs</span>(<span class="cm-variable">d2</span> <span class="cm-operator">-</span> <span class="cm-variable">d1</span>) <span class="cm-operator">&gt;</span> <span class="cm-number">5</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">errorIdx</span>.<span class="cm-variable">add</span>(<span class="cm-variable-3">Integer</span>.<span class="cm-variable">parseInt</span>(<span class="cm-variable">records</span>.<span class="cm-variable">get</span>(<span class="cm-variable">i</span>)[<span class="cm-number">5</span>])); &nbsp;<span class="cm-comment">// 添加第一条记录的索引</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">errorIdx</span>.<span class="cm-variable">add</span>(<span class="cm-variable-3">Integer</span>.<span class="cm-variable">parseInt</span>(<span class="cm-variable">records</span>.<span class="cm-variable">get</span>(<span class="cm-variable">j</span>)[<span class="cm-number">5</span>])); &nbsp;<span class="cm-comment">// 添加第二条记录的索引</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果没有异常记录，返回 "null"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">errorIdx</span>.<span class="cm-variable">isEmpty</span>()) <span class="cm-keyword">return</span> <span class="cm-string">"null"</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 格式化输出异常记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">StringJoiner</span> <span class="cm-variable">sj</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">StringJoiner</span>(<span class="cm-string">";"</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">idx</span> : <span class="cm-variable">errorIdx</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">sj</span>.<span class="cm-variable">add</span>(<span class="cm-variable-3">String</span>.<span class="cm-variable">join</span>(<span class="cm-string">","</span>, <span class="cm-variable">logs</span>[<span class="cm-variable">idx</span>])); &nbsp;<span class="cm-comment">// 将异常记录按照要求的格式连接起来</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">sj</span>.<span class="cm-variable">toString</span>(); &nbsp;<span class="cm-comment">// 返回格式化后的异常记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 1981px;"></div><div class="CodeMirror-gutters" style="display: none; height: 1981px;"></div></div></div></pre><h1 id='c解法'><span>C++解法</span></h1><ul><li><p><span>解题思路</span></p></li><li><p>&nbsp;</p></li></ul><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="cpp"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="cpp"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">更新中</span></span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h1 id='c解法-2'><span>C解法</span></h1><ul><li><h3 id='解题思路'><span>解题思路</span></h3></li><li><p>&nbsp;</p></li></ul><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="c"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="c"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">更新中</span></span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><p>&nbsp;</p><h1 id='js解法'><span>JS解法</span></h1><ul><li><h3 id='解题思路-2'><span>解题思路</span></h3></li><li><p><span>问题要求从员工的打卡记录中找出异常记录，异常记录满足以下条件之一：</span></p></li></ul><p><span>报告地址和打卡地址不一致。</span><span>同一员工的打卡记录之间，时间差小于 60 分钟，且地点差异大于 5 米。</span><span>步骤：</span><span>输入数据处理：</span></p><p><span>记录的格式为 [员工ID, 打卡时间, 打卡地点, 预计打卡地点, 报告打卡地点]。</span><span>需要遍历每一条记录，并检查打卡地址是否与报告地址一致。</span><span>需要将每一条记录按照员工 ID 分类存储，以便后续对同一员工的打卡记录进行时间和地点差的检查。</span><span>判断异常条件：</span></p><p><span>如果报告地址与打卡地址不一致，记录为异常。</span><span>对于每个员工的记录，按照打卡时间升序排序，然后检查相邻记录的时间差和地点差。</span><span>如果时间差小于 60 分钟且地点差大于 5 米，则这两条记录都标记为异常。</span><span>输出：</span></p><p><span>如果有异常记录，输出异常记录的内容，按要求格式化（记录间用 ; 分隔）。</span><span>如果没有异常记录，输出 &quot;null&quot;。</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 找出所有异常的打卡记录</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">function</span> <span class="cm-def">findAnomalies</span>(<span class="cm-def">logs</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 用 Map 存储员工的所有打卡记录，键是员工ID，值是该员工的所有打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">empData</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">Map</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 用 Set 存储异常记录的索引</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">errorIdx</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">Set</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 遍历每条记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-2">logs</span>.<span class="cm-property">forEach</span>((<span class="cm-def">log</span>, <span class="cm-def">i</span>) <span class="cm-operator">=&gt;</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 复制记录并添加记录的索引，以便后续引用</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">extendedLog</span> <span class="cm-operator">=</span> [<span class="cm-meta">...</span><span class="cm-variable-2">log</span>, <span class="cm-variable-2">i</span>.<span class="cm-property">toString</span>()];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 1. 如果报告地址与打卡地址不一致，标记异常</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable-2">extendedLog</span>[<span class="cm-number">3</span>].<span class="cm-property">trim</span>() <span class="cm-operator">!==</span> <span class="cm-variable-2">extendedLog</span>[<span class="cm-number">4</span>].<span class="cm-property">trim</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">errorIdx</span>.<span class="cm-property">add</span>(<span class="cm-variable-2">i</span>); <span class="cm-comment">// 将异常记录的索引添加到 errorIdx</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 2. 按员工ID将记录分类存储</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-operator">!</span><span class="cm-variable-2">empData</span>.<span class="cm-property">has</span>(<span class="cm-variable-2">extendedLog</span>[<span class="cm-number">0</span>])) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">empData</span>.<span class="cm-property">set</span>(<span class="cm-variable-2">extendedLog</span>[<span class="cm-number">0</span>], []); <span class="cm-comment">// 如果没有该员工，初始化为一个空数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">empData</span>.<span class="cm-property">get</span>(<span class="cm-variable-2">extendedLog</span>[<span class="cm-number">0</span>]).<span class="cm-property">push</span>(<span class="cm-variable-2">extendedLog</span>); <span class="cm-comment">// 将记录加入该员工的记录列表</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  });</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 对每个员工的记录进行处理</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">const</span> <span class="cm-def">key</span> <span class="cm-keyword">of</span> <span class="cm-variable-2">empData</span>.<span class="cm-property">keys</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">records</span> <span class="cm-operator">=</span> <span class="cm-variable-2">empData</span>.<span class="cm-property">get</span>(<span class="cm-variable-2">key</span>); &nbsp;<span class="cm-comment">// 获取该员工的所有打卡记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 按打卡时间升序排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">records</span>.<span class="cm-property">sort</span>((<span class="cm-def">a</span>, <span class="cm-def">b</span>) <span class="cm-operator">=&gt;</span> <span class="cm-variable">parseInt</span>(<span class="cm-variable-2">a</span>[<span class="cm-number">1</span>]) <span class="cm-operator">-</span> <span class="cm-variable">parseInt</span>(<span class="cm-variable-2">b</span>[<span class="cm-number">1</span>]));</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 遍历员工的打卡记录，检查时间和地点差</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">let</span> <span class="cm-def">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable-2">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable-2">records</span>.<span class="cm-property">length</span>; <span class="cm-variable-2">i</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">t1</span> <span class="cm-operator">=</span> <span class="cm-variable">parseInt</span>(<span class="cm-variable-2">records</span>[<span class="cm-variable-2">i</span>][<span class="cm-number">1</span>]); <span class="cm-comment">// 获取第一个记录的时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">d1</span> <span class="cm-operator">=</span> <span class="cm-variable">parseInt</span>(<span class="cm-variable-2">records</span>[<span class="cm-variable-2">i</span>][<span class="cm-number">2</span>]); <span class="cm-comment">// 获取第一个记录的地点</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 与之后的记录进行比较</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">let</span> <span class="cm-def">j</span> <span class="cm-operator">=</span> <span class="cm-variable-2">i</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>; <span class="cm-variable-2">j</span> <span class="cm-operator">&lt;</span> <span class="cm-variable-2">records</span>.<span class="cm-property">length</span>; <span class="cm-variable-2">j</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">t2</span> <span class="cm-operator">=</span> <span class="cm-variable">parseInt</span>(<span class="cm-variable-2">records</span>[<span class="cm-variable-2">j</span>][<span class="cm-number">1</span>]); <span class="cm-comment">// 获取第二个记录的时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">d2</span> <span class="cm-operator">=</span> <span class="cm-variable">parseInt</span>(<span class="cm-variable-2">records</span>[<span class="cm-variable-2">j</span>][<span class="cm-number">2</span>]); <span class="cm-comment">// 获取第二个记录的地点</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果时间差大于等于60分钟，则跳出循环</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable-2">t2</span> <span class="cm-operator">-</span> <span class="cm-variable-2">t1</span> <span class="cm-operator">&gt;=</span> <span class="cm-number">60</span>) <span class="cm-keyword">break</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果时间差小于60分钟，且地点差超过5米，认为两条记录异常</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">Math</span>.<span class="cm-property">abs</span>(<span class="cm-variable-2">d2</span> <span class="cm-operator">-</span> <span class="cm-variable-2">d1</span>) <span class="cm-operator">&gt;</span> <span class="cm-number">5</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 标记两条记录为异常</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">errorIdx</span>.<span class="cm-property">add</span>(<span class="cm-variable">parseInt</span>(<span class="cm-variable-2">records</span>[<span class="cm-variable-2">i</span>][<span class="cm-number">5</span>]));</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">errorIdx</span>.<span class="cm-property">add</span>(<span class="cm-variable">parseInt</span>(<span class="cm-variable-2">records</span>[<span class="cm-variable-2">j</span>][<span class="cm-number">5</span>]));</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 如果没有异常记录，返回 "null"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable-2">errorIdx</span>.<span class="cm-property">size</span> <span class="cm-operator">===</span> <span class="cm-number">0</span>) <span class="cm-keyword">return</span> <span class="cm-string">"null"</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 格式化输出异常记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">result</span> <span class="cm-operator">=</span> <span class="cm-variable">Array</span>.<span class="cm-property">from</span>(<span class="cm-variable-2">errorIdx</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  .<span class="cm-property">map</span>(<span class="cm-def">idx</span> <span class="cm-operator">=&gt;</span> <span class="cm-variable-2">logs</span>[<span class="cm-variable-2">idx</span>].<span class="cm-property">join</span>(<span class="cm-string">","</span>)) <span class="cm-comment">// 将每条异常记录转换为字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  .<span class="cm-property">join</span>(<span class="cm-string">";"</span>); &nbsp;<span class="cm-comment">// 用分号连接所有异常记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable-2">result</span>; &nbsp;<span class="cm-comment">// 返回格式化后的异常记录</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 读取输入数据并调用 findAnomalies 方法</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">readline</span> <span class="cm-operator">=</span> <span class="cm-variable">require</span>(<span class="cm-string">'readline'</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">rl</span> <span class="cm-operator">=</span> <span class="cm-variable">readline</span>.<span class="cm-property">createInterface</span>({</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-property">input</span>: <span class="cm-variable">process</span>.<span class="cm-property">stdin</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-property">output</span>: <span class="cm-variable">process</span>.<span class="cm-property">stdout</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">});</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 询问输入的记录数量 n</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">rl</span>.<span class="cm-property">question</span>(<span class="cm-string">''</span>, (<span class="cm-def">n</span>) <span class="cm-operator">=&gt;</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">logs</span> <span class="cm-operator">=</span> [];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">let</span> <span class="cm-def">count</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 监听每一行输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">rl</span>.<span class="cm-property">on</span>(<span class="cm-string">'line'</span>, (<span class="cm-def">input</span>) <span class="cm-operator">=&gt;</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable-2">input</span>.<span class="cm-property">trim</span>() <span class="cm-operator">===</span> <span class="cm-string">''</span>) <span class="cm-keyword">return</span>; &nbsp;<span class="cm-comment">// 如果输入为空则跳过</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">logs</span>.<span class="cm-property">push</span>(<span class="cm-variable-2">input</span>.<span class="cm-property">split</span>(<span class="cm-string">","</span>)); &nbsp;<span class="cm-comment">// 将输入的记录按逗号分割，保存到 logs 数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">count</span><span class="cm-operator">++</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable-2">count</span> <span class="cm-operator">===</span> <span class="cm-variable">parseInt</span>(<span class="cm-variable-2">n</span>)) { &nbsp;<span class="cm-comment">// 如果输入的记录数量达到 n</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">console</span>.<span class="cm-property">log</span>(<span class="cm-variable">findAnomalies</span>(<span class="cm-variable-2">logs</span>)); &nbsp;<span class="cm-comment">// 调用 findAnomalies 输出结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">rl</span>.<span class="cm-property">close</span>(); &nbsp;<span class="cm-comment">// 关闭输入流</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  });</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">});</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 2050px;"></div><div class="CodeMirror-gutters" style="display: none; height: 2050px;"></div></div></div></pre><h1 id='注意'><span>注意：</span></h1><p><strong><span>如果发现代码有用例覆盖不到的情况，欢迎反馈！会在第一时间修正，更新。</span></strong><strong><span>解题不易，如对您有帮助，欢迎点赞/收藏</span></strong></p></div></div><script>(function(){function e(e,n,i){document.addEventListener(e,function(e){if(!e.defaultPrevented)for(var t=e.target;t&&t!=this;t=t.parentNode)if(t.matches(n)){!1===i.call(t,e)&&(e.preventDefault(),e.stopPropagation());break}},!1)}var t=document.body.parentElement,i=[],r=null,o=document.body.classList.contains("typora-export-collapse-outline");function a(){return t.scrollTop}e("click",".outline-expander",function(e){var t=this.closest(".outline-item-wrapper").classList;return t.contains("outline-item-open")?t.remove("outline-item-open"):t.add("outline-item-open"),u(),!1}),e("click",".outline-item",function(e){var t=this.querySelector(".outline-label");location.hash="#"+t.getAttribute("href"),o&&((t=this.closest(".outline-item-wrapper").classList).contains("outline-item-open")||t.add("outline-item-open"),d(),t.add("outline-item-active"))});function s(){var e=a();r=null;for(var t=0;t<i.length&&i[t][1]-e<60;t++)r=i[t]}function n(){c=setTimeout(function(){var n;i=[],n=a(),document.querySelector("#write").querySelectorAll("h1, h2, h3, h4, h5, h6").forEach(e=>{var t=e.getAttribute("id");i.push([t,n+e.getBoundingClientRect().y])}),s(),u()},300)}var l,c,d=function(){document.querySelectorAll(".outline-item-active").forEach(e=>e.classList.remove("outline-item-active")),document.querySelectorAll(".outline-item-single.outline-item-open").forEach(e=>e.classList.remove("outline-item-open"))},u=function(){if(r&&(d(),t=document.querySelector('.outline-label[href="#'+(CSS.escape?CSS.escape(r[0]):r[0])+'"]')))if(o){var e=t.closest(".outline-item-open>ul>.outline-item-wrapper");if(e)e.classList.add("outline-item-active");else{for(var t,n=(t=t.closest(".outline-item-wrapper")).parentElement.closest(".outline-item-wrapper");n;)n=(t=n).parentElement.closest(".outline-item-wrapper");t.classList.add("outline-item-active")}}else t.closest(".outline-item-wrapper").classList.add("outline-item-active")};window.addEventListener("scroll",function(e){l&&clearTimeout(l),l=setTimeout(function(){s(),u()},300)});window.addEventListener("resize",function(e){c&&clearTimeout(c),n()}),n()})();</script></body></html>