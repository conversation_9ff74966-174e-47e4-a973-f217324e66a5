<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(A卷,100分)- 整理扑克牌（Java & JS & Python）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>给定一组数字&#xff0c;表示扑克牌的牌面数字&#xff0c;忽略扑克牌的花色&#xff0c;请按如下规则对这一组扑克牌进行整理&#xff1a;</p> 
<p><strong>步骤1.</strong> 对扑克牌进行分组&#xff0c;形成组合牌&#xff0c;规则如下&#xff1a;</p> 
<ul><li>当牌面数字相同张数大于等于4时&#xff0c;组合牌为“炸弹”&#xff1b;</li><li>3张相同牌面数字 &#43; 2张相同牌面数字&#xff0c;且3张牌与2张牌不相同时&#xff0c;组合牌为“葫芦”&#xff1b;</li><li>3张相同牌面数字&#xff0c;组合牌为“三张”&#xff1b;</li><li>2张相同牌面数字&#xff0c;组合牌为“对子”&#xff1b;</li><li>剩余没有相同的牌&#xff0c;则为“单张”&#xff1b;</li></ul> 
<p><strong>步骤2.</strong> 对上述组合牌进行由大到小排列&#xff0c;规则如下&#xff1a;</p> 
<ul><li>不同类型组合牌之间由大到小排列规则&#xff1a;“炸弹” &gt; “葫芦” &gt; “三张” &gt; “对子” &gt; “单张”&#xff1b;</li><li>相同类型组合牌之间&#xff0c;除“葫芦”外&#xff0c;按组合牌全部牌面数字加总由大到小排列&#xff1b;</li><li>“葫芦”则先按3张相同牌面数字加总由大到小排列&#xff0c;3张相同牌面数字加总相同时&#xff0c;再按另外2张牌面数字加总由大到小排列&#xff1b;</li><li>由于“葫芦”&gt;“三张”&#xff0c;因此如果能形成更大的组合牌&#xff0c;也可以将“三张”拆分为2张和1张&#xff0c;其中的2张可以和其它“三张”重新组合成“葫芦”&#xff0c;剩下的1张为“单张”</li></ul> 
<p><strong>步骤3. </strong>当存在多个可能组合方案时&#xff0c;按如下规则排序取最大的一个组合方案&#xff1a;</p> 
<ul><li>依次对组合方案中的组合牌进行大小比较&#xff0c;规则同上&#xff1b;</li><li>当组合方案A中的第n个组合牌大于组合方案B中的第n个组合牌时&#xff0c;组合方案A大于组合方案B&#xff1b;</li></ul> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>第一行为空格分隔的N个正整数&#xff0c;每个整数取值范围[1,13]&#xff0c;N的取值范围[1,1000]</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>经重新排列后的扑克牌数字列表&#xff0c;每个数字以空格分隔</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">1 3 3 3 2 1 5</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">3 3 3 1 1 5 2</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">无</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:85px;">输入</td><td style="width:413px;">4 4 2 1 2 1 3 3 3 4</td></tr><tr><td style="width:85px;">输出</td><td style="width:413px;">4 4 4 3 3 2 2 1 1 3</td></tr><tr><td style="width:85px;">说明</td><td style="width:413px;">无</td></tr></tbody></table> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>我的解题思路如下&#xff1a;</p> 
<p>首先&#xff0c;将给定牌中&#xff0c;炸弹&#xff0c;三张&#xff0c;对子&#xff0c;单子先统计出来&#xff0c;即先不处理葫芦。</p> 
<p>统计逻辑很简单&#xff0c;就是看某个牌面的数量&#xff1a;</p> 
<ul><li>&gt;&#61;4&#xff0c;那么该牌面可以组成炸弹</li><li>&#61;&#61;&#61;3&#xff0c;那么该牌面可以组成三张</li><li>&#61;&#61;&#61;2&#xff0c;那么该牌面可以组成对子</li><li>&#61;&#61;&#61;1&#xff0c;那么该牌面可以组成单张</li></ul> 
<p>统计完后&#xff0c;我们就可以先对炸弹进行排序&#xff0c;排序规则是&#xff1a;全部牌面数字加总由大到小排列</p> 
<p>接着可以组合葫芦了&#xff0c;组合逻辑如下&#xff1a;</p> 
<p>首先&#xff0c;需要先对三张、对子按照加总降序</p> 
<p>然后&#xff0c;选取一个最大的三张&#xff0c;并比较第二大的三张的牌面和第一大的对子的牌面</p> 
<ul><li>如果对子牌面大&#xff0c;则直接组合最大的三张和最大对子为忽略</li><li>如果第二大三张牌面大&#xff0c;则拆分该三张为一个对子&#xff0c;一个单张&#xff0c;其中对子和最大的三张组合成葫芦&#xff0c;单张加入前面统计的单张数组</li></ul> 
<p>按照上面规则组合葫芦&#xff0c;<span style="color:#fe2c24;">直到三张用完</span>。</p> 
<blockquote> 
 <p>注意上面逻辑是三张用完结束&#xff0c;而不是对子用完&#xff0c;因为还有一种情况就是对子先用完了&#xff0c;但是三张还有多个&#xff0c;此时我们要继续拆分小的三张来组合大三张为葫芦。</p> 
</blockquote> 
<p>组合完葫芦后。</p> 
<p>我们就可以对单张进行加总降序排序了&#xff0c;因为组合葫芦过程中&#xff0c;很可能产生新的单张。</p> 
<p>最后&#xff0c;依次将统计并排序后的炸弹、葫芦、三张、对子、单张&#xff0c;打印出来</p> 
<hr /> 
<p>2023.03.24 补充说明</p> 
<p>在看了满分答案后&#xff0c;我发现当前我的代码实现和满分答案的区别仅仅在于炸弹排序有所不同&#xff0c;比如下面用例&#xff1a;</p> 
<blockquote> 
 <p>5 5 5 5 4 4 4 4 4</p> 
</blockquote> 
<p>满分答案的输出是&#xff1a;4 4 4 4 4 5 5 5 5</p> 
<p>我的答案输出是&#xff1a;5 5 5 5 4 4 4 4 4</p> 
<p></p> 
<p>对满分答案经过分析后&#xff0c;我发现造成这个差异的原因是&#xff0c;我对题目中“加总”的理解有偏差。</p> 
<p>比如&#xff0c;斗地主时&#xff0c;炸弹的张数越多&#xff0c;炸弹越大&#xff0c;因此五个4 在现实中就是要比 四个5 的炸弹大。</p> 
<p>但是本题中“加总”这个词比较有迷惑性&#xff0c;我理解是 &#61; 牌面值 * 牌数量&#xff0c;即炸弹的所有牌之和&#xff0c;因此我的逻辑中 五个4 和 四个5 的炸弹是一样大的。</p> 
<p>而满分答案的对“加总”的理解和现实生活中一致&#xff0c;就是炸弹的牌数越多&#xff0c;就越大。</p> 
<p></p> 
<p>因此&#xff0c;本题代码修正非常容易&#xff0c;就是将炸弹的排序规则修改一下&#xff0c;先按照炸弹牌的牌数量降序&#xff0c;如果牌数量一致&#xff0c;则再按照牌面大小降序。</p> 
<p>比如下面代码中&#xff1a;</p> 
<p>JS的第49行</p> 
<p>Java的第58行</p> 
<p>Python的第37行</p> 
<p></p> 
<p>那么其他类型牌&#xff0c;比如葫芦&#xff0c;三张&#xff0c;对子&#xff0c;单张&#xff0c;的排序逻辑是否需要变动呢&#xff1f;</p> 
<p>答案是不需要&#xff0c;因为这些类型牌中的牌数量都是固定的&#xff0c;比如葫芦就是3&#43;2&#xff0c;三张就是3&#xff0c;对子就是2&#xff0c;单张就是1。因此这些同类型牌之间的对比就是牌面值得对比。可以沿用之前得逻辑。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.on(&#34;line&#34;, (line) &#61;&gt; {
  const arr &#61; line.split(&#34; &#34;).map(Number);
  console.log(getResult(arr));
});

function getResult(arr) {
  const card &#61; {};

  // 统计各种牌面的数量
  for (let num of arr) {
    card[num] ? card[num]&#43;&#43; : (card[num] &#61; 1);
  }

  // 统计组合&#xff0c;4代表炸弹&#xff0c;3&#43;2代表葫芦&#xff0c;3代表三张&#xff0c;2代表对子&#xff0c;1代表单张
  const combine &#61; {
    4: [],
    &#34;3&#43;2&#34;: [],
    3: [],
    2: [],
    1: [],
  };

  // 首先将初始组合统计出来
  for (let num in card) {
    switch (card[num]) {
      case 3:
        combine[3].push(num - 0);
        break;
      case 2:
        combine[2].push(num - 0);
        break;
      case 1:
        combine[1].push(num - 0);
        break;
      default:
        combine[4].push([num - 0, card[num]]); // 由于炸弹可能有4张以上相同牌面组成&#xff0c;因此既需要统计牌面num&#xff0c;也需要统计牌数card[num]
    }
  }

  // 炸弹排序
  combine[4].sort((a, b) &#61;&gt; (a[1] !&#61; b[1] ? b[1] - a[1] : b[0] - a[0]));

  // 三张排序&#xff0c;牌面值越大&#xff0c;三张越大
  combine[3].sort((a, b) &#61;&gt; b - a);

  // 对子降序&#xff0c;牌面值越大&#xff0c;对子越大
  combine[2].sort((a, b) &#61;&gt; b - a);

  // 尝试组合出葫芦
  while (combine[3].length) {
    // 如果对子用完&#xff0c;三张还有一个&#xff0c;那么可以直接结束循环
    if (combine[2].length &#61;&#61;&#61; 0 &amp;&amp; combine[3].length &#61;&#61;&#61; 1) break;

    // 选取一个最大的三张
    const san_top &#61; combine[3].shift();

    let tmp;
    // 如果第二大的三张的牌面&#xff0c;比最大的对子牌面大&#xff0c;或者没有对子了&#xff0c;则可以拆分三张&#xff0c;组合出葫芦
    if (
      combine[2].length &#61;&#61;&#61; 0 ||
      (combine[3].length &gt;&#61; 1 &amp;&amp; combine[3][0] &gt; combine[2][0])
    ) {
      tmp &#61; combine[3].shift();
      // 拆分三张为对子的话&#xff0c;会多出一个单张
      combine[1].push(tmp);
    } else {
      // 如果对子牌面比三张大&#xff0c;则不需要拆分三张&#xff0c;直接使用对子组合出葫芦
      tmp &#61; combine[2].shift();
    }
    combine[&#34;3&#43;2&#34;].push([san_top, tmp]); // 葫芦元素含义&#xff1a;[三张牌面&#xff0c;对子牌面]
  }

  // 处理完葫芦后&#xff0c;就可以对单张进行降序了&#xff08;因为组合葫芦的过程中&#xff0c;可能产生新的单张&#xff0c;因此单张排序要在葫芦组合得到后进行&#xff09;
  combine[1].sort((a, b) &#61;&gt; b - a);

  // ans存放题解
  const ans &#61; [];

  // 首先将炸弹放到ans中
  for (let card of combine[4]) {
    const [score, count] &#61; card;
    ans.push(...new Array(count).fill(score));
  }

  // 然后将葫芦放大ans中
  for (let card of combine[&#34;3&#43;2&#34;]) {
    const [san, er] &#61; card;
    ans.push(...new Array(3).fill(san), ...new Array(2).fill(er));
  }

  // 之后将三张放到ans中
  for (let san of combine[3]) {
    ans.push(...new Array(3).fill(san));
  }

  // 接着是对子放到ans中
  for (let er of combine[2]) {
    ans.push(...new Array(2).fill(er));
  }

  // 最后是单张放到ans中
  ans.push(...combine[1]);

  return ans.join(&#34; &#34;);
}
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.*;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    String str &#61; sc.nextLine();
    Integer[] arr &#61; Arrays.stream(str.split(&#34; &#34;)).map(Integer::parseInt).toArray(Integer[]::new);

    System.out.println(getResult(arr));
  }

  public static String getResult(Integer[] arr) {
    HashMap&lt;Integer, Integer&gt; card &#61; new HashMap&lt;&gt;();

    // 统计各种牌面的数量
    for (Integer num : arr) {
      if (card.containsKey(num)) {
        int val &#61; card.get(num);
        card.put(num, &#43;&#43;val);
      } else {
        card.put(num, 1);
      }
    }

    // 统计组合&#xff0c;4代表炸弹&#xff0c;3&#43;2代表葫芦&#xff0c;3代表三张&#xff0c;2代表对子&#xff0c;1代表单张
    HashMap&lt;String, LinkedList&lt;Integer[]&gt;&gt; combine &#61; new HashMap&lt;&gt;();
    combine.put(&#34;4&#34;, new LinkedList&lt;Integer[]&gt;());
    combine.put(&#34;3&#43;2&#34;, new LinkedList&lt;Integer[]&gt;());
    combine.put(&#34;3&#34;, new LinkedList&lt;Integer[]&gt;());
    combine.put(&#34;2&#34;, new LinkedList&lt;Integer[]&gt;());
    combine.put(&#34;1&#34;, new LinkedList&lt;Integer[]&gt;());

    // 首先将初始组合统计出来
    Set&lt;Integer&gt; cardKeys &#61; card.keySet();
    for (Integer num : cardKeys) {
      switch (card.get(num)) {
        case 3:
          combine.get(&#34;3&#34;).add(new Integer[] {num});
          break;
        case 2:
          combine.get(&#34;2&#34;).add(new Integer[] {num});
          break;
        case 1:
          combine.get(&#34;1&#34;).add(new Integer[] {num});
          break;
        default:
          combine
              .get(&#34;4&#34;)
              .add(
                  new Integer[] {
                    num, card.get(num)
                  }); // 由于炸弹可能有4张以上相同牌面组成&#xff0c;因此既需要统计牌面num&#xff0c;也需要统计牌数card[num]
      }
    }

    // 炸弹排序
    combine.get(&#34;4&#34;).sort((a, b) -&gt; !Objects.equals(a[1], b[1]) ? b[1] - a[1] : b[0] - a[0]);

    // 三张排序&#xff0c;牌面值越大&#xff0c;三张越大
    combine.get(&#34;3&#34;).sort((a, b) -&gt; b[0] - a[0]);

    // 对子排序&#xff0c;牌面值越大&#xff0c;对子越大
    combine.get(&#34;2&#34;).sort((a, b) -&gt; b[0] - a[0]);

    // 尝试组合出葫芦
    while (combine.get(&#34;3&#34;).size() &gt; 0) {
      // 如果对子用完&#xff0c;三张还有一个&#xff0c;那么可以直接结束循环
      if (combine.get(&#34;2&#34;).size() &#61;&#61; 0 &amp;&amp; combine.get(&#34;3&#34;).size() &#61;&#61; 1) break;

      // 否则&#xff0c;选取一个最大的三张
      Integer san_top &#61; combine.get(&#34;3&#34;).removeFirst()[0];

      Integer tmp;
      // 如果此时没有对子了&#xff0c;胡总和第二大的三张的牌面&#xff0c;比最大的对子牌面大&#xff0c;则可以拆分三张&#xff0c;组合出葫芦
      if (combine.get(&#34;2&#34;).size() &#61;&#61; 0
          || (combine.get(&#34;3&#34;).size() &gt; 0
              &amp;&amp; combine.get(&#34;3&#34;).get(0)[0] &gt; combine.get(&#34;2&#34;).get(0)[0])) {
        tmp &#61; combine.get(&#34;3&#34;).removeFirst()[0];
        // 拆分三张为对子的话&#xff0c;会多出一个单张
        combine.get(&#34;1&#34;).add(new Integer[] {tmp});
      } else {
        // 如果对子牌面比三张大&#xff0c;则不需要拆分三张&#xff0c;直接使用对子组合出葫芦
        tmp &#61; combine.get(&#34;2&#34;).removeFirst()[0];
      }
      combine.get(&#34;3&#43;2&#34;).add(new Integer[] {san_top, tmp}); // 葫芦元素含义&#xff1a;[三张牌面&#xff0c;对子牌面]
    }

    // 处理完葫芦后&#xff0c;就可以对单张进行降序了&#xff08;因为组合葫芦的过程中&#xff0c;可能产生新的单张&#xff0c;因此单张排序要在葫芦组合得到后进行&#xff09;
    combine.get(&#34;1&#34;).sort((a, b) -&gt; b[0] - a[0]);

    // ans存放题解
    ArrayList&lt;Integer&gt; ans &#61; new ArrayList&lt;&gt;();

    // 首先将炸弹放到ans中
    for (Integer[] vals : combine.get(&#34;4&#34;)) {
      int score &#61; vals[0];
      int count &#61; vals[1];
      for (int i &#61; 0; i &lt; count; i&#43;&#43;) {
        ans.add(score);
      }
    }

    // 然后将葫芦放大ans中
    for (Integer[] vals : combine.get(&#34;3&#43;2&#34;)) {
      int san &#61; vals[0];
      int er &#61; vals[1];
      for (int i &#61; 0; i &lt; 3; i&#43;&#43;) ans.add(san);
      for (int i &#61; 0; i &lt; 2; i&#43;&#43;) ans.add(er);
    }

    // 之后将三张放到ans中
    for (Integer[] vals : combine.get(&#34;3&#34;)) {
      for (int i &#61; 0; i &lt; 3; i&#43;&#43;) ans.add(vals[0]);
    }

    // 接着是对子放到ans中
    for (Integer[] vals : combine.get(&#34;2&#34;)) {
      for (int i &#61; 0; i &lt; 2; i&#43;&#43;) ans.add(vals[0]);
    }

    // 最后是单张放到ans中
    for (Integer[] vals : combine.get(&#34;1&#34;)) {
      ans.add(vals[0]);
    }

    StringJoiner sj &#61; new StringJoiner(&#34; &#34;);
    for (Integer an : ans) {
      sj.add(an &#43; &#34;&#34;);
    }

    return sj.toString();
  }
}
</code></pre> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
arr &#61; input().split()


# 算法入口
def getResult(arr):
    # card统计各种牌面的数量
    card &#61; {}
    for num in arr:
        if card.get(num) is None:
            card[num] &#61; 1
        else:
            card[num] &#43;&#61; 1

    # combine统计组合&#xff0c;4代表炸弹&#xff0c;3&#43;2代表葫芦&#xff0c;3代表三张&#xff0c;2代表对子&#xff0c;1代表单张
    combine &#61; {
        &#34;4&#34;: [],
        &#34;3&#43;2&#34;: [],
        &#34;3&#34;: [],
        &#34;2&#34;: [],
        &#34;1&#34;: []
    }

    # 首先将初始组合统计出来
    for num in card.keys():
        if card[num] &#61;&#61; 3:
            combine[&#34;3&#34;].append(int(num))
        elif card[num] &#61;&#61; 2:
            combine[&#34;2&#34;].append(int(num))
        elif card[num] &#61;&#61; 1:
            combine[&#34;1&#34;].append(int(num))
        else:
            # 由于炸弹可能有4张以上相同牌面组成&#xff0c;因此既需要统计牌面num&#xff0c;也需要统计牌数card[num]
            combine[&#34;4&#34;].append([int(num), card[num]])

    # 炸弹排序
    combine[&#34;4&#34;].sort(key&#61;lambda x: (-x[1], -x[0]))

    # 三张排序&#xff0c;牌面值越大&#xff0c;三张越大
    combine[&#34;3&#34;].sort(reverse&#61;True)

    # 对子降序&#xff0c;牌面值越大&#xff0c;对子越大
    combine[&#34;2&#34;].sort(reverse&#61;True)

    # 尝试组合出葫芦
    while len(combine[&#34;3&#34;]) &gt; 0:
        # 如果对子用完&#xff0c;三张还有一个&#xff0c;那么可以直接结束循环
        if len(combine[&#34;2&#34;]) &#61;&#61; 0 and len(combine[&#34;3&#34;]) &#61;&#61; 1:
            break

        # 选取一个最大的三张
        san_top &#61; combine[&#34;3&#34;].pop(0)

        tmp &#61; None

        #  如果第二大的三张的牌面&#xff0c;比最大的对子牌面大&#xff0c;或者没有对子了&#xff0c;则可以拆分三张&#xff0c;组合出葫芦
        if len(combine[&#34;2&#34;]) &#61;&#61; 0 or (len(combine[&#34;3&#34;]) &gt;&#61; 1 and combine[&#34;3&#34;][0] &gt; combine[&#34;2&#34;][0]):
            tmp &#61; combine[&#34;3&#34;].pop(0)
            # 拆分三张为对子的话&#xff0c;会多出一个单张
            combine[&#34;1&#34;].append(tmp)
        else:
            # 如果对子牌面比三张大&#xff0c;则不需要拆分三张&#xff0c;直接使用对子组合出葫芦
            tmp &#61; combine[&#34;2&#34;].pop(0)

        combine[&#34;3&#43;2&#34;].append([san_top, tmp])  # 葫芦元素含义&#xff1a;[三张牌面&#xff0c;对子牌面]

    # 处理完葫芦后&#xff0c;就可以对单张进行降序了&#xff08;因为组合葫芦的过程中&#xff0c;可能产生新的单张&#xff0c;因此单张排序要在葫芦组合得到后进行&#xff09;
    combine[&#34;1&#34;].sort(reverse&#61;True)

    # ans存放题解
    ans &#61; []

    # 首先将炸弹放到ans中
    for score, count in combine[&#34;4&#34;]:
        ans &#43;&#61; [score] * count

    # 然后将葫芦放大ans中
    for san, er in combine[&#34;3&#43;2&#34;]:
        ans &#43;&#61; [san] * 3 &#43; [er] * 2

    # 之后将三张放到ans中
    for san in combine[&#34;3&#34;]:
        ans &#43;&#61; [san] * 3

    # 接着是对子放到ans中
    for er in combine[&#34;2&#34;]:
        ans &#43;&#61; [er] * 2

    # 最后是单张放到ans中
    for dan in combine[&#34;1&#34;]:
        ans &#43;&#61; [dan]

    return &#34; &#34;.join(map(str, ans))


# 算法调用
print(getResult(arr))</code></pre>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>