<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box"><div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
             
                <div id="content_views" class="markdown_views prism-atom-one-light">
                    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
                        <path stroke-linecap="round" d="M5,0 0,2.5 5,5z" id="raphael-marker-block" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path>
                    </svg>
                    <h2><a name="t0"></a><a id="OD_0"></a>最新华为OD机试</h2> 

<h2><a name="t1"></a><a id="_4"></a>题目描述</h2> 
<p>智能手机方便了我们生活的同时，也侵占了我们不少的时间。“手机App防沉迷系统”能够让我们每天合理地规划手机App使用时间，在正确的时间做正确的事。</p> 
<p>它的大概原理是这样的：</p> 
<ol><li>在一天24小时内，可以注册每个App的允许使用时段</li><li>一个时间段只能使用一个App</li><li>App有优先级，数值越高，优先级越高。注册使用时段时，如果高优先级的App时间和低优先级的时段有冲突，则系统会自动注销低优先级的时段，如果App的优先级相同，则后添加的App不能注册。</li></ol> 
<p>请编程实现，根据输入数据注册App，并根据输入的时间点，返回时间点使用的App名称，如果该时间点没有注册任何App，请返回<a href="https://so.csdn.net/so/search?q=%E5%AD%97%E7%AC%A6%E4%B8%B2&amp;spm=1001.2101.3001.7020" target="_blank" class="hl hl-1" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7020&quot;,&quot;dest&quot;:&quot;https://so.csdn.net/so/search?q=%E5%AD%97%E7%AC%A6%E4%B8%B2&amp;spm=1001.2101.3001.7020&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;字符串\&quot;}&quot;}" data-tit="字符串" data-pretit="字符串">字符串</a>“NA”。</p> 
<h2><a name="t2"></a><a id="_16"></a>输入描述</h2> 
<p>第一行表示注册的App数量 N（N ≤ 100）</p> 
<p>第二部分包括 N 行，每行表示一条App注册数据</p> 
<p>最后一行输入一个时间点，程序即返回该时间点使用的App</p> 
<blockquote> 
 <p>2<br> App1 1 09:00 10:00<br> App2 2 11:00 11:30<br> 09:30</p> 
</blockquote> 
<p>数据说明如下：</p> 
<ol><li>N行注册数据以空格分隔，四项数依次表示：App名称、优先级、起始时间、结束时间</li><li>优先级1~5，数字越大，优先级越高</li><li>时间格式 HH:MM，小时和分钟都是两位，不足两位前面补0</li><li>起始时间需小于结束时间，否则注册不上</li><li>注册信息中的时间段包含起始时间点，不包含结束时间点</li></ol> 
<h2><a name="t3"></a><a id="_38"></a>输出描述</h2> 
<p>输出一个字符串，表示App名称，或NA表示空闲时间</p> 
<h2><a name="t4"></a><a id="1_42"></a>示例1</h2> 
<p>输入</p> 
<pre data-index="0" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">1
App1 1 09:00 10:00
09:30
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li></ul></pre> 
<p>输出</p> 
<pre data-index="1" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">App1
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<blockquote> 
 <p>App1注册在9点到10点间，9点半可用的应用名是App1</p> 
</blockquote> 
<h2><a name="t5"></a><a id="2_62"></a>示例2</h2> 
<p>输入</p> 
<pre data-index="2" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">2
App1 1 09:00 10:00
App2 2 09:10 09:30
09:20
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li></ul></pre> 
<p>输出</p> 
<pre data-index="3" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">App2
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<blockquote> 
 <p>APP1和App2的时段有冲突，App2优先级高，注册App2之后，App1自动注销，因此输出App2。</p> 
</blockquote> 
<h2><a name="t6"></a><a id="3_80"></a>示例3</h2> 
<p>输入</p> 
<pre data-index="4" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">2
App1 1 09:00 10:00
App2 2 09:10 09:30
09:50
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li></ul></pre> 
<p>输出</p> 
<pre data-index="5" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">NA
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<blockquote></blockquote> 
<h2><a name="t7"></a><a id="_102"></a>解题思路</h2> 
<h2><a name="t8"></a><a id="Java_108"></a>Java</h2> 
<pre data-index="6" class="set-code-hide prettyprint"><code class="prism language-java has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token class-name">ArrayList</span></span><span class="token punctuation">;</span>
<span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token class-name">Scanner</span></span><span class="token punctuation">;</span>

<span class="token keyword">public</span> <span class="token keyword">class</span> <span class="token class-name">Main</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 定义App类，用于存储App的相关信息</span>
    <span class="token keyword">static</span> <span class="token keyword">class</span> <span class="token class-name">App</span> <span class="token punctuation">{<!-- --></span>
        <span class="token class-name">String</span> name<span class="token punctuation">;</span> <span class="token comment">// App名称</span>
        <span class="token keyword">int</span> priority<span class="token punctuation">;</span> <span class="token comment">// App优先级</span>
        <span class="token keyword">int</span> startTime<span class="token punctuation">;</span> <span class="token comment">// App允许使用的起始时间（以分钟为单位）</span>
        <span class="token keyword">int</span> endTime<span class="token punctuation">;</span> <span class="token comment">// App允许使用的结束时间（以分钟为单位）</span>

        <span class="token comment">// App类的构造函数，用于创建App对象</span>
        <span class="token keyword">public</span> <span class="token class-name">App</span><span class="token punctuation">(</span><span class="token class-name">String</span> name<span class="token punctuation">,</span> <span class="token keyword">int</span> priority<span class="token punctuation">,</span> <span class="token keyword">int</span> startTime<span class="token punctuation">,</span> <span class="token keyword">int</span> endTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">this</span><span class="token punctuation">.</span>name <span class="token operator">=</span> name<span class="token punctuation">;</span>
            <span class="token keyword">this</span><span class="token punctuation">.</span>priority <span class="token operator">=</span> priority<span class="token punctuation">;</span>
            <span class="token keyword">this</span><span class="token punctuation">.</span>startTime <span class="token operator">=</span> startTime<span class="token punctuation">;</span>
            <span class="token keyword">this</span><span class="token punctuation">.</span>endTime <span class="token operator">=</span> endTime<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 主函数</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> args<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token class-name">Scanner</span> sc <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Scanner</span><span class="token punctuation">(</span><span class="token class-name">System</span><span class="token punctuation">.</span>in<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 创建Scanner对象，用于读取标准输入</span>
        <span class="token keyword">int</span> n <span class="token operator">=</span> sc<span class="token punctuation">.</span><span class="token function">nextInt</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取App数量</span>

        <span class="token class-name">ArrayList</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">App</span><span class="token punctuation">&gt;</span></span> apps <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">ArrayList</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token punctuation">&gt;</span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 创建App列表，用于存储所有App</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token comment">// 循环读取每个App的信息，并创建App对象添加到列表中</span>
            <span class="token class-name">String</span> appName <span class="token operator">=</span> sc<span class="token punctuation">.</span><span class="token function">next</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword">int</span> appPriority <span class="token operator">=</span> sc<span class="token punctuation">.</span><span class="token function">nextInt</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword">int</span> appStartTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>sc<span class="token punctuation">.</span><span class="token function">next</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword">int</span> appEndTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>sc<span class="token punctuation">.</span><span class="token function">next</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            apps<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span><span class="token keyword">new</span> <span class="token class-name">App</span><span class="token punctuation">(</span>appName<span class="token punctuation">,</span> appPriority<span class="token punctuation">,</span> appStartTime<span class="token punctuation">,</span> appEndTime<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token keyword">int</span> queryTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>sc<span class="token punctuation">.</span><span class="token function">next</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取查询时间，并转换为分钟</span>
        <span class="token class-name">String</span> appAtTime <span class="token operator">=</span> <span class="token string">"NA"</span><span class="token punctuation">;</span> <span class="token comment">// 初始化查询时间对应的App名称为"NA"</span>

        <span class="token comment">// 创建已注册App列表</span>
        <span class="token class-name">ArrayList</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">App</span><span class="token punctuation">&gt;</span></span> registeredApps <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">ArrayList</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token punctuation">&gt;</span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token class-name">App</span> app <span class="token operator">:</span> apps<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>app<span class="token punctuation">.</span>startTime <span class="token operator">&gt;=</span> app<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span> <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果起始时间不小于结束时间，则跳过</span>

            <span class="token comment">// 遍历已注册的App列表，检查时间冲突</span>
            <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> registeredApps<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">;</span> i <span class="token operator">&gt;=</span> <span class="token number">0</span><span class="token punctuation">;</span> i<span class="token operator">--</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token class-name">App</span> registered <span class="token operator">=</span> registeredApps<span class="token punctuation">.</span><span class="token function">get</span><span class="token punctuation">(</span>i<span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token comment">// 如果存在时间冲突</span>
                <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token class-name">Math</span><span class="token punctuation">.</span><span class="token function">max</span><span class="token punctuation">(</span>app<span class="token punctuation">.</span>startTime<span class="token punctuation">,</span> registered<span class="token punctuation">.</span>startTime<span class="token punctuation">)</span> <span class="token operator">&lt;</span> <span class="token class-name">Math</span><span class="token punctuation">.</span><span class="token function">min</span><span class="token punctuation">(</span>app<span class="token punctuation">.</span>endTime<span class="token punctuation">,</span> registered<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                    <span class="token comment">// 如果当前App的优先级高于已注册App的优先级</span>
                    <span class="token keyword">if</span> <span class="token punctuation">(</span>app<span class="token punctuation">.</span>priority <span class="token operator">&gt;</span> registered<span class="token punctuation">.</span>priority<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                        registeredApps<span class="token punctuation">.</span><span class="token function">remove</span><span class="token punctuation">(</span>i<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 注销低优先级的App</span>
                    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{<!-- --></span>
                        <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果优先级不高，继续检查下一个已注册App</span>
                    <span class="token punctuation">}</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>

            <span class="token comment">// 将当前App添加到已注册App列表中</span>
            registeredApps<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span>app<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// 遍历已注册App列表，找到查询时间对应的App</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token class-name">App</span> app <span class="token operator">:</span> registeredApps<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>queryTime <span class="token operator">&gt;=</span> app<span class="token punctuation">.</span>startTime <span class="token operator">&amp;&amp;</span> queryTime <span class="token operator">&lt;</span> app<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                appAtTime <span class="token operator">=</span> app<span class="token punctuation">.</span>name<span class="token punctuation">;</span> <span class="token comment">// 更新查询时间对应的App名称</span>
                <span class="token keyword">break</span><span class="token punctuation">;</span> <span class="token comment">// 找到后退出循环</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        <span class="token class-name">System</span><span class="token punctuation">.</span>out<span class="token punctuation">.</span><span class="token function">println</span><span class="token punctuation">(</span>appAtTime<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 输出查询时间对应的App名称</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 时间转换函数，将时间字符串转换为以分钟为单位的整数</span>
    <span class="token keyword">private</span> <span class="token keyword">static</span> <span class="token keyword">int</span> <span class="token function">convertTime</span><span class="token punctuation">(</span><span class="token class-name">String</span> time<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> parts <span class="token operator">=</span> time<span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">":"</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 将时间字符串按照":"分割</span>
        <span class="token keyword">return</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token function">parseInt</span><span class="token punctuation">(</span>parts<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">*</span> <span class="token number">60</span> <span class="token operator">+</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token function">parseInt</span><span class="token punctuation">(</span>parts<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 将小时和分钟转换为分钟</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li></ul></pre> 
<h2><a name="t9"></a><a id="Python_192"></a>Python</h2> 
<pre data-index="7" class="set-code-hide prettyprint"><code class="prism language-python has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">class</span> <span class="token class-name">App</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""定义App类，用于存储App的相关信息"""</span>

    <span class="token keyword">def</span> <span class="token function">__init__</span><span class="token punctuation">(</span>self<span class="token punctuation">,</span> name<span class="token punctuation">,</span> priority<span class="token punctuation">,</span> start_time<span class="token punctuation">,</span> end_time<span class="token punctuation">)</span><span class="token punctuation">:</span>
        self<span class="token punctuation">.</span>name <span class="token operator">=</span> name  <span class="token comment"># App名称</span>
        self<span class="token punctuation">.</span>priority <span class="token operator">=</span> priority  <span class="token comment"># App优先级</span>
        self<span class="token punctuation">.</span>start_time <span class="token operator">=</span> start_time  <span class="token comment"># App允许使用的起始时间（以分钟为单位）</span>
        self<span class="token punctuation">.</span>end_time <span class="token operator">=</span> end_time  <span class="token comment"># App允许使用的结束时间（以分钟为单位）</span>

<span class="token keyword">def</span> <span class="token function">convert_time</span><span class="token punctuation">(</span>time_str<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""
    时间转换函数，将时间字符串转换为以分钟为单位的整数
    :param time_str: 时间字符串，格式为"小时:分钟"
    :return: 转换后的分钟数
    """</span>
    hours<span class="token punctuation">,</span> minutes <span class="token operator">=</span> <span class="token builtin">map</span><span class="token punctuation">(</span><span class="token builtin">int</span><span class="token punctuation">,</span> time_str<span class="token punctuation">.</span>split<span class="token punctuation">(</span><span class="token string">":"</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment"># 将时间字符串按照":"分割并转换为整数</span>
    <span class="token keyword">return</span> hours <span class="token operator">*</span> <span class="token number">60</span> <span class="token operator">+</span> minutes  <span class="token comment"># 将小时和分钟转换为分钟</span>

<span class="token keyword">def</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    n <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span><span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment"># 读取App数量</span>
    apps <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span>  <span class="token comment"># 创建App列表，用于存储所有App</span>

    <span class="token keyword">for</span> _ <span class="token keyword">in</span> <span class="token builtin">range</span><span class="token punctuation">(</span>n<span class="token punctuation">)</span><span class="token punctuation">:</span>
        <span class="token comment"># 循环读取每个App的信息，并创建App对象添加到列表中</span>
        app_name<span class="token punctuation">,</span> app_priority<span class="token punctuation">,</span> app_start_time<span class="token punctuation">,</span> app_end_time <span class="token operator">=</span> <span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span>split<span class="token punctuation">(</span><span class="token punctuation">)</span>
        app_priority <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span>app_priority<span class="token punctuation">)</span>
        app_start_time <span class="token operator">=</span> convert_time<span class="token punctuation">(</span>app_start_time<span class="token punctuation">)</span>
        app_end_time <span class="token operator">=</span> convert_time<span class="token punctuation">(</span>app_end_time<span class="token punctuation">)</span>
        apps<span class="token punctuation">.</span>append<span class="token punctuation">(</span>App<span class="token punctuation">(</span>app_name<span class="token punctuation">,</span> app_priority<span class="token punctuation">,</span> app_start_time<span class="token punctuation">,</span> app_end_time<span class="token punctuation">)</span><span class="token punctuation">)</span>

    query_time <span class="token operator">=</span> convert_time<span class="token punctuation">(</span><span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment"># 读取查询时间，并转换为分钟</span>
    app_at_time <span class="token operator">=</span> <span class="token string">"NA"</span>  <span class="token comment"># 初始化查询时间对应的App名称为"NA"</span>

    <span class="token comment"># 创建已注册App列表</span>
    registered_apps <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span>
    <span class="token keyword">for</span> app <span class="token keyword">in</span> apps<span class="token punctuation">:</span>
        <span class="token keyword">if</span> app<span class="token punctuation">.</span>start_time <span class="token operator">&gt;=</span> app<span class="token punctuation">.</span>end_time<span class="token punctuation">:</span>
            <span class="token keyword">continue</span>  <span class="token comment"># 如果起始时间不小于结束时间，则跳过</span>

        <span class="token comment"># 遍历已注册的App列表，检查时间冲突</span>
        <span class="token keyword">for</span> i <span class="token keyword">in</span> <span class="token builtin">range</span><span class="token punctuation">(</span><span class="token builtin">len</span><span class="token punctuation">(</span>registered_apps<span class="token punctuation">)</span> <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">,</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
            registered <span class="token operator">=</span> registered_apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span>
            <span class="token comment"># 如果存在时间冲突</span>
            <span class="token keyword">if</span> <span class="token builtin">max</span><span class="token punctuation">(</span>app<span class="token punctuation">.</span>start_time<span class="token punctuation">,</span> registered<span class="token punctuation">.</span>start_time<span class="token punctuation">)</span> <span class="token operator">&lt;</span> <span class="token builtin">min</span><span class="token punctuation">(</span>app<span class="token punctuation">.</span>end_time<span class="token punctuation">,</span> registered<span class="token punctuation">.</span>end_time<span class="token punctuation">)</span><span class="token punctuation">:</span>
                <span class="token comment"># 如果当前App的优先级高于已注册App的优先级</span>
                <span class="token keyword">if</span> app<span class="token punctuation">.</span>priority <span class="token operator">&gt;</span> registered<span class="token punctuation">.</span>priority<span class="token punctuation">:</span>
                    registered_apps<span class="token punctuation">.</span>pop<span class="token punctuation">(</span>i<span class="token punctuation">)</span>  <span class="token comment"># 注销低优先级的App</span>
                <span class="token keyword">else</span><span class="token punctuation">:</span>
                    <span class="token keyword">continue</span>  <span class="token comment"># 如果优先级不高，继续检查下一个已注册App</span>

        <span class="token comment"># 将当前App添加到已注册App列表中</span>
        registered_apps<span class="token punctuation">.</span>append<span class="token punctuation">(</span>app<span class="token punctuation">)</span>

    <span class="token comment"># 遍历已注册App列表，找到查询时间对应的App</span>
    <span class="token keyword">for</span> app <span class="token keyword">in</span> registered_apps<span class="token punctuation">:</span>
        <span class="token keyword">if</span> query_time <span class="token operator">&gt;=</span> app<span class="token punctuation">.</span>start_time <span class="token keyword">and</span> query_time <span class="token operator">&lt;</span> app<span class="token punctuation">.</span>end_time<span class="token punctuation">:</span>
            app_at_time <span class="token operator">=</span> app<span class="token punctuation">.</span>name  <span class="token comment"># 更新查询时间对应的App名称</span>
            <span class="token keyword">break</span>  <span class="token comment"># 找到后退出循环</span>

    <span class="token keyword">print</span><span class="token punctuation">(</span>app_at_time<span class="token punctuation">)</span>  <span class="token comment"># 输出查询时间对应的App名称</span>

<span class="token keyword">if</span> __name__ <span class="token operator">==</span> <span class="token string">"__main__"</span><span class="token punctuation">:</span>
    main<span class="token punctuation">(</span><span class="token punctuation">)</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li></ul></pre> 
<h2><a name="t10"></a><a id="JavaScript_260"></a>JavaScript</h2> 
<pre data-index="8" class="set-code-hide prettyprint"><code class="prism language-js has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">const</span> readline <span class="token operator">=</span> <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">'readline'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 定义App类，用于存储App的相关信息</span>
<span class="token keyword">class</span> <span class="token class-name">App</span> <span class="token punctuation">{<!-- --></span>
  <span class="token function">constructor</span><span class="token punctuation">(</span><span class="token parameter">name<span class="token punctuation">,</span> priority<span class="token punctuation">,</span> startTime<span class="token punctuation">,</span> endTime</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">this</span><span class="token punctuation">.</span>name <span class="token operator">=</span> name<span class="token punctuation">;</span> <span class="token comment">// App名称</span>
    <span class="token keyword">this</span><span class="token punctuation">.</span>priority <span class="token operator">=</span> priority<span class="token punctuation">;</span> <span class="token comment">// App优先级</span>
    <span class="token keyword">this</span><span class="token punctuation">.</span>startTime <span class="token operator">=</span> startTime<span class="token punctuation">;</span> <span class="token comment">// App允许使用的起始时间（以分钟为单位）</span>
    <span class="token keyword">this</span><span class="token punctuation">.</span>endTime <span class="token operator">=</span> endTime<span class="token punctuation">;</span> <span class="token comment">// App允许使用的结束时间（以分钟为单位）</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 创建readline接口实例</span>
<span class="token keyword">const</span> rl <span class="token operator">=</span> readline<span class="token punctuation">.</span><span class="token function">createInterface</span><span class="token punctuation">(</span><span class="token punctuation">{<!-- --></span>
  <span class="token literal-property property">input</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdin<span class="token punctuation">,</span>
  <span class="token literal-property property">output</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdout
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 用于存储输入行的数组</span>
<span class="token keyword">const</span> lines <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
<span class="token comment">// 读取输入</span>
rl<span class="token punctuation">.</span><span class="token function">on</span><span class="token punctuation">(</span><span class="token string">'line'</span><span class="token punctuation">,</span> <span class="token punctuation">(</span><span class="token parameter">line</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{<!-- --></span>
  lines<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>line<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">on</span><span class="token punctuation">(</span><span class="token string">'close'</span><span class="token punctuation">,</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{<!-- --></span>
  <span class="token comment">// 当输入完成后开始处理数据</span>
  <span class="token function">processInput</span><span class="token punctuation">(</span>lines<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 处理输入数据的函数</span>
<span class="token keyword">function</span> <span class="token function">processInput</span><span class="token punctuation">(</span><span class="token parameter">lines</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
  <span class="token keyword">const</span> n <span class="token operator">=</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>lines<span class="token punctuation">.</span><span class="token function">shift</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取App数量</span>
  <span class="token keyword">const</span> apps <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">;</span> <span class="token comment">// 创建App列表，用于存储所有App</span>

  <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">let</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 循环读取每个App的信息，并创建App对象添加到列表中</span>
    <span class="token keyword">const</span> <span class="token punctuation">[</span>appName<span class="token punctuation">,</span> appPriority<span class="token punctuation">,</span> appStartTime<span class="token punctuation">,</span> appEndTime<span class="token punctuation">]</span> <span class="token operator">=</span> lines<span class="token punctuation">.</span><span class="token function">shift</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">' '</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    apps<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span><span class="token keyword">new</span> <span class="token class-name">App</span><span class="token punctuation">(</span>appName<span class="token punctuation">,</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>appPriority<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>appStartTime<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>appEndTime<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token keyword">const</span> queryTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>lines<span class="token punctuation">.</span><span class="token function">shift</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取查询时间，并转换为分钟</span>
  <span class="token keyword">let</span> appAtTime <span class="token operator">=</span> <span class="token string">"NA"</span><span class="token punctuation">;</span> <span class="token comment">// 初始化查询时间对应的App名称为"NA"</span>

  <span class="token comment">// 创建已注册App列表</span>
  <span class="token keyword">const</span> registeredApps <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
  <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">const</span> app <span class="token keyword">of</span> apps<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>app<span class="token punctuation">.</span>startTime <span class="token operator">&gt;=</span> app<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span> <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果起始时间不小于结束时间，则跳过</span>

    <span class="token comment">// 遍历已注册的App列表，检查时间冲突</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">let</span> i <span class="token operator">=</span> registeredApps<span class="token punctuation">.</span>length <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">;</span> i <span class="token operator">&gt;=</span> <span class="token number">0</span><span class="token punctuation">;</span> i<span class="token operator">--</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
      <span class="token keyword">const</span> registered <span class="token operator">=</span> registeredApps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>
      <span class="token comment">// 如果存在时间冲突</span>
      <span class="token keyword">if</span> <span class="token punctuation">(</span>Math<span class="token punctuation">.</span><span class="token function">max</span><span class="token punctuation">(</span>app<span class="token punctuation">.</span>startTime<span class="token punctuation">,</span> registered<span class="token punctuation">.</span>startTime<span class="token punctuation">)</span> <span class="token operator">&lt;</span> Math<span class="token punctuation">.</span><span class="token function">min</span><span class="token punctuation">(</span>app<span class="token punctuation">.</span>endTime<span class="token punctuation">,</span> registered<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 如果当前App的优先级高于已注册App的优先级</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>app<span class="token punctuation">.</span>priority <span class="token operator">&gt;</span> registered<span class="token punctuation">.</span>priority<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
          registeredApps<span class="token punctuation">.</span><span class="token function">splice</span><span class="token punctuation">(</span>i<span class="token punctuation">,</span> <span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 注销低优先级的App</span>
        <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{<!-- --></span>
          <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果优先级不高，继续检查下一个已注册App</span>
        <span class="token punctuation">}</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 将当前App添加到已注册App列表中</span>
    registeredApps<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>app<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token comment">// 遍历已注册App列表，找到查询时间对应的App</span>
  <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">const</span> app <span class="token keyword">of</span> registeredApps<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>queryTime <span class="token operator">&gt;=</span> app<span class="token punctuation">.</span>startTime <span class="token operator">&amp;&amp;</span> queryTime <span class="token operator">&lt;</span> app<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
      appAtTime <span class="token operator">=</span> app<span class="token punctuation">.</span>name<span class="token punctuation">;</span> <span class="token comment">// 更新查询时间对应的App名称</span>
      <span class="token keyword">break</span><span class="token punctuation">;</span> <span class="token comment">// 找到后退出循环</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>

  console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>appAtTime<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 输出查询时间对应的App名称</span>
<span class="token punctuation">}</span>

<span class="token comment">// 时间转换函数，将时间字符串转换为以分钟为单位的整数</span>
<span class="token keyword">function</span> <span class="token function">convertTime</span><span class="token punctuation">(</span><span class="token parameter">time</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
  <span class="token keyword">const</span> <span class="token punctuation">[</span>hours<span class="token punctuation">,</span> minutes<span class="token punctuation">]</span> <span class="token operator">=</span> time<span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">':'</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">(</span>Number<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 将时间字符串按照":"分割并转换为数字</span>
  <span class="token keyword">return</span> hours <span class="token operator">*</span> <span class="token number">60</span> <span class="token operator">+</span> minutes<span class="token punctuation">;</span> <span class="token comment">// 将小时和分钟转换为分钟</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li><li style="color: rgb(153, 153, 153);">80</li><li style="color: rgb(153, 153, 153);">81</li><li style="color: rgb(153, 153, 153);">82</li></ul></pre> 
<h2><a name="t11"></a><a id="C_348"></a>C++</h2> 
<pre data-index="9" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;iostream&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;vector&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;string&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;sstream&gt;</span></span>

<span class="token comment">// 定义App类，用于存储App的相关信息</span>
class App <span class="token punctuation">{<!-- --></span>
public<span class="token operator">:</span>
    std<span class="token operator">::</span>string name<span class="token punctuation">;</span> <span class="token comment">// App名称</span>
    <span class="token keyword">int</span> priority<span class="token punctuation">;</span> <span class="token comment">// App优先级</span>
    <span class="token keyword">int</span> startTime<span class="token punctuation">;</span> <span class="token comment">// App允许使用的起始时间（以分钟为单位）</span>
    <span class="token keyword">int</span> endTime<span class="token punctuation">;</span> <span class="token comment">// App允许使用的结束时间（以分钟为单位）</span>

    <span class="token comment">// App类的构造函数，用于创建App对象</span>
    <span class="token function">App</span><span class="token punctuation">(</span>std<span class="token operator">::</span>string name<span class="token punctuation">,</span> <span class="token keyword">int</span> priority<span class="token punctuation">,</span> <span class="token keyword">int</span> startTime<span class="token punctuation">,</span> <span class="token keyword">int</span> endTime<span class="token punctuation">)</span>
        <span class="token operator">:</span> <span class="token function">name</span><span class="token punctuation">(</span>name<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">priority</span><span class="token punctuation">(</span>priority<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">startTime</span><span class="token punctuation">(</span>startTime<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">endTime</span><span class="token punctuation">(</span>endTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span><span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// 时间转换函数，将时间字符串转换为以分钟为单位的整数</span>
<span class="token keyword">int</span> <span class="token function">convertTime</span><span class="token punctuation">(</span><span class="token keyword">const</span> std<span class="token operator">::</span>string<span class="token operator">&amp;</span> time<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> hours<span class="token punctuation">,</span> minutes<span class="token punctuation">;</span>
    <span class="token keyword">char</span> colon<span class="token punctuation">;</span>
    std<span class="token operator">::</span>istringstream <span class="token function">iss</span><span class="token punctuation">(</span>time<span class="token punctuation">)</span><span class="token punctuation">;</span>
    iss <span class="token operator">&gt;&gt;</span> hours <span class="token operator">&gt;&gt;</span> colon <span class="token operator">&gt;&gt;</span> minutes<span class="token punctuation">;</span> <span class="token comment">// 将时间字符串按照":"分割并转换为小时和分钟</span>
    <span class="token keyword">return</span> hours <span class="token operator">*</span> <span class="token number">60</span> <span class="token operator">+</span> minutes<span class="token punctuation">;</span> <span class="token comment">// 将小时和分钟转换为分钟</span>
<span class="token punctuation">}</span>

<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> n<span class="token punctuation">;</span> <span class="token comment">// 读取App数量</span>
    std<span class="token operator">::</span>cin <span class="token operator">&gt;&gt;</span> n<span class="token punctuation">;</span>

    std<span class="token operator">::</span>vector<span class="token operator">&lt;</span>App<span class="token operator">&gt;</span> apps<span class="token punctuation">;</span> <span class="token comment">// 创建App列表，用于存储所有App</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 循环读取每个App的信息，并创建App对象添加到列表中</span>
        std<span class="token operator">::</span>string appName<span class="token punctuation">;</span>
        <span class="token keyword">int</span> appPriority<span class="token punctuation">,</span> appStartTime<span class="token punctuation">,</span> appEndTime<span class="token punctuation">;</span>
        std<span class="token operator">::</span>string startTimeStr<span class="token punctuation">,</span> endTimeStr<span class="token punctuation">;</span>
        std<span class="token operator">::</span>cin <span class="token operator">&gt;&gt;</span> appName <span class="token operator">&gt;&gt;</span> appPriority <span class="token operator">&gt;&gt;</span> startTimeStr <span class="token operator">&gt;&gt;</span> endTimeStr<span class="token punctuation">;</span>
        appStartTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>startTimeStr<span class="token punctuation">)</span><span class="token punctuation">;</span>
        appEndTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>endTimeStr<span class="token punctuation">)</span><span class="token punctuation">;</span>
        apps<span class="token punctuation">.</span><span class="token function">emplace_back</span><span class="token punctuation">(</span>appName<span class="token punctuation">,</span> appPriority<span class="token punctuation">,</span> appStartTime<span class="token punctuation">,</span> appEndTime<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    std<span class="token operator">::</span>string queryTimeStr<span class="token punctuation">;</span>
    std<span class="token operator">::</span>cin <span class="token operator">&gt;&gt;</span> queryTimeStr<span class="token punctuation">;</span>
    <span class="token keyword">int</span> queryTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>queryTimeStr<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取查询时间，并转换为分钟</span>
    std<span class="token operator">::</span>string appAtTime <span class="token operator">=</span> <span class="token string">"NA"</span><span class="token punctuation">;</span> <span class="token comment">// 初始化查询时间对应的App名称为"NA"</span>

    std<span class="token operator">::</span>vector<span class="token operator">&lt;</span>App<span class="token operator">&gt;</span> registeredApps<span class="token punctuation">;</span> <span class="token comment">// 创建已注册App列表</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">const</span> App<span class="token operator">&amp;</span> app <span class="token operator">:</span> apps<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>app<span class="token punctuation">.</span>startTime <span class="token operator">&gt;=</span> app<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span> <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果起始时间不小于结束时间，则跳过</span>

        <span class="token comment">// 遍历已注册的App列表，检查时间冲突</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> registeredApps<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">;</span> i <span class="token operator">&gt;=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token operator">--</span>i<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">const</span> App<span class="token operator">&amp;</span> registered <span class="token operator">=</span> registeredApps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>
            <span class="token comment">// 如果存在时间冲突</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>std<span class="token operator">::</span><span class="token function">max</span><span class="token punctuation">(</span>app<span class="token punctuation">.</span>startTime<span class="token punctuation">,</span> registered<span class="token punctuation">.</span>startTime<span class="token punctuation">)</span> <span class="token operator">&lt;</span> std<span class="token operator">::</span><span class="token function">min</span><span class="token punctuation">(</span>app<span class="token punctuation">.</span>endTime<span class="token punctuation">,</span> registered<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token comment">// 如果当前App的优先级高于已注册App的优先级</span>
                <span class="token keyword">if</span> <span class="token punctuation">(</span>app<span class="token punctuation">.</span>priority <span class="token operator">&gt;</span> registered<span class="token punctuation">.</span>priority<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                    registeredApps<span class="token punctuation">.</span><span class="token function">erase</span><span class="token punctuation">(</span>registeredApps<span class="token punctuation">.</span><span class="token function">begin</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">+</span> i<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 注销低优先级的App</span>
                <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{<!-- --></span>
                    <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果优先级不高，继续检查下一个已注册App</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// 将当前App添加到已注册App列表中</span>
        registeredApps<span class="token punctuation">.</span><span class="token function">push_back</span><span class="token punctuation">(</span>app<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 遍历已注册App列表，找到查询时间对应的App</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">const</span> App<span class="token operator">&amp;</span> app <span class="token operator">:</span> registeredApps<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>queryTime <span class="token operator">&gt;=</span> app<span class="token punctuation">.</span>startTime <span class="token operator">&amp;&amp;</span> queryTime <span class="token operator">&lt;</span> app<span class="token punctuation">.</span>endTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            appAtTime <span class="token operator">=</span> app<span class="token punctuation">.</span>name<span class="token punctuation">;</span> <span class="token comment">// 更新查询时间对应的App名称</span>
            <span class="token keyword">break</span><span class="token punctuation">;</span> <span class="token comment">// 找到后退出循环</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    std<span class="token operator">::</span>cout <span class="token operator">&lt;&lt;</span> appAtTime <span class="token operator">&lt;&lt;</span> std<span class="token operator">::</span>endl<span class="token punctuation">;</span> <span class="token comment">// 输出查询时间对应的App名称</span>

    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li><li style="color: rgb(153, 153, 153);">80</li><li style="color: rgb(153, 153, 153);">81</li><li style="color: rgb(153, 153, 153);">82</li><li style="color: rgb(153, 153, 153);">83</li></ul></pre> 
<h2><a name="t12"></a><a id="C_436"></a>C语言</h2> 
<pre data-index="10" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdio.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdlib.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;string.h&gt;</span></span>

<span class="token comment">// 定义App结构体，用于存储App的相关信息</span>
<span class="token keyword">typedef</span> <span class="token keyword">struct</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">char</span> name<span class="token punctuation">[</span><span class="token number">30</span><span class="token punctuation">]</span><span class="token punctuation">;</span> <span class="token comment">// App名称</span>
    <span class="token keyword">int</span> priority<span class="token punctuation">;</span>  <span class="token comment">// App优先级</span>
    <span class="token keyword">int</span> startTime<span class="token punctuation">;</span> <span class="token comment">// App允许使用的起始时间（以分钟为单位）</span>
    <span class="token keyword">int</span> endTime<span class="token punctuation">;</span>   <span class="token comment">// App允许使用的结束时间（以分钟为单位）</span>
<span class="token punctuation">}</span> App<span class="token punctuation">;</span>

<span class="token comment">// 时间转换函数，将时间字符串转换为以分钟为单位的整数</span>
<span class="token keyword">int</span> <span class="token function">convertTime</span><span class="token punctuation">(</span><span class="token keyword">char</span> <span class="token operator">*</span>time<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> hours<span class="token punctuation">,</span> minutes<span class="token punctuation">;</span>
    <span class="token function">sscanf</span><span class="token punctuation">(</span>time<span class="token punctuation">,</span> <span class="token string">"%d:%d"</span><span class="token punctuation">,</span> <span class="token operator">&amp;</span>hours<span class="token punctuation">,</span> <span class="token operator">&amp;</span>minutes<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 将时间字符串按照":"分割并转换为整数</span>
    <span class="token keyword">return</span> hours <span class="token operator">*</span> <span class="token number">60</span> <span class="token operator">+</span> minutes<span class="token punctuation">;</span> <span class="token comment">// 将小时和分钟转换为分钟</span>
<span class="token punctuation">}</span>

<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> n<span class="token punctuation">;</span> <span class="token comment">// 读取App数量</span>
    <span class="token function">scanf</span><span class="token punctuation">(</span><span class="token string">"%d"</span><span class="token punctuation">,</span> <span class="token operator">&amp;</span>n<span class="token punctuation">)</span><span class="token punctuation">;</span>

    App <span class="token operator">*</span>apps <span class="token operator">=</span> <span class="token punctuation">(</span>App <span class="token operator">*</span><span class="token punctuation">)</span><span class="token function">malloc</span><span class="token punctuation">(</span>n <span class="token operator">*</span> <span class="token keyword">sizeof</span><span class="token punctuation">(</span>App<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 创建App数组，用于存储所有App</span>

    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 循环读取每个App的信息，并创建App对象添加到数组中</span>
        <span class="token function">scanf</span><span class="token punctuation">(</span><span class="token string">"%s %d"</span><span class="token punctuation">,</span> apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>name<span class="token punctuation">,</span> <span class="token operator">&amp;</span>apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>priority<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">char</span> startTimeStr<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span><span class="token punctuation">,</span> endTimeStr<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
        <span class="token function">scanf</span><span class="token punctuation">(</span><span class="token string">"%s %s"</span><span class="token punctuation">,</span> startTimeStr<span class="token punctuation">,</span> endTimeStr<span class="token punctuation">)</span><span class="token punctuation">;</span>
        apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>startTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>startTimeStr<span class="token punctuation">)</span><span class="token punctuation">;</span>
        apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>endTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>endTimeStr<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword">char</span> queryTimeStr<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    <span class="token function">scanf</span><span class="token punctuation">(</span><span class="token string">"%s"</span><span class="token punctuation">,</span> queryTimeStr<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword">int</span> queryTime <span class="token operator">=</span> <span class="token function">convertTime</span><span class="token punctuation">(</span>queryTimeStr<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取查询时间，并转换为分钟</span>
    <span class="token keyword">char</span> <span class="token operator">*</span>appAtTime <span class="token operator">=</span> <span class="token string">"NA"</span><span class="token punctuation">;</span> <span class="token comment">// 初始化查询时间对应的App名称为"NA"</span>

    <span class="token comment">// 创建已注册App数组和计数器</span>
    App <span class="token operator">*</span>registeredApps <span class="token operator">=</span> <span class="token punctuation">(</span>App <span class="token operator">*</span><span class="token punctuation">)</span><span class="token function">malloc</span><span class="token punctuation">(</span>n <span class="token operator">*</span> <span class="token keyword">sizeof</span><span class="token punctuation">(</span>App<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword">int</span> registeredCount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>

    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>startTime <span class="token operator">&gt;=</span> apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>endTime<span class="token punctuation">)</span> <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果起始时间不小于结束时间，则跳过</span>

        <span class="token comment">// 遍历已注册的App数组，检查时间冲突</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> j <span class="token operator">=</span> registeredCount <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">;</span> j <span class="token operator">&gt;=</span> <span class="token number">0</span><span class="token punctuation">;</span> j<span class="token operator">--</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token comment">// 如果存在时间冲突</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>startTime <span class="token operator">&lt;</span> registeredApps<span class="token punctuation">[</span>j<span class="token punctuation">]</span><span class="token punctuation">.</span>endTime <span class="token operator">&amp;&amp;</span> apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>endTime <span class="token operator">&gt;</span> registeredApps<span class="token punctuation">[</span>j<span class="token punctuation">]</span><span class="token punctuation">.</span>startTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token comment">// 如果当前App的优先级高于已注册App的优先级</span>
                <span class="token keyword">if</span> <span class="token punctuation">(</span>apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>priority <span class="token operator">&gt;</span> registeredApps<span class="token punctuation">[</span>j<span class="token punctuation">]</span><span class="token punctuation">.</span>priority<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                    <span class="token comment">// 注销低优先级的App</span>
                    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> k <span class="token operator">=</span> j<span class="token punctuation">;</span> k <span class="token operator">&lt;</span> registeredCount <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">;</span> k<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                        registeredApps<span class="token punctuation">[</span>k<span class="token punctuation">]</span> <span class="token operator">=</span> registeredApps<span class="token punctuation">[</span>k <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                    registeredCount<span class="token operator">--</span><span class="token punctuation">;</span> <span class="token comment">// 减少已注册App的计数</span>
                <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{<!-- --></span>
                    <span class="token keyword">goto</span> continue_outer<span class="token punctuation">;</span> <span class="token comment">// 如果优先级不高，继续检查下一个已注册App</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// 将当前App添加到已注册App数组中</span>
        registeredApps<span class="token punctuation">[</span>registeredCount<span class="token operator">++</span><span class="token punctuation">]</span> <span class="token operator">=</span> apps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>

        continue_outer<span class="token operator">:</span> <span class="token punctuation">;</span> <span class="token comment">// 标签用于跳过当前循环</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 遍历已注册App数组，找到查询时间对应的App</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> registeredCount<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>queryTime <span class="token operator">&gt;=</span> registeredApps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>startTime <span class="token operator">&amp;&amp;</span> queryTime <span class="token operator">&lt;</span> registeredApps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>endTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            appAtTime <span class="token operator">=</span> registeredApps<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">.</span>name<span class="token punctuation">;</span> <span class="token comment">// 更新查询时间对应的App名称</span>
            <span class="token keyword">break</span><span class="token punctuation">;</span> <span class="token comment">// 找到后退出循环</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token function">printf</span><span class="token punctuation">(</span><span class="token string">"%s\n"</span><span class="token punctuation">,</span> appAtTime<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 输出查询时间对应的App名称</span>

    <span class="token comment">// 释放动态分配的内存</span>
    <span class="token function">free</span><span class="token punctuation">(</span>apps<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token function">free</span><span class="token punctuation">(</span>registeredApps<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li><li style="color: rgb(153, 153, 153);">80</li><li style="color: rgb(153, 153, 153);">81</li><li style="color: rgb(153, 153, 153);">82</li><li style="color: rgb(153, 153, 153);">83</li><li style="color: rgb(153, 153, 153);">84</li><li style="color: rgb(153, 153, 153);">85</li><li style="color: rgb(153, 153, 153);">86</li></ul></pre> 

                </div><div data-report-view="{&quot;mod&quot;:&quot;1585297308_001&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6548&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/banxia_frontend/article/details/141832127&quot;,&quot;extend1&quot;:&quot;pc&quot;,&quot;ab&quot;:&quot;new&quot;}"><div></div></div>
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/markdown_views-f23dff6052.css" rel="stylesheet">
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/style-c216769e99.css" rel="stylesheet">
        </div></html>