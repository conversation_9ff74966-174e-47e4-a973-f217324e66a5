<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,200分)- 抢7游戏（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>A、B两个人玩抢7游戏&#xff0c;游戏规则为&#xff1a;</p> 
<p>A先报一个起始数字 X&#xff08;10 ≤ 起始数字 ≤ 10000&#xff09;&#xff0c;B报下一个数字 Y &#xff08;X - Y &lt; 3&#xff09;&#xff0c;A再报一个数字 Z&#xff08;Y - Z &lt; 3&#xff09;&#xff0c;以此类推&#xff0c;直到其中一个抢到7&#xff0c;抢到7即为胜者&#xff1b;</p> 
<p>在B赢得比赛的情况下&#xff0c;一共有多少种组合&#xff1f;</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>起始数字 M</p> 
<ul><li>10 ≤ M ≤ 10000</li></ul> 
<p>如&#xff1a;</p> 
<blockquote> 
 <p>100</p> 
</blockquote> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>B能赢得比赛的组合次数</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">10</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">1</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">无</td></tr></tbody></table> 
<p></p> 
<h3>数学分析解法&#xff08;可能会超时&#xff09;</h3> 
<p>下面模拟M为10~14时&#xff0c;B能够获胜的一些情况&#xff1a;</p> 
<p style="text-align:center;"><img alt="" src="https://img-blog.csdnimg.cn/direct/7cad1b64aaac450494d3e88ed81d1c6c.png" /></p> 
<p>看完上图&#xff0c;我们可以发现&#xff1a;</p> 
<p>抛开A首次叫的数字M&#xff0c;剩下的 M - 7 长度&#xff08;上图中有颜色的&#xff09;&#xff0c;必须发生奇数次叫&#xff0c;才能保证B获胜。</p> 
<blockquote> 
 <p>原因是&#xff1a;奇数次叫中&#xff0c;第一次必然是B&#xff0c;由于是奇数次&#xff0c;因此最后一次也必然是B&#xff0c;比如</p> 
 <p>BAB</p> 
 <p>BABAB</p> 
 <p>都是奇数次。</p> 
</blockquote> 
<p>因此我们只需要将整数 M - 7 划分为奇数块即可&#xff0c;且每块取值只能是1或2。</p> 
<p></p> 
<p>我们可以假设初始时&#xff0c;一共发生了M-7次叫&#xff08;M-7可能不是奇数&#xff09;&#xff0c;即每块长度都是1&#xff0c;此时我们设</p> 
<ul><li>oneCount &#61; M - 7</li><li>twoCount &#61; 0</li></ul> 
<p>然后检查 oneCount &#43; twoCount 的和&#xff08;一共叫几次&#xff09;&#xff1a;</p> 
<ul><li>若为奇数&#xff0c;则计算 oneCount 个 1 和 twoCount 个 2 形成的不重复的全排列的个数&#xff0c;统计进结果ans</li><li>若为偶数&#xff0c;则B无法获胜</li></ul> 
<p>之后&#xff0c;我们应该合并两个1为一个2&#xff0c;即&#xff1a;</p> 
<ul><li>oneCount -&#61; 2</li><li>twoCount &#43;&#61; 1</li></ul> 
<p>此时就会产生一种新的叫声情况&#xff0c;将新的oneCount和twoCount带入前面逻辑&#xff0c;进行循环处理&#xff0c;知道oneCount &lt; 0 停止。</p> 
<p></p> 
<p>本题的数量级很大&#xff0c;10 ≤ M ≤ 10000&#xff0c;因此满足要求的情况数量可能极端大&#xff0c;此时我们应该使用大数记录结果。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JS算法源码</h4> 
<pre><code class="language-javascript">const rl &#61; require(&#34;readline&#34;).createInterface({ input: process.stdin });
var iter &#61; rl[Symbol.asyncIterator]();
const readline &#61; async () &#61;&gt; (await iter.next()).value;

void (async function () {
  const m &#61; parseInt(await readline());

  const factor &#61; initFactor(m - 7);

  let oneCount &#61; m - 7;
  let twoCount &#61; 0;

  // 记录B赢的情况数
  let ans &#61; BigInt(0);

  while (oneCount &gt;&#61; 0) {
    // 叫的次数为奇数时&#xff0c;才能B赢
    if ((oneCount &#43; twoCount) % 2 !&#61; 0) {
      ans &#43;&#61; getPermutationCount(oneCount, twoCount);
    }

    // 合并两个1为一个2
    oneCount -&#61; 2;
    twoCount &#43;&#61; 1;
  }

  console.log(ans.toString());

  // 求解不重复的全排列数
  function getPermutationCount(oneCount, twoCount) {
    // 即 1 1 1 1 1 或 2 2 2 这种情况&#xff0c;此时只有一种排列
    if (oneCount &#61;&#61; 0 || twoCount &#61;&#61; 0) {
      return BigInt(1);
    } else {
      // 排列数去重&#xff0c;比如 1 1 1 2 2 的不重复排列数为 5! / 3! / 2! &#61; 10
      return factor[oneCount &#43; twoCount] / factor[oneCount] / factor[twoCount];
    }
  }

  // 阶乘
  function initFactor(n) {
    const factor &#61; new Array(n &#43; 1);

    factor[0] &#61; BigInt(1);

    for (let i &#61; 1; i &lt;&#61; n; i&#43;&#43;) {
      factor[i] &#61; BigInt(i) * factor[i - 1];
    }

    return factor;
  }
})();
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.math.BigInteger;
import java.util.Scanner;

public class Main {
  static BigInteger[] factor;

  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    int m &#61; sc.nextInt();

    initFactor(m - 7);

    int oneCount &#61; m - 7;
    int twoCount &#61; 0;

    // 记录B赢的情况数
    BigInteger ans &#61; new BigInteger(&#34;0&#34;);

    while (oneCount &gt;&#61; 0) {
      // 叫的次数为奇数时&#xff0c;才能B赢
      if ((oneCount &#43; twoCount) % 2 !&#61; 0) {
        ans &#61; ans.add(getPermutationCount(oneCount, twoCount));
      }

      // 合并两个1为一个2
      oneCount -&#61; 2;
      twoCount &#43;&#61; 1;
    }

    System.out.println(ans);
  }

  // 求解不重复的全排列数
  public static BigInteger getPermutationCount(int oneCount, int twoCount) {
    if (oneCount &#61;&#61; 0 || twoCount &#61;&#61; 0) { // 即 1 1 1 1 1 或 2 2 2 这种情况&#xff0c;此时只有一种排列
      return new BigInteger(&#34;1&#34;);
    } else {
      // 排列数去重&#xff0c;比如 1 1 1 2 2 的不重复排列数为 5! / 3! / 2! &#61; 10
      return factor[oneCount &#43; twoCount].divide(factor[oneCount].multiply(factor[twoCount]));
    }
  }

  // 阶乘
  public static void initFactor(int n) {
    factor &#61; new BigInteger[n &#43; 1];
    factor[0] &#61; new BigInteger(&#34;1&#34;);
    for (int i &#61; 1; i &lt;&#61; n; i&#43;&#43;) {
      factor[i] &#61; factor[i - 1].multiply(new BigInteger(i &#43; &#34;&#34;));
    }
  }
}
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<p>这里Python进行了大数除法&#xff0c;因此数值类型变为了float&#xff0c;不在支持大数&#xff0c;所以需要使用decimal。</p> 
<pre><code class="language-python">import decimal  # 超大数运算内置库
from decimal import Decimal
decimal.setcontext(decimal.Context(prec&#61;2500))  # 设置超大数精度

m &#61; int(input())
factor &#61; []


# 阶乘
def initFactor(n):
    factor.append(Decimal(1))

    for i in range(1, n&#43;1):
        factor.append(Decimal(i) * factor[-1])


# 求解不重复的全排列数
def getPermutationCount(oneCount, twoCount):
    if oneCount &#61;&#61; 0 or twoCount &#61;&#61; 0:
        # 即 1 1 1 1 1 或 2 2 2 这种情况&#xff0c;此时只有一种排列
        return 1
    else:
        # 排列数去重&#xff0c;比如 1 1 1 2 2 的不重复排列数为 5! / 3! / 2! &#61; 10
        return factor[oneCount &#43; twoCount] / factor[oneCount] / factor[twoCount]


def getResult():
    initFactor(m - 7)

    oneCount &#61; m - 7
    twoCount &#61; 0

    # 记录B赢的情况数
    ans &#61; Decimal(0)

    while oneCount &gt;&#61; 0:
        # 叫的次数为奇数时&#xff0c;才能B赢
        if (oneCount &#43; twoCount) % 2 !&#61; 0:
            ans &#43;&#61; getPermutationCount(oneCount, twoCount)

        # 合并两个1为一个2
        oneCount -&#61; 2
        twoCount &#43;&#61; 1

    return ans


print(&#34;{:.0f}&#34;.format(getResult()))
</code></pre> 
<p></p> 
<h4>C算法源码</h4> 
<p>下面代码没有实现大数运算&#xff0c;关于大数运算可以参考&#xff1a;</p> 
<p><a href="https://blog.csdn.net/chenlong_cxy/article/details/124141228?utm_medium&#61;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-124141228-blog-79074728.235%5Ev40%5Epc_relevant_anti_vip_base&amp;spm&#61;1001.2101.3001.4242.1&amp;utm_relevant_index&#61;4" title="大数运算&#xff08;加、减、乘、除&#xff09;-CSDN博客">大数运算&#xff08;加、减、乘、除&#xff09;-CSDN博客</a></p> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;

// 阶乘
long long getFactor(int n) {
    long long ans &#61; 1;

    for (int i &#61; 2; i &lt;&#61; n; i&#43;&#43;) {
        ans *&#61; i;
    }

    return ans;
}

// 求解不重复的全排列数
long long getPermutationCount(int oneCount, int twoCount) {
    if (oneCount &#61;&#61; 0 || twoCount &#61;&#61; 0) {
        // 即 1 1 1 1 1 或 2 2 2 这种情况&#xff0c;此时只有一种排列
        return 1;
    } else {
        // 排列数去重&#xff0c;比如 1 1 1 2 2 的不重复排列数为 5! / 3! / 2! &#61; 10
        return getFactor(oneCount &#43; twoCount) / getFactor(oneCount) / getFactor(twoCount);
    }
}

int main() {
    int m;
    scanf(&#34;%d&#34;, &amp;m);

    int oneCount &#61; m - 7;
    int twoCount &#61; 0;

    // 记录B赢的情况数
    long long ans &#61; 0;

    while (oneCount &gt;&#61; 0) {
        // 叫的次数为奇数时&#xff0c;才能B赢
        if ((oneCount &#43; twoCount) % 2 !&#61; 0) {
            ans &#43;&#61; getPermutationCount(oneCount, twoCount);
        }

        // 合并两个1为一个2
        oneCount -&#61; 2;
        twoCount &#43;&#61; 1;
    }

    printf(&#34;%lld&#34;, ans);

    return 0;
}</code></pre> 
<p></p> 
<h3>动态规划解法&#xff08;不会超时&#xff09; </h3> 
<p>本题最优解法为动态规划&#xff0c;动态规划的逻辑很简单&#xff0c;假设A从m开始叫&#xff0c;那么&#xff1a;</p> 
<p>B叫了数字 i 的方案数有多少种呢&#xff1f;</p> 
<p>如果B叫了数字 i&#xff0c;那么上一把A可能会叫数字i&#43;1&#xff0c;也可能叫数字i&#43;2</p> 
<blockquote> 
 <p>dpB[i] 表示 B 能叫到数字 i 的方案数</p> 
 <p>dpA[i] 表示 A 能叫到数字 j 的方案数</p> 
</blockquote> 
<p>那么 dpB[i] &#61; dpA[i&#43;1] &#43; dpA[i&#43;2]</p> 
<p></p> 
<p>同理的是&#xff0c;如果A叫了数字 i&#xff0c;那么上一把B可能会叫数字i&#43;1&#xff0c;也可能会叫数字 i&#43;2</p> 
<p>那么 dpA[i] &#61; dpB[i&#43;1] &#43; dpB[i&#43;2]</p> 
<p></p> 
<p>初始时&#xff0c;是A从m开始叫&#xff0c;因此 dpA[m] &#61; 1&#xff0c;即A叫到数字m的方案数为1。而B肯定叫不到数字m&#xff0c;因此初始化dpB[m] &#61; 0。</p> 
<p></p> 
<p>之后我们可以递推出dpB[m-1]&#xff0c;即B叫出数字m-1的方案数&#xff0c;即dpB[m-1] &#61; dpA[m] &#43; dp[m&#43;1]</p> 
<blockquote> 
 <p>提示&#xff0c;根据dpB[m-1] &#61; dpA[m] &#43; dpA[m&#43;1]的递推式&#xff0c;我们可以了解到dpA,dpB数组的长度应该初始化为m&#43;2&#xff0c;这样上面递推式才不会越界。</p> 
 <p>且dpA[m]  &#61; 1&#xff0c;dpA[m&#43;1] &#61; 0</p> 
</blockquote> 
<p>而数字m-1&#xff0c;对于A而言是叫不到的&#xff0c;因此dpA[m-1]&#61;0&#xff0c;但是也可以基于递推式得到&#xff1a;</p> 
<blockquote> 
 <p>dpA[m-1] &#61; dpB[m] &#43; dpB[m&#43;1]&#xff0c;而dpB[m]和dpB[m&#43;1]都应该初始化为0。</p> 
</blockquote> 
<p></p> 
<p>因此我们只需要按照上面递推式&#xff0c;一直递推到dpB[7]即可返回。</p> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.math.BigInteger;
import java.util.Scanner;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    int m &#61; sc.nextInt();

    // dpA[i] 表示 A 叫 数字i 的方案数
    BigInteger[] dpA &#61; new BigInteger[m &#43; 2];
    // 初始化dpA[i]
    for (int i &#61; 0; i &lt; m &#43; 2; i&#43;&#43;) dpA[i] &#61; new BigInteger(&#34;0&#34;);
    // 由于是A从m开始叫&#xff0c;因此A叫m的方案数为1
    dpA[m] &#61; new BigInteger(&#34;1&#34;);

    // dpB[i] 表示 B叫 数字i 的方案数
    BigInteger[] dpB &#61; new BigInteger[m &#43; 2];
    // 初始化dpB[i]
    for (int i &#61; 0; i &lt; m &#43; 2; i&#43;&#43;) dpB[i] &#61; new BigInteger(&#34;0&#34;);

    for (int i &#61; m - 1; i &gt;&#61; 7; i--) {
      // B叫数字i的方案数 &#61; A叫数字i&#43;1的方案数 &#43; A叫数字i&#43;2的方案数
      dpB[i] &#61; dpA[i &#43; 1].add(dpA[i &#43; 2]);
      // A叫数字i的方案数 &#61; B叫数字i&#43;1的方案数 &#43; B叫数字i&#43;2的方案数
      dpA[i] &#61; dpB[i &#43; 1].add(dpB[i &#43; 2]);
    }

    // 返回B叫7的方案数
    System.out.println(dpB[7]);
  }
}
</code></pre> 
<p></p> 
<h4>JS算法源码</h4> 
<pre><code class="language-javascript">const rl &#61; require(&#34;readline&#34;).createInterface({ input: process.stdin });
var iter &#61; rl[Symbol.asyncIterator]();
const readline &#61; async () &#61;&gt; (await iter.next()).value;

void (async function () {
  const m &#61; parseInt(await readline());

  // dpA[i] 表示 A 叫 数字i 的方案数
  const dpA &#61; new Array(m &#43; 2).fill(0).map(() &#61;&gt; BigInt(0));
  // 由于是A从m开始叫&#xff0c;因此A叫m的方案数为1
  dpA[m] &#61; BigInt(1);

  // dpB[i] 表示 B叫 数字i 的方案数
  const dpB &#61; new Array(m &#43; 2).fill(0).map(() &#61;&gt; BigInt(0));

  for (let i &#61; m - 1; i &gt;&#61; 7; i--) {
    // B叫数字i的方案数 &#61; A叫数字i&#43;1的方案数 &#43; A叫数字i&#43;2的方案数
    dpB[i] &#61; dpA[i &#43; 1] &#43; dpA[i &#43; 2];
    // A叫数字i的方案数 &#61; B叫数字i&#43;1的方案数 &#43; B叫数字i&#43;2的方案数
    dpA[i] &#61; dpB[i &#43; 1] &#43; dpB[i &#43; 2];
  }

  console.log(dpB[7].toString());
})();
</code></pre> 
<p></p> 
<h4 style="background-color:transparent;">Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
m &#61; int(input())


# 算法入口
def getResult():
    # dpA[i] 表示 A 叫 数字i 的方案数
    dpA &#61; [0 for _ in range(m &#43; 2)]
    # 由于是A从m开始叫&#xff0c;因此A叫m的方案数为1
    dpA[m] &#61; 1

    # dpB[i] 表示 B叫 数字i 的方案数
    dpB &#61; [0 for _ in range(m &#43; 2)]

    for i in range(m - 1, 6, -1):
        # B叫数字i的方案数 &#61; A叫数字i&#43;1的方案数 &#43; A叫数字i&#43;2的方案数
        dpB[i] &#61; dpA[i &#43; 1] &#43; dpA[i &#43; 2]
        # A叫数字i的方案数 &#61; B叫数字i&#43;1的方案数 &#43; B叫数字i&#43;2的方案数
        dpA[i] &#61; dpB[i &#43; 1] &#43; dpB[i &#43; 2]

    # 返回B叫7的方案数
    return dpB[7]


# 算法调用
print(getResult())</code></pre> 
<p></p> 
<h4>C算法源码</h4> 
<p> 下面代码没有实现大数运算&#xff0c;关于大数运算可以参考&#xff1a;</p> 
<p><a href="https://blog.csdn.net/chenlong_cxy/article/details/124141228?utm_medium&#61;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-124141228-blog-79074728.235%5Ev40%5Epc_relevant_anti_vip_base&amp;spm&#61;1001.2101.3001.4242.1&amp;utm_relevant_index&#61;4" title="大数运算&#xff08;加、减、乘、除&#xff09;-CSDN博客">大数运算&#xff08;加、减、乘、除&#xff09;-CSDN博客</a></p> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;

int main() {
    int m;
    scanf(&#34;%d&#34;, &amp;m);

    // dpA[i] 表示 A 叫 数字i 的方案数
    long long dpA[m &#43; 2];
    // 初始化dpA[i]
    for (int i &#61; 0; i &lt; m &#43; 2; i&#43;&#43;) dpA[i] &#61; 0;
    // 由于是A从m开始叫&#xff0c;因此A叫m的方案数为1
    dpA[m] &#61; 1;

    // dpB[i] 表示 B叫 数字i 的方案数
    long long dpB[m &#43; 2];
    // 初始化dpB[i]
    for (int i &#61; 0; i &lt; m &#43; 2; i&#43;&#43;) dpB[i] &#61; 0;

    for (int i &#61; m - 1; i &gt;&#61; 7; i--) {
        // B叫数字i的方案数 &#61; A叫数字i&#43;1的方案数 &#43; A叫数字i&#43;2的方案数
        dpB[i] &#61; dpA[i &#43; 1] &#43; dpA[i &#43; 2];
        // A叫数字i的方案数 &#61; B叫数字i&#43;1的方案数 &#43; B叫数字i&#43;2的方案数
        dpA[i] &#61; dpB[i &#43; 1] &#43; dpB[i &#43; 2];
    }

    // 返回B叫7的方案数
    printf(&#34;%lld&#34;, dpB[7]);

    return 0;
}</code></pre> 
<p></p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>