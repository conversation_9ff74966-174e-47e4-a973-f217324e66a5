<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(B卷,200分)- 矩阵扩散（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>存在一个m×n的二维数组&#xff0c;其成员取值范围为0或1。</p> 
<p>其中值为1的成员具备扩散性&#xff0c;每经过1S&#xff0c;将上下左右值为0的成员同化为1。</p> 
<p>二维数组的成员初始值都为0&#xff0c;将第[i,j]和[k,l]两个个位置上元素修改成1后&#xff0c;求矩阵的所有元素变为1需要多长时间。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>输入数据中的前2个数字表示这是一个m×n的矩阵&#xff0c;m和n不会超过1024大小&#xff1b;</p> 
<p>中间两个数字表示一个初始扩散点位置为i,j&#xff1b;</p> 
<p>最后2个数字表示另一个扩散点位置为k,l。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>输出矩阵的所有元素变为1所需要秒数。</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">4,4,0,0,3,3</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">3</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;"> <p>输入数据中的前2个数字表示这是一个4*4的矩阵&#xff1b;</p> <p>中间两个数字表示一个初始扩散点位置为0,0&#xff1b;</p> <p>最后2个数字表示另一个扩散点位置为3,3。</p> <p>给出的样例是一个简单模型&#xff0c;初始点在对角线上&#xff0c;达到中间的位置分别为3次迭代&#xff0c;即3秒。所以输出为3。</p> </td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>用例图示如下&#xff1a;</p> 
<p><img alt="" height="246" src="https://img-blog.csdnimg.cn/0d2d7314acf44c3c87506a96292fccb0.png" width="1045" /></p> 
<p> 一共需要4-1&#61;3秒。</p> 
<p></p> 
<p>本题可以使用图的多源BFS来解题。</p> 
<p>关于图的多源BFS得解析&#xff0c;请看&#xff1a;</p> 
<p><a href="https://blog.csdn.net/qfc_128220/article/details/127711317?spm&#61;1001.2014.3001.5501" title="华为OD机试 - 计算疫情扩散时间_伏城之外的博客-CSDN博客">华为OD机试 - 计算疫情扩散时间_伏城之外的博客-CSDN博客</a></p> 
<p>更多相关题目请看&#xff1a;</p> 
<p><a href="https://blog.csdn.net/qfc_128220/article/details/128169190?spm&#61;1001.2014.3001.5501" title="华为OD机试 - 污染水域_伏城之外的博客-CSDN博客">华为OD机试 - 污染水域_伏城之外的博客-CSDN博客</a></p> 
<p><a href="https://fcqian.blog.csdn.net/article/details/128233067" rel="nofollow" title="华为OD机试 - 计算网络信号、输出给定位置的信号强弱_伏城之外的博客-CSDN博客">华为OD机试 - 计算网络信号、输出给定位置的信号强弱_伏城之外的博客-CSDN博客</a></p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.on(&#34;line&#34;, (line) &#61;&gt; {
  const [m, n, i, j, k, l] &#61; line.split(&#34;,&#34;).map(Number);
  console.log(getResult(m, n, i, j, k, l));
});

/**
 * &#64;param {*} m m×n的二维数组
 * &#64;param {*} n m×n的二维数组
 * &#64;param {*} i 扩散点位置为i,j
 * &#64;param {*} j 扩散点位置为i,j
 * &#64;param {*} k 扩散点位置为k,l
 * &#64;param {*} l 扩散点位置为k,l
 */
function getResult(m, n, i, j, k, l) {
  const matrix &#61; new Array(m).fill(0).map(() &#61;&gt; new Array(n).fill(0));
  matrix[i][j] &#61; 1;
  matrix[k][l] &#61; 1;

  // count记录未被扩散的点的数量
  let count &#61; m * n - 2;

  // 多源BFS实现队列
  let queue &#61; [
    [i, j],
    [k, l],
  ];

  // 上下左右偏移量
  const offsets &#61; [
    [1, 0],
    [-1, 0],
    [0, 1],
    [0, -1],
  ];

  let day &#61; 0;

  // 如果扩散点没有了&#xff0c;或者所有点已被扩散&#xff0c;则停止循环
  while (queue.length &gt; 0 &amp;&amp; count &gt; 0) {
    const newQueue &#61; [];

    for (const [x, y] of queue) {
      for (let [offsetX, offsetY] of offsets) {
        const newX &#61; x &#43; offsetX;
        const newY &#61; y &#43; offsetY;

        if (
          newX &gt;&#61; 0 &amp;&amp;
          newX &lt; m &amp;&amp;
          newY &gt;&#61; 0 &amp;&amp;
          newY &lt; n &amp;&amp;
          matrix[newX][newY] &#61;&#61; 0
        ) {
          // 将点被扩散的时间记录为该点的值
          matrix[newX][newY] &#61; 1;
          // 被扩散到的点将变为新的扩散源
          newQueue.push([newX, newY]);
          // 未被扩散点的数量--
          count--;
        }
      }
    }

    queue &#61; newQueue;
    day&#43;&#43;;
  }

  return day;
}
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.Arrays;
import java.util.LinkedList;
import java.util.Scanner;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    Integer[] arr &#61;
        Arrays.stream(sc.next().split(&#34;,&#34;)).map(Integer::parseInt).toArray(Integer[]::new);

    System.out.println(getResult(arr[0], arr[1], arr[2], arr[3], arr[4], arr[5]));
  }

  /**
   * &#64;param m m m×n的二维数组
   * &#64;param n n m×n的二维数组
   * &#64;param i 扩散点位置为i,j
   * &#64;param j 扩散点位置为i,j
   * &#64;param k 扩散点位置为k,l
   * &#64;param l 扩散点位置为k,l
   * &#64;return 扩散所有点需要的时间
   */
  public static int getResult(int m, int n, int i, int j, int k, int l) {
    int[][] matrix &#61; new int[m][n];
    matrix[i][j] &#61; 1;
    matrix[k][l] &#61; 1;

    // count记录未被扩散的点的数量
    int count &#61; m * n - 2;

    // 多源BFS实现队列
    LinkedList&lt;int[]&gt; queue &#61; new LinkedList&lt;&gt;();
    queue.addLast(new int[] {i, j});
    queue.addLast(new int[] {k, l});

    // 上下左右偏移量
    int[][] offsets &#61; {<!-- -->{1, 0}, {-1, 0}, {0, 1}, {0, -1}};

    int day &#61; 0;
    // 如果扩散点没有了&#xff0c;或者所有点已被扩散&#xff0c;则停止循环
    while (queue.size() &gt; 0 &amp;&amp; count &gt; 0) {
      LinkedList&lt;int[]&gt; newQueue &#61; new LinkedList&lt;&gt;();

      for (int[] pos : queue) {
        int x &#61; pos[0];
        int y &#61; pos[1];

        for (int[] offset : offsets) {
          int newX &#61; x &#43; offset[0];
          int newY &#61; y &#43; offset[1];

          if (newX &gt;&#61; 0 &amp;&amp; newX &lt; m &amp;&amp; newY &gt;&#61; 0 &amp;&amp; newY &lt; n &amp;&amp; matrix[newX][newY] &#61;&#61; 0) {
            // 将点被扩散的时间记录为该点的值
            matrix[newX][newY] &#61; 1;
            // 被扩散到的点将变为新的扩散源
            newQueue.addLast(new int[] {newX, newY});
            // 未被扩散点的数量--
            count--;
          }
        }
      }

      queue &#61; newQueue;
      day&#43;&#43;;
    }

    return day;
  }
}
</code></pre> 
<p></p> 
<h4 style="background-color:transparent;">Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
m, n, i, j, k, l &#61; map(int, input().split(&#34;,&#34;))


# 算法入口
def getResult(m, n, i, j, k, l):
    &#34;&#34;&#34;
    :param m: 矩阵行数
    :param n: 矩阵列数
    :param i: 扩散点1行号
    :param j: 扩散点1列好
    :param k: 扩散点2行号
    :param l: 扩散点2列号
    :return: 矩阵的所有元素变为1所需要秒数
    &#34;&#34;&#34;
    matrix &#61; [[0 for _ in range(n)] for _ in range(m)]
    matrix[i][j] &#61; 1
    matrix[k][l] &#61; 1

    # count记录未被扩散的点的数量
    count &#61; m * n - 2

    # 多源BFS实现队列
    queue &#61; [[i, j], [k, l]]

    # 上下左右偏移量
    offsets &#61; ((1, 0), (-1, 0), (0, 1), (0, -1))

    day &#61; 0

    # 如果扩散点没有了&#xff0c;或者所有点已被扩散&#xff0c;则停止循环
    while len(queue) &gt; 0 and count &gt; 0:
        newQueue &#61; []

        for x, y in queue:
            for offsetX, offsetY in offsets:
                newX &#61; x &#43; offsetX
                newY &#61; y &#43; offsetY

                if 0 &lt;&#61; newX &lt; m and 0 &lt;&#61; newY &lt; n and matrix[newX][newY] &#61;&#61; 0:
                    # 将点被扩散的时间记录为该点的值
                    matrix[newX][newY] &#61; 1
                    # 被扩散到的点将变为新的扩散源
                    newQueue.append([newX, newY])
                    # 未被扩散点的数量--
                    count -&#61; 1

        queue &#61; newQueue
        day &#43;&#61; 1

    return day


# 算法调用
print(getResult(m, n, i, j, k, l))
</code></pre> 
<p></p> 
<h4>C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;

#define MAX_SIZE 1024

typedef struct ListNode {
    int ele;
    struct ListNode *next;
} ListNode;

typedef struct {
    int size;
    ListNode *head;
    ListNode *tail;
} LinkedList;

LinkedList *new_LinkedList();
void addLast_LinkedList(LinkedList *link, int ele);

int matrix[MAX_SIZE][MAX_SIZE] &#61; {0};

int main() {
    int m, n, i, j, k, l;
    scanf(&#34;%d,%d,%d,%d,%d,%d&#34;, &amp;m, &amp;n, &amp;i, &amp;j, &amp;k, &amp;l);

    // 初始扩散点位置为i,j
    matrix[i][j] &#61; 1;
    // 初始扩散点位置为k,l
    matrix[k][l] &#61; 1;

    // count记录未被扩散的点的数量
    int count &#61; m * n - 2;

    // 多源BFS实现队列
    LinkedList *queue &#61; new_LinkedList();
    addLast_LinkedList(queue, i * n &#43; j);
    addLast_LinkedList(queue, k * n &#43; l);

    // 上下左右偏移量
    int offsets[4][2] &#61; {<!-- -->{1,  0},
                         {-1, 0},
                         {0,  1},
                         {0,  -1}};

    // 扩散时间
    int day &#61; 0;

    // 如果扩散点没有了&#xff0c;或者所有点已被扩散&#xff0c;则停止循环
    while (queue-&gt;size &gt; 0 &amp;&amp; count &gt; 0) {
        LinkedList *newQueue &#61; new_LinkedList();

        ListNode *cur &#61; queue-&gt;head;
        while (cur !&#61; NULL) {
            int x &#61; cur-&gt;ele / n;
            int y &#61; cur-&gt;ele % n;

            for (int a &#61; 0; a &lt; 4; a&#43;&#43;) {
                int newX &#61; x &#43; offsets[a][0];
                int newY &#61; y &#43; offsets[a][1];

                if (newX &gt;&#61; 0 &amp;&amp; newX &lt; m &amp;&amp; newY &gt;&#61; 0 &amp;&amp; newY &lt; n &amp;&amp; matrix[newX][newY] &#61;&#61; 0) {
                    // 将点被扩散的时间记录为该点的值
                    matrix[newX][newY] &#61; 1;
                    // 被扩散到的点将变为新的扩散源
                    addLast_LinkedList(newQueue, newX * n &#43; newY);
                    // 未被扩散点的数量--
                    count--;
                }
            }
            cur &#61; cur-&gt;next;
        }

        queue &#61; newQueue;
        day&#43;&#43;;
    }

    printf(&#34;%d\n&#34;, day);

    return 0;
}

LinkedList *new_LinkedList() {
    LinkedList *link &#61; (LinkedList *) malloc(sizeof(LinkedList));

    link-&gt;size &#61; 0;
    link-&gt;head &#61; NULL;
    link-&gt;tail &#61; NULL;

    return link;
}

void addLast_LinkedList(LinkedList *link, int ele) {
    ListNode *node &#61; (ListNode *) malloc(sizeof(ListNode));

    node-&gt;ele &#61; ele;
    node-&gt;next &#61; NULL;

    if (link-&gt;size &#61;&#61; 0) {
        link-&gt;head &#61; node;
        link-&gt;tail &#61; node;
    } else {
        link-&gt;tail-&gt;next &#61; node;
        link-&gt;tail &#61; node;
    }

    link-&gt;size&#43;&#43;;
}</code></pre> 
<p></p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>