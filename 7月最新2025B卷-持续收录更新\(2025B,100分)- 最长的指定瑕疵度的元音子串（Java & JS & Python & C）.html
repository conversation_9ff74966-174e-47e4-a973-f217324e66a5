<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,100分)- 最长的指定瑕疵度的元音子串（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>开头和结尾都是元音字母&#xff08;aeiouAEIOU&#xff09;的字符串为元音字符串&#xff0c;其中混杂的非元音字母数量为其瑕疵度。比如:</p> 
<ol><li>“a” 、 “aa”是元音字符串&#xff0c;其瑕疵度都为0</li><li>“aiur”不是元音字符串&#xff08;结尾不是元音字符&#xff09;</li><li> “abira”是元音字符串&#xff0c;其瑕疵度为2</li></ol> 
<p>给定一个字符串&#xff0c;请找出指定瑕疵度的最长元音字符子串&#xff0c;并输出其长度&#xff0c;如果找不到满足条件的元音字符子串&#xff0c;输出0。</p> 
<p>子串&#xff1a;字符串中任意个连续的字符组成的子序列称为该字符串的子串。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>首行输入是一个整数&#xff0c;表示预期的瑕疵度flaw&#xff0c;取值范围[0, 65535]。</p> 
<p>接下来一行是一个仅由字符a-z和A-Z组成的字符串&#xff0c;字符串长度(0, 65535]。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>输出为一个整数&#xff0c;代表满足条件的元音字符子串的长度。</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">0<br /> asdbuiodevauufgh</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">3</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">无</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">2<br /> aeueo</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">0</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">无</td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p><img alt="" height="799" src="https://img-blog.csdnimg.cn/75a9cd9bc529429498db58f1cc4ef433.png" width="1200" /></p> 
<p>瑕疵度计算规则如上图注解所示。</p> 
<p>当两指针之间划定的子串的瑕疵度diff  大于  指定的瑕疵度flaw时&#xff0c;则左指针 l&#43;&#43;</p> 
<p>当两指针之间划定的子串的瑕疵度diff  小于  指定的瑕疵度flaw时&#xff0c;则右指针 r&#43;&#43;</p> 
<p>当两指针之间划定的子串的瑕疵度diff  等于  指定的瑕疵度flaw时&#xff0c;则记录当前子串长度&#xff0c;并r&#43;&#43;</p> 
<p>按以上逻辑&#xff0c;直到r指针移动到idxs数组的尾部。</p> 
<p><img alt="" height="744" src="https://img-blog.csdnimg.cn/eb378b5515874c268c7f1fe75ad03674.png" width="1200" /></p> 
<h4 style="background-color:transparent;">Java算法源码</h4> 
<pre><code class="language-java">import java.util.ArrayList;
import java.util.HashSet;
import java.util.Scanner;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    int flaw &#61; sc.nextInt();
    String s &#61; sc.next();

    System.out.println(getResult(flaw, s));
  }

  public static int getResult(int flaw, String s) {
    char[] yuan &#61; {&#39;a&#39;, &#39;e&#39;, &#39;i&#39;, &#39;o&#39;, &#39;u&#39;, &#39;A&#39;, &#39;E&#39;, &#39;I&#39;, &#39;O&#39;, &#39;U&#39;};
    HashSet&lt;Character&gt; set &#61; new HashSet&lt;&gt;();
    for (char c : yuan) set.add(c);

    ArrayList&lt;Integer&gt; idxs &#61; new ArrayList&lt;&gt;();
    for (int i &#61; 0; i &lt; s.length(); i&#43;&#43;) {
      if (set.contains(s.charAt(i))) idxs.add(i);
    }

    int ans &#61; 0;
    int n &#61; idxs.size();

    int l &#61; 0;
    int r &#61; 0;

    while (r &lt; n) {
      // 瑕疵度计算
      int diff &#61; idxs.get(r) - idxs.get(l) - (r - l);

      if (diff &gt; flaw) {
        l&#43;&#43;;
      } else if (diff &lt; flaw) {
        r&#43;&#43;;
      } else {
        ans &#61; Math.max(ans, idxs.get(r) - idxs.get(l) &#43; 1);
        r&#43;&#43;;
      }
    }

    return ans;
  }
}
</code></pre> 
<p> </p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JS算法源码</h4> 
<pre><code class="language-javascript">const rl &#61; require(&#34;readline&#34;).createInterface({ input: process.stdin });
var iter &#61; rl[Symbol.asyncIterator]();
const readline &#61; async () &#61;&gt; (await iter.next()).value;

// 输入输出处理
void (async function () {
  const flaw &#61; parseInt(await readline());
  const str &#61; await readline();

  console.log(getResult(str, flaw));
})();

function getResult(str, flaw) {
  const set &#61; new Set(&#34;aeiouAEIOU&#34;);

  const idxs &#61; [];
  for (let i &#61; 0; i &lt; str.length; i&#43;&#43;) {
    if (set.has(str[i])) idxs.push(i);
  }

  let ans &#61; 0;
  let n &#61; idxs.length;

  let l &#61; 0;
  let r &#61; 0;

  while (r &lt; n) {
    // 瑕疵度计算
    let diff &#61; idxs[r] - idxs[l] - (r - l);

    if (diff &gt; flaw) {
      l&#43;&#43;;
    } else if (diff &lt; flaw) {
      r&#43;&#43;;
    } else {
      ans &#61; Math.max(ans, idxs[r] - idxs[l] &#43; 1);
      r&#43;&#43;;
    }
  }

  return ans;
}
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
flaw &#61; int(input())
s &#61; input()


# 算法入口
def getResult():
    yuanSet &#61; set(list(&#34;aeiouAEIOU&#34;))

    idxs &#61; []
    for i in range(len(s)):
        if s[i] in yuanSet:
            idxs.append(i)

    ans &#61; 0

    l &#61; 0
    r &#61; 0

    while r &lt; len(idxs):
        # 瑕疵度计算
        diff &#61; idxs[r] - idxs[l] - (r - l)

        if diff &gt; flaw:
            l &#43;&#61; 1
        elif diff &lt; flaw:
            r &#43;&#61; 1
        else:
            ans &#61; max(ans, idxs[r] - idxs[l] &#43; 1)
            r &#43;&#61; 1

    return ans


# 算法调用
print(getResult())
</code></pre> 
<p></p> 
<h4 style="background-color:transparent;">C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;
#include &lt;string.h&gt;

#define MAX(a,b) ((a) &gt; (b) ? (a) : (b))

#define MAX_SIZE 65535

int main() {
    int flaw;
    scanf(&#34;%d&#34;, &amp;flaw);

    char s[MAX_SIZE];
    scanf(&#34;%s&#34;, s);

    char* yuan &#61; &#34;aeiouAEIOU&#34;;

    int idxs[MAX_SIZE];
    int idxs_size &#61; 0;

    int i &#61; 0;
    while (s[i] !&#61; &#39;\0&#39;) {
        if(strchr(yuan, s[i]) !&#61; NULL) {
            idxs[idxs_size&#43;&#43;] &#61; i;
        }
        i&#43;&#43;;
    }

    int ans &#61; 0;
    int n &#61; idxs_size;

    int l &#61; 0;
    int r &#61; 0;

    while(r &lt; n) {
        // 瑕疵度计算
        int diff &#61; idxs[r] - idxs[l] - (r - l);

        if(diff &gt; flaw) {
            l&#43;&#43;;
        } else if(diff &lt; flaw) {
            r&#43;&#43;;
        } else {
            ans &#61; MAX(ans, idxs[r] - idxs[l] &#43; 1);
            r&#43;&#43;;
        }
    }

    printf(&#34;%d\n&#34;, ans);

    return 0;
}</code></pre>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>