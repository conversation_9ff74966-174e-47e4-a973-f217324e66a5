<!doctype html><html><head><meta charset='UTF-8'><meta name='viewport' content='width=device-width initial-scale=1'><link href='https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext' rel='stylesheet' type='text/css' /><style type='text/css'>html {overflow-x: initial !important;}:root { --bg-color: #ffffff; --text-color: #333333; --select-text-bg-color: #B5D6FC; --select-text-font-color: auto; --monospace: "Lucida Console",Consolas,"Courier",monospace; --title-bar-height: 20px; }.mac-os-11 { --title-bar-height: 28px; }html { font-size: 14px; background-color: var(--bg-color); color: var(--text-color); font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; -webkit-font-smoothing: antialiased; }h1, h2, h3, h4, h5 { white-space: pre-wrap; }body { margin: 0px; padding: 0px; height: auto; inset: 0px; font-size: 1rem; line-height: 1.42857; overflow-x: hidden; background: inherit; }iframe { margin: auto; }a.url { word-break: break-all; }a:active, a:hover { outline: 0px; }.in-text-selection, ::selection { text-shadow: none; background: var(--select-text-bg-color); color: var(--select-text-font-color); }#write { margin: 0px auto; height: auto; width: inherit; word-break: normal; overflow-wrap: break-word; position: relative; white-space: normal; overflow-x: visible; padding-top: 36px; }#write.first-line-indent p { text-indent: 2em; }#write.first-line-indent li p, #write.first-line-indent p * { text-indent: 0px; }#write.first-line-indent li { margin-left: 2em; }.for-image #write { padding-left: 8px; padding-right: 8px; }body.typora-export { padding-left: 30px; padding-right: 30px; }.typora-export .footnote-line, .typora-export li, .typora-export p { white-space: pre-wrap; }.typora-export .task-list-item input { pointer-events: none; }@media screen and (max-width: 500px) {  body.typora-export { padding-left: 0px; padding-right: 0px; }  #write { padding-left: 20px; padding-right: 20px; }}#write li > figure:last-child { margin-bottom: 0.5rem; }#write ol, #write ul { position: relative; }img { max-width: 100%; vertical-align: middle; image-orientation: from-image; }button, input, select, textarea { color: inherit; font: inherit; }input[type="checkbox"], input[type="radio"] { line-height: normal; padding: 0px; }*, ::after, ::before { box-sizing: border-box; }#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p, #write pre { width: inherit; }#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p { position: relative; }p { line-height: inherit; }h1, h2, h3, h4, h5, h6 { break-after: avoid-page; break-inside: avoid; orphans: 4; }p { orphans: 4; }h1 { font-size: 2rem; }h2 { font-size: 1.8rem; }h3 { font-size: 1.6rem; }h4 { font-size: 1.4rem; }h5 { font-size: 1.2rem; }h6 { font-size: 1rem; }.md-math-block, .md-rawblock, h1, h2, h3, h4, h5, h6, p { margin-top: 1rem; margin-bottom: 1rem; }.hidden { display: none; }.md-blockmeta { color: rgb(204, 204, 204); font-weight: 700; font-style: italic; }a { cursor: pointer; }sup.md-footnote { padding: 2px 4px; background-color: rgba(238, 238, 238, 0.7); color: rgb(85, 85, 85); border-radius: 4px; cursor: pointer; }sup.md-footnote a, sup.md-footnote a:hover { color: inherit; text-transform: inherit; text-decoration: inherit; }#write input[type="checkbox"] { cursor: pointer; width: inherit; height: inherit; }figure { overflow-x: auto; margin: 1.2em 0px; max-width: calc(100% + 16px); padding: 0px; }figure > table { margin: 0px; }thead, tr { break-inside: avoid; break-after: auto; }thead { display: table-header-group; }table { border-collapse: collapse; border-spacing: 0px; width: 100%; overflow: auto; break-inside: auto; text-align: left; }table.md-table td { min-width: 32px; }.CodeMirror-gutters { border-right: 0px; background-color: inherit; }.CodeMirror-linenumber { user-select: none; }.CodeMirror { text-align: left; }.CodeMirror-placeholder { opacity: 0.3; }.CodeMirror pre { padding: 0px 4px; }.CodeMirror-lines { padding: 0px; }div.hr:focus { cursor: none; }#write pre { white-space: pre-wrap; }#write.fences-no-line-wrapping pre { white-space: pre; }#write pre.ty-contain-cm { white-space: normal; }.CodeMirror-gutters { margin-right: 4px; }.md-fences { font-size: 0.9rem; display: block; break-inside: avoid; text-align: left; overflow: visible; white-space: pre; background: inherit; position: relative !important; }.md-fences-adv-panel { width: 100%; margin-top: 10px; text-align: center; padding-top: 0px; padding-bottom: 8px; overflow-x: auto; }#write .md-fences.mock-cm { white-space: pre-wrap; }.md-fences.md-fences-with-lineno { padding-left: 0px; }#write.fences-no-line-wrapping .md-fences.mock-cm { white-space: pre; overflow-x: auto; }.md-fences.mock-cm.md-fences-with-lineno { padding-left: 8px; }.CodeMirror-line, twitterwidget { break-inside: avoid; }svg { break-inside: avoid; }.footnotes { opacity: 0.8; font-size: 0.9rem; margin-top: 1em; margin-bottom: 1em; }.footnotes + .footnotes { margin-top: 0px; }.md-reset { margin: 0px; padding: 0px; border: 0px; outline: 0px; vertical-align: top; background: 0px 0px; text-decoration: none; text-shadow: none; float: none; position: static; width: auto; height: auto; white-space: nowrap; cursor: inherit; -webkit-tap-highlight-color: transparent; line-height: normal; font-weight: 400; text-align: left; box-sizing: content-box; direction: ltr; }li div { padding-top: 0px; }blockquote { margin: 1rem 0px; }li .mathjax-block, li p { margin: 0.5rem 0px; }li blockquote { margin: 1rem 0px; }li { margin: 0px; position: relative; }blockquote > :last-child { margin-bottom: 0px; }blockquote > :first-child, li > :first-child { margin-top: 0px; }.footnotes-area { color: rgb(136, 136, 136); margin-top: 0.714rem; padding-bottom: 0.143rem; white-space: normal; }#write .footnote-line { white-space: pre-wrap; }@media print {  body, html { border: 1px solid transparent; height: 99%; break-after: avoid; break-before: avoid; font-variant-ligatures: no-common-ligatures; }  #write { margin-top: 0px; border-color: transparent !important; padding-top: 0px !important; padding-bottom: 0px !important; }  .typora-export * { -webkit-print-color-adjust: exact; }  .typora-export #write { break-after: avoid; }  .typora-export #write::after { height: 0px; }  .is-mac table { break-inside: avoid; }  #write > p:nth-child(1) { margin-top: 0px; }  .typora-export-show-outline .typora-export-sidebar { display: none; }  figure { overflow-x: visible; }}.footnote-line { margin-top: 0.714em; font-size: 0.7em; }a img, img a { cursor: pointer; }pre.md-meta-block { font-size: 0.8rem; min-height: 0.8rem; white-space: pre-wrap; background: rgb(204, 204, 204); display: block; overflow-x: hidden; }p > .md-image:only-child:not(.md-img-error) img, p > img:only-child { display: block; margin: auto; }#write.first-line-indent p > .md-image:only-child:not(.md-img-error) img { left: -2em; position: relative; }p > .md-image:only-child { display: inline-block; width: 100%; }#write .MathJax_Display { margin: 0.8em 0px 0px; }.md-math-block { width: 100%; }.md-math-block:not(:empty)::after { display: none; }.MathJax_ref { fill: currentcolor; }[contenteditable="true"]:active, [contenteditable="true"]:focus, [contenteditable="false"]:active, [contenteditable="false"]:focus { outline: 0px; box-shadow: none; }.md-task-list-item { position: relative; list-style-type: none; }.task-list-item.md-task-list-item { padding-left: 0px; }.md-task-list-item > input { position: absolute; top: 0px; left: 0px; margin-left: -1.2em; margin-top: calc(1em - 10px); border: none; }.math { font-size: 1rem; }.md-toc { min-height: 3.58rem; position: relative; font-size: 0.9rem; border-radius: 10px; }.md-toc-content { position: relative; margin-left: 0px; }.md-toc-content::after, .md-toc::after { display: none; }.md-toc-item { display: block; color: rgb(65, 131, 196); }.md-toc-item a { text-decoration: none; }.md-toc-inner:hover { text-decoration: underline; }.md-toc-inner { display: inline-block; cursor: pointer; }.md-toc-h1 .md-toc-inner { margin-left: 0px; font-weight: 700; }.md-toc-h2 .md-toc-inner { margin-left: 2em; }.md-toc-h3 .md-toc-inner { margin-left: 4em; }.md-toc-h4 .md-toc-inner { margin-left: 6em; }.md-toc-h5 .md-toc-inner { margin-left: 8em; }.md-toc-h6 .md-toc-inner { margin-left: 10em; }@media screen and (max-width: 48em) {  .md-toc-h3 .md-toc-inner { margin-left: 3.5em; }  .md-toc-h4 .md-toc-inner { margin-left: 5em; }  .md-toc-h5 .md-toc-inner { margin-left: 6.5em; }  .md-toc-h6 .md-toc-inner { margin-left: 8em; }}a.md-toc-inner { font-size: inherit; font-style: inherit; font-weight: inherit; line-height: inherit; }.footnote-line a:not(.reversefootnote) { color: inherit; }.reversefootnote { font-family: ui-monospace, sans-serif; }.md-attr { display: none; }.md-fn-count::after { content: "."; }code, pre, samp, tt { font-family: var(--monospace); }kbd { margin: 0px 0.1em; padding: 0.1em 0.6em; font-size: 0.8em; color: rgb(36, 39, 41); background: rgb(255, 255, 255); border: 1px solid rgb(173, 179, 185); border-radius: 3px; box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px, rgb(255, 255, 255) 0px 0px 0px 2px inset; white-space: nowrap; vertical-align: middle; }.md-comment { color: rgb(162, 127, 3); opacity: 0.6; font-family: var(--monospace); }code { text-align: left; vertical-align: initial; }a.md-print-anchor { white-space: pre !important; border-width: initial !important; border-style: none !important; border-color: initial !important; display: inline-block !important; position: absolute !important; width: 1px !important; right: 0px !important; outline: 0px !important; background: 0px 0px !important; text-decoration: initial !important; text-shadow: initial !important; }.os-windows.monocolor-emoji .md-emoji { font-family: "Segoe UI Symbol", sans-serif; }.md-diagram-panel > svg { max-width: 100%; }[lang="flow"] svg, [lang="mermaid"] svg { max-width: 100%; height: auto; }[lang="mermaid"] .node text { font-size: 1rem; }table tr th { border-bottom: 0px; }video { max-width: 100%; display: block; margin: 0px auto; }iframe { max-width: 100%; width: 100%; border: none; }.highlight td, .highlight tr { border: 0px; }mark { background: rgb(255, 255, 0); color: rgb(0, 0, 0); }.md-html-inline .md-plain, .md-html-inline strong, mark .md-inline-math, mark strong { color: inherit; }.md-expand mark .md-meta { opacity: 0.3 !important; }mark .md-meta { color: rgb(0, 0, 0); }@media print {  .typora-export h1, .typora-export h2, .typora-export h3, .typora-export h4, .typora-export h5, .typora-export h6 { break-inside: avoid; }}.md-diagram-panel .messageText { stroke: none !important; }.md-diagram-panel .start-state { fill: var(--node-fill); }.md-diagram-panel .edgeLabel rect { opacity: 1 !important; }.md-fences.md-fences-math { font-size: 1em; }.md-fences-advanced:not(.md-focus) { padding: 0px; white-space: nowrap; border: 0px; }.md-fences-advanced:not(.md-focus) { background: inherit; }.typora-export-show-outline .typora-export-content { max-width: 1440px; margin: auto; display: flex; flex-direction: row; }.typora-export-sidebar { width: 300px; font-size: 0.8rem; margin-top: 80px; margin-right: 18px; }.typora-export-show-outline #write { --webkit-flex: 2; flex: 2 1 0%; }.typora-export-sidebar .outline-content { position: fixed; top: 0px; max-height: 100%; overflow: hidden auto; padding-bottom: 30px; padding-top: 60px; width: 300px; }@media screen and (max-width: 1024px) {  .typora-export-sidebar, .typora-export-sidebar .outline-content { width: 240px; }}@media screen and (max-width: 800px) {  .typora-export-sidebar { display: none; }}.outline-content li, .outline-content ul { margin-left: 0px; margin-right: 0px; padding-left: 0px; padding-right: 0px; list-style: none; overflow-wrap: anywhere; }.outline-content ul { margin-top: 0px; margin-bottom: 0px; }.outline-content strong { font-weight: 400; }.outline-expander { width: 1rem; height: 1.42857rem; position: relative; display: table-cell; vertical-align: middle; cursor: pointer; padding-left: 4px; }.outline-expander::before { content: ""; position: relative; font-family: Ionicons; display: inline-block; font-size: 8px; vertical-align: middle; }.outline-item { padding-top: 3px; padding-bottom: 3px; cursor: pointer; }.outline-expander:hover::before { content: ""; }.outline-h1 > .outline-item { padding-left: 0px; }.outline-h2 > .outline-item { padding-left: 1em; }.outline-h3 > .outline-item { padding-left: 2em; }.outline-h4 > .outline-item { padding-left: 3em; }.outline-h5 > .outline-item { padding-left: 4em; }.outline-h6 > .outline-item { padding-left: 5em; }.outline-label { cursor: pointer; display: table-cell; vertical-align: middle; text-decoration: none; color: inherit; }.outline-label:hover { text-decoration: underline; }.outline-item:hover { border-color: rgb(245, 245, 245); background-color: var(--item-hover-bg-color); }.outline-item:hover { margin-left: -28px; margin-right: -28px; border-left: 28px solid transparent; border-right: 28px solid transparent; }.outline-item-single .outline-expander::before, .outline-item-single .outline-expander:hover::before { display: none; }.outline-item-open > .outline-item > .outline-expander::before { content: ""; }.outline-children { display: none; }.info-panel-tab-wrapper { display: none; }.outline-item-open > .outline-children { display: block; }.typora-export .outline-item { padding-top: 1px; padding-bottom: 1px; }.typora-export .outline-item:hover { margin-right: -8px; border-right: 8px solid transparent; }.typora-export .outline-expander::before { content: "+"; font-family: inherit; top: -1px; }.typora-export .outline-expander:hover::before, .typora-export .outline-item-open > .outline-item > .outline-expander::before { content: "−"; }.typora-export-collapse-outline .outline-children { display: none; }.typora-export-collapse-outline .outline-item-open > .outline-children, .typora-export-no-collapse-outline .outline-children { display: block; }.typora-export-no-collapse-outline .outline-expander::before { content: "" !important; }.typora-export-show-outline .outline-item-active > .outline-item .outline-label { font-weight: 700; }.md-inline-math-container mjx-container { zoom: 0.95; }mjx-container { break-inside: avoid; }.md-alert.md-alert-note { border-left-color: rgb(9, 105, 218); }.md-alert.md-alert-important { border-left-color: rgb(130, 80, 223); }.md-alert.md-alert-warning { border-left-color: rgb(154, 103, 0); }.md-alert.md-alert-tip { border-left-color: rgb(31, 136, 61); }.md-alert.md-alert-caution { border-left-color: rgb(207, 34, 46); }.md-alert { padding: 0px 1em; margin-bottom: 16px; color: inherit; border-left: 0.25em solid rgb(0, 0, 0); }.md-alert-text-note { color: rgb(9, 105, 218); }.md-alert-text-important { color: rgb(130, 80, 223); }.md-alert-text-warning { color: rgb(154, 103, 0); }.md-alert-text-tip { color: rgb(31, 136, 61); }.md-alert-text-caution { color: rgb(207, 34, 46); }.md-alert-text { font-size: 0.9rem; font-weight: 700; }.md-alert-text svg { fill: currentcolor; position: relative; top: 0.125em; margin-right: 1ch; overflow: visible; }.md-alert-text-container::after { content: attr(data-text); text-transform: capitalize; pointer-events: none; margin-right: 1ch; }.CodeMirror { height: auto; }.CodeMirror.cm-s-inner { background: inherit; }.CodeMirror-scroll { overflow: auto hidden; z-index: 3; }.CodeMirror-gutter-filler, .CodeMirror-scrollbar-filler { background-color: rgb(255, 255, 255); }.CodeMirror-gutters { border-right: 1px solid rgb(221, 221, 221); background: inherit; white-space: nowrap; }.CodeMirror-linenumber { padding: 0px 3px 0px 5px; text-align: right; color: rgb(153, 153, 153); }.cm-s-inner .cm-keyword { color: rgb(119, 0, 136); }.cm-s-inner .cm-atom, .cm-s-inner.cm-atom { color: rgb(34, 17, 153); }.cm-s-inner .cm-number { color: rgb(17, 102, 68); }.cm-s-inner .cm-def { color: rgb(0, 0, 255); }.cm-s-inner .cm-variable { color: rgb(0, 0, 0); }.cm-s-inner .cm-variable-2 { color: rgb(0, 85, 170); }.cm-s-inner .cm-variable-3 { color: rgb(0, 136, 85); }.cm-s-inner .cm-string { color: rgb(170, 17, 17); }.cm-s-inner .cm-property { color: rgb(0, 0, 0); }.cm-s-inner .cm-operator { color: rgb(152, 26, 26); }.cm-s-inner .cm-comment, .cm-s-inner.cm-comment { color: rgb(170, 85, 0); }.cm-s-inner .cm-string-2 { color: rgb(255, 85, 0); }.cm-s-inner .cm-meta { color: rgb(85, 85, 85); }.cm-s-inner .cm-qualifier { color: rgb(85, 85, 85); }.cm-s-inner .cm-builtin { color: rgb(51, 0, 170); }.cm-s-inner .cm-bracket { color: rgb(153, 153, 119); }.cm-s-inner .cm-tag { color: rgb(17, 119, 0); }.cm-s-inner .cm-attribute { color: rgb(0, 0, 204); }.cm-s-inner .cm-header, .cm-s-inner.cm-header { color: rgb(0, 0, 255); }.cm-s-inner .cm-quote, .cm-s-inner.cm-quote { color: rgb(0, 153, 0); }.cm-s-inner .cm-hr, .cm-s-inner.cm-hr { color: rgb(153, 153, 153); }.cm-s-inner .cm-link, .cm-s-inner.cm-link { color: rgb(0, 0, 204); }.cm-negative { color: rgb(221, 68, 68); }.cm-positive { color: rgb(34, 153, 34); }.cm-header, .cm-strong { font-weight: 700; }.cm-del { text-decoration: line-through; }.cm-em { font-style: italic; }.cm-link { text-decoration: underline; }.cm-error { color: red; }.cm-invalidchar { color: red; }.cm-constant { color: rgb(38, 139, 210); }.cm-defined { color: rgb(181, 137, 0); }div.CodeMirror span.CodeMirror-matchingbracket { color: rgb(0, 255, 0); }div.CodeMirror span.CodeMirror-nonmatchingbracket { color: rgb(255, 34, 34); }.cm-s-inner .CodeMirror-activeline-background { background: inherit; }.CodeMirror { position: relative; overflow: hidden; }.CodeMirror-scroll { height: 100%; outline: 0px; position: relative; box-sizing: content-box; background: inherit; }.CodeMirror-sizer { position: relative; }.CodeMirror-gutter-filler, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-vscrollbar { position: absolute; z-index: 6; display: none; outline: 0px; }.CodeMirror-vscrollbar { right: 0px; top: 0px; overflow: hidden; }.CodeMirror-hscrollbar { bottom: 0px; left: 0px; overflow: auto hidden; }.CodeMirror-scrollbar-filler { right: 0px; bottom: 0px; }.CodeMirror-gutter-filler { left: 0px; bottom: 0px; }.CodeMirror-gutters { position: absolute; left: 0px; top: 0px; padding-bottom: 10px; z-index: 3; overflow-y: hidden; }.CodeMirror-gutter { white-space: normal; height: 100%; box-sizing: content-box; padding-bottom: 30px; margin-bottom: -32px; display: inline-block; }.CodeMirror-gutter-wrapper { position: absolute; z-index: 4; background: 0px 0px !important; border: none !important; }.CodeMirror-gutter-background { position: absolute; top: 0px; bottom: 0px; z-index: 4; }.CodeMirror-gutter-elt { position: absolute; cursor: default; z-index: 4; }.CodeMirror-lines { cursor: text; }.CodeMirror pre { border-radius: 0px; border-width: 0px; background: 0px 0px; font-family: inherit; font-size: inherit; margin: 0px; white-space: pre; overflow-wrap: normal; color: inherit; z-index: 2; position: relative; overflow: visible; }.CodeMirror-wrap pre { overflow-wrap: break-word; white-space: pre-wrap; word-break: normal; }.CodeMirror-code pre { border-right: 30px solid transparent; width: fit-content; }.CodeMirror-wrap .CodeMirror-code pre { border-right: none; width: auto; }.CodeMirror-linebackground { position: absolute; inset: 0px; z-index: 0; }.CodeMirror-linewidget { position: relative; z-index: 2; overflow: auto; }.CodeMirror-wrap .CodeMirror-scroll { overflow-x: hidden; }.CodeMirror-measure { position: absolute; width: 100%; height: 0px; overflow: hidden; visibility: hidden; }.CodeMirror-measure pre { position: static; }.CodeMirror div.CodeMirror-cursor { position: absolute; visibility: hidden; border-right: none; width: 0px; }.CodeMirror div.CodeMirror-cursor { visibility: hidden; }.CodeMirror-focused div.CodeMirror-cursor { visibility: inherit; }.cm-searching { background: rgba(255, 255, 0, 0.4); }span.cm-underlined { text-decoration: underline; }span.cm-strikethrough { text-decoration: line-through; }.cm-tw-syntaxerror { color: rgb(255, 255, 255); background-color: rgb(153, 0, 0); }.cm-tw-deleted { text-decoration: line-through; }.cm-tw-header5 { font-weight: 700; }.cm-tw-listitem:first-child { padding-left: 10px; }.cm-tw-box { border-style: solid; border-right-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-color: inherit; border-top-width: 0px !important; }.cm-tw-underline { text-decoration: underline; }@media print {  .CodeMirror div.CodeMirror-cursor { visibility: hidden; }}:root {    --side-bar-bg-color: #fafafa;    --control-text-color: #777;}@include-when-export url(https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext);/* open-sans-regular - latin-ext_latin */  /* open-sans-italic - latin-ext_latin */    /* open-sans-700 - latin-ext_latin */    /* open-sans-700italic - latin-ext_latin */  html {    font-size: 16px;    -webkit-font-smoothing: antialiased;}body {    font-family: "Open Sans","Clear Sans", "Helvetica Neue", Helvetica, Arial, 'Segoe UI Emoji', sans-serif;    color: rgb(51, 51, 51);    line-height: 1.6;}#write {    max-width: 860px;  	margin: 0 auto;  	padding: 30px;    padding-bottom: 100px;}@media only screen and (min-width: 1400px) {	#write {		max-width: 1024px;	}}@media only screen and (min-width: 1800px) {	#write {		max-width: 1200px;	}}#write > ul:first-child,#write > ol:first-child{    margin-top: 30px;}a {    color: #4183C4;}h1,h2,h3,h4,h5,h6 {    position: relative;    margin-top: 1rem;    margin-bottom: 1rem;    font-weight: bold;    line-height: 1.4;    cursor: text;}h1:hover a.anchor,h2:hover a.anchor,h3:hover a.anchor,h4:hover a.anchor,h5:hover a.anchor,h6:hover a.anchor {    text-decoration: none;}h1 tt,h1 code {    font-size: inherit;}h2 tt,h2 code {    font-size: inherit;}h3 tt,h3 code {    font-size: inherit;}h4 tt,h4 code {    font-size: inherit;}h5 tt,h5 code {    font-size: inherit;}h6 tt,h6 code {    font-size: inherit;}h1 {    font-size: 2.25em;    line-height: 1.2;    border-bottom: 1px solid #eee;}h2 {    font-size: 1.75em;    line-height: 1.225;    border-bottom: 1px solid #eee;}/*@media print {    .typora-export h1,    .typora-export h2 {        border-bottom: none;        padding-bottom: initial;    }    .typora-export h1::after,    .typora-export h2::after {        content: "";        display: block;        height: 100px;        margin-top: -96px;        border-top: 1px solid #eee;    }}*/h3 {    font-size: 1.5em;    line-height: 1.43;}h4 {    font-size: 1.25em;}h5 {    font-size: 1em;}h6 {   font-size: 1em;    color: #777;}p,blockquote,ul,ol,dl,table{    margin: 0.8em 0;}li>ol,li>ul {    margin: 0 0;}hr {    height: 2px;    padding: 0;    margin: 16px 0;    background-color: #e7e7e7;    border: 0 none;    overflow: hidden;    box-sizing: content-box;}li p.first {    display: inline-block;}ul,ol {    padding-left: 30px;}ul:first-child,ol:first-child {    margin-top: 0;}ul:last-child,ol:last-child {    margin-bottom: 0;}blockquote {    border-left: 4px solid #dfe2e5;    padding: 0 15px;    color: #777777;}blockquote blockquote {    padding-right: 0;}table {    padding: 0;    word-break: initial;}table tr {    border: 1px solid #dfe2e5;    margin: 0;    padding: 0;}table tr:nth-child(2n),thead {    background-color: #f8f8f8;}table th {    font-weight: bold;    border: 1px solid #dfe2e5;    border-bottom: 0;    margin: 0;    padding: 6px 13px;}table td {    border: 1px solid #dfe2e5;    margin: 0;    padding: 6px 13px;}table th:first-child,table td:first-child {    margin-top: 0;}table th:last-child,table td:last-child {    margin-bottom: 0;}.CodeMirror-lines {    padding-left: 4px;}.code-tooltip {    box-shadow: 0 1px 1px 0 rgba(0,28,36,.3);    border-top: 1px solid #eef2f2;}.md-fences,code,tt {    border: 1px solid #e7eaed;    background-color: #f8f8f8;    border-radius: 3px;    padding: 0;    padding: 2px 4px 0px 4px;    font-size: 0.9em;}code {    background-color: #f3f4f4;    padding: 0 2px 0 2px;}.md-fences {    margin-bottom: 15px;    margin-top: 15px;    padding-top: 8px;    padding-bottom: 6px;}.md-task-list-item > input {  margin-left: -1.3em;}@media print {    html {        font-size: 13px;    }    pre {        page-break-inside: avoid;        word-wrap: break-word;    }}.md-fences {	background-color: #f8f8f8;}#write pre.md-meta-block {	padding: 1rem;    font-size: 85%;    line-height: 1.45;    background-color: #f7f7f7;    border: 0;    border-radius: 3px;    color: #777777;    margin-top: 0 !important;}.mathjax-block>.code-tooltip {	bottom: .375rem;}.md-mathjax-midline {    background: #fafafa;}#write>h3.md-focus:before{	left: -1.5625rem;	top: .375rem;}#write>h4.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}#write>h5.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}#write>h6.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}.md-image>.md-meta {    /*border: 1px solid #ddd;*/    border-radius: 3px;    padding: 2px 0px 0px 4px;    font-size: 0.9em;    color: inherit;}.md-tag {    color: #a7a7a7;    opacity: 1;}.md-toc {     margin-top:20px;    padding-bottom:20px;}.sidebar-tabs {    border-bottom: none;}#typora-quick-open {    border: 1px solid #ddd;    background-color: #f8f8f8;}#typora-quick-open-item {    background-color: #FAFAFA;    border-color: #FEFEFE #e5e5e5 #e5e5e5 #eee;    border-style: solid;    border-width: 1px;}/** focus mode */.on-focus-mode blockquote {    border-left-color: rgba(85, 85, 85, 0.12);}header, .context-menu, .megamenu-content, footer{    font-family: "Segoe UI", "Arial", sans-serif;}.file-node-content:hover .file-node-icon,.file-node-content:hover .file-node-open-state{    visibility: visible;}.mac-seamless-mode #typora-sidebar {    background-color: #fafafa;    background-color: var(--side-bar-bg-color);}.mac-os #write{    caret-color: AccentColor;}.md-lang {    color: #b4654d;}/*.html-for-mac {    --item-hover-bg-color: #E6F0FE;}*/#md-notification .btn {    border: 0;}.dropdown-menu .divider {    border-color: #e5e5e5;    opacity: 0.4;}.ty-preferences .window-content {    background-color: #fafafa;}.ty-preferences .nav-group-item.active {    color: white;    background: #999;}.menu-item-container a.menu-style-btn {    background-color: #f5f8fa;    background-image: linear-gradient( 180deg , hsla(0, 0%, 100%, 0.8), hsla(0, 0%, 100%, 0)); }</style><title>【华为OD-E卷 -62 模拟消息队列 100分（python、java、c++、js、c）】</title></head><body class='typora-export os-windows typora-export-show-outline typora-export-no-collapse-outline'><div class='typora-export-content'><div class="typora-export-sidebar"><div class="outline-content"><li class="outline-item-wrapper outline-h2"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#注意点此-链接-其他算法题目详细解法">注意：点此 链接 其他算法题目详细解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h2"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#投稿交流错误反馈-v">投稿、交流、错误反馈 +V：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#"></a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#华为od-e卷---模拟消息队列-100分pythonjavacjsc）">【华为OD-E卷 - 模拟消息队列 100分（python、java、c++、js、c）】</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#题目">题目</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h3"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入描述">输入描述</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h3"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出描述">输出描述</a></div><ul class="outline-children"></ul></li></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例">用例</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h4"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例一">用例一：</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入">输入：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出">输出：</a></div><ul class="outline-children"></ul></li></ul></li><li class="outline-item-wrapper outline-h4"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例二">用例二：</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入-2">输入：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出-2">输出：</a></div><ul class="outline-children"></ul></li></ul></li></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#python解法">python解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#java解法">java解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#c解法">C++解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#c解法-2">C解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#js解法">JS解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#注意">注意：</a></div><ul class="outline-children"></ul></li></div></div><div id='write'  class=''><h2 id='注意点此-链接-其他算法题目详细解法'><a href='https://blog.csdn.net/codeclimb/category_12842753.html?spm=1001.2014.3001.5482'><span>注意：点此 链接 其他算法题目详细解法</span></a></h2><h2 id='投稿交流错误反馈-v'><span>投稿、交流、错误反馈 +V：</span></h2><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="http"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="http"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-error">locally-attached</span></span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h1></h1><h1 id='华为od-e卷---模拟消息队列-100分pythonjavacjsc）'><span>【华为OD-E卷 - 模拟消息队列 100分（python、java、c++、js、c）】</span></h1><h1 id='题目'><span>题目</span></h1><p><strong><span>让我们来模拟一个消息队列的运作，有一个发布者和若干消费者，发布者会在给定的时刻向消息队列发送消息，</span><span>若此时消息队列有消费者订阅，这个消息会被发送到订阅的消费者中优先级最高（输入中消费者按优先级升序排列）的一个； 若此时没有订阅的消费者，该消息被消息队列丢弃。 消费者则会在给定的时刻订阅消息队列或取消订阅。</span><span>当消息发送和订阅发生在同一时刻时，先处理订阅操作，即同一时刻订阅的消费者成为消息发送的候选。 当消息发送和取消订阅发生在同一时刻时，先处理取消订阅操作，即消息不会被发送到同一时刻取消订阅的消费者。</span></strong></p><h3 id='输入描述'><span>输入描述</span></h3><ul><li><p><span>输入为两行。</span></p></li></ul><p><span>第一行为2N个正整数，代表发布者发送的N个消息的时刻和内容（为方便解折，消息内容也用正整数表示）。第一个数字是第一个消息的发送时刻，第二个数字是第一个消息的内容，以此类推。用例保证发送时刻不会重复，但注意消息并没有按照发送时刻排列。</span></p><p><span>第二行为2M个正整数，代表M个消费者订阅和取消订阅的时刻。第一个数字是第一个消费者订阅的时刻，第二个数字是第一个消费者取消订阅的时刻，以此类推。用例保证每个消费者的取消订阅时刻大于订阅时刻，消费者按优先级升序排列。</span></p><p><span>两行的数字都由空格分隔。N不超过100，M不超过10，每行的长度不超过1000字符</span></p><h3 id='输出描述'><span>输出描述</span></h3><ul><li><p><span>输出为M行，依次为M个消费者收到的消息内容，消息内容按收到的顺序排列，且由空格分隔；</span></p></li></ul><p><span>若某个消费者没有收到任何消息，则对应的行输出-1</span></p><p>&nbsp;</p><h1 id='用例'><span>用例</span></h1><h4 id='用例一'><span>用例一：</span></h4><h5 id='输入'><span>输入：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">2 22 1 11 4 44 5 55 3 33</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">1 7 2 3</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 46px;"></div><div class="CodeMirror-gutters" style="display: none; height: 46px;"></div></div></div></pre><h5 id='输出'><span>输出：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">11 33 44 55</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">22</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 46px;"></div><div class="CodeMirror-gutters" style="display: none; height: 46px;"></div></div></div></pre><p><span>说明 消息11在1时刻到达，此时只有第一个消费者订阅，消息发送给它；</span></p><p><span>消息22在2时刻到达，此时两个消费者都订阅了，消息发送给优先级最高的第二个消费者；</span></p><p><span>消息33在时刻3到达，此时只有第一个消费者订阅，消息发送给它；</span></p><p><span>余下的消息按规则也是发送给第一个消费者</span></p><h4 id='用例二'><span>用例二：</span></h4><h5 id='输入-2'><span>输入：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">5 64 11 64 9 97</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">9 11 4 9</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 46px;"></div><div class="CodeMirror-gutters" style="display: none; height: 46px;"></div></div></div></pre><h5 id='输出-2'><span>输出：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">97</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">64</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 46px;"></div><div class="CodeMirror-gutters" style="display: none; height: 46px;"></div></div></div></pre><p><span>说明 消息64在5时刻到达，此时只有第二个消费者订阅，消息发送给它；</span></p><p><span>消息97在9时刻到达，此时只有第一消费者订阅(因为第二个消费者刚好在9时刻取消订阅)，消息发送给它;</span></p><p><span>11时刻也到达了一个内容为64的消息，不过因为没有消费者订阅，消息被丢弃</span></p><h1 id='python解法'><span>python解法</span></h1><ul><li><p><span>解题思路：</span></p></li><li><p><span>解题思路</span><span>本题的目的是模拟消息的传递，给定两个数组，一个表示发布者发布消息的时间及内容，另一个表示订阅者的订阅时间和退订时间。我们需要根据每个发布者发布消息的时间和每个订阅者的订阅时间以及退订时间，判断订阅者能接收到哪些消息。</span></p></li></ul><p><span>解决步骤：</span><span>输入解析：</span></p><p><span>pubArr 数组包含一对一对的发布者发布消息的时间和消息内容，依次组成 (publish_time, content)。</span><span>subArr 数组包含一对一对的订阅者的订阅时间和退订时间，依次组成 (sub_time, unsub_time)。</span><span>处理发布者与订阅者信息：</span></p><p><span>将发布者的时间与消息内容组合成一个元组，并按发布的时间排序，确保我们按时间顺序处理消息。</span><span>将订阅者的订阅时间和退订时间组合成一个元组。</span><span>创建一个 subscriber_content 列表，用于存储每个订阅者所接收到的消息内容。</span><span>模拟消息的传递：</span></p><p><span>遍历所有发布者，对于每个发布者，在其发布消息时，查看每个订阅者是否在有效的订阅期内（即订阅时间小于等于发布者的发布消息时间且退订时间大于等于发布消息时间）。</span><span>如果订阅者在有效订阅期内，则该订阅者接收到该消息，并把消息内容添加到该订阅者的消息列表中。</span><span>输出结果：</span></p><p><span>对每个订阅者，输出其接收到的所有消息。如果某个订阅者没有接收到任何消息，则输出 -1。</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="python" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="python"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><span><span>​</span>x</span></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">from</span> <span class="cm-variable">collections</span> <span class="cm-keyword">import</span> <span class="cm-variable">deque</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment"># 处理发布者和订阅者消息的函数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">def</span> <span class="cm-def">process_messages</span>(<span class="cm-variable">pubArr</span>, <span class="cm-variable">subArr</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 将发布者的发布信息转换成 (时间, 内容) 元组并存储</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">publishers</span> <span class="cm-operator">=</span> []</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> <span class="cm-variable">i</span> <span class="cm-keyword">in</span> <span class="cm-builtin">range</span>(<span class="cm-number">0</span>, <span class="cm-builtin">len</span>(<span class="cm-variable">pubArr</span>), <span class="cm-number">2</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">publishers</span>.<span class="cm-property">append</span>((<span class="cm-variable">pubArr</span>[<span class="cm-variable">i</span>], <span class="cm-variable">pubArr</span>[<span class="cm-variable">i</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>]))</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 将订阅者的订阅信息转换成 (订阅时间, 退订时间) 元组并存储</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">subscribers</span> <span class="cm-operator">=</span> []</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> <span class="cm-variable">i</span> <span class="cm-keyword">in</span> <span class="cm-builtin">range</span>(<span class="cm-number">0</span>, <span class="cm-builtin">len</span>(<span class="cm-variable">subArr</span>), <span class="cm-number">2</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subscribers</span>.<span class="cm-property">append</span>((<span class="cm-variable">subArr</span>[<span class="cm-variable">i</span>], <span class="cm-variable">subArr</span>[<span class="cm-variable">i</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>]))</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 按发布者的发布消息时间升序排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">publishers</span>.<span class="cm-property">sort</span>(<span class="cm-variable">key</span><span class="cm-operator">=</span><span class="cm-keyword">lambda</span> <span class="cm-variable">x</span>: <span class="cm-variable">x</span>[<span class="cm-number">0</span>])</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 初始化一个二维列表，用于存储每个订阅者接收到的消息内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">subscriber_content</span> <span class="cm-operator">=</span> [[] <span class="cm-keyword">for</span> <span class="cm-variable">_</span> <span class="cm-keyword">in</span> <span class="cm-builtin">range</span>(<span class="cm-builtin">len</span>(<span class="cm-variable">subscribers</span>))]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 遍历所有的发布者信息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> <span class="cm-variable">pub_time</span>, <span class="cm-variable">pub_content</span> <span class="cm-keyword">in</span> <span class="cm-variable">publishers</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 反向遍历订阅者，检查订阅者是否处于有效订阅期内</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> <span class="cm-variable">j</span> <span class="cm-keyword">in</span> <span class="cm-builtin">range</span>(<span class="cm-builtin">len</span>(<span class="cm-variable">subscribers</span>)<span class="cm-operator">-</span><span class="cm-number">1</span>, <span class="cm-operator">-</span><span class="cm-number">1</span>, <span class="cm-operator">-</span><span class="cm-number">1</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">sub_time</span>, <span class="cm-variable">unsub_time</span> <span class="cm-operator">=</span> <span class="cm-variable">subscribers</span>[<span class="cm-variable">j</span>]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 如果发布者的发布消息时间在订阅者的订阅期内，添加消息内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> <span class="cm-variable">unsub_time</span> <span class="cm-operator">&gt;</span> <span class="cm-variable">pub_time</span> <span class="cm-operator">&gt;=</span> <span class="cm-variable">sub_time</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subscriber_content</span>[<span class="cm-variable">j</span>].<span class="cm-property">append</span>(<span class="cm-variable">pub_content</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">break</span> &nbsp;<span class="cm-comment"># 找到一个有效的订阅者后就停止，避免重复分配消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 输出每个订阅者接收到的消息内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> <span class="cm-variable">contents</span> <span class="cm-keyword">in</span> <span class="cm-variable">subscriber_content</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 如果订阅者接收到的消息为空，输出 "-1"，否则输出消息内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-builtin">print</span>(<span class="cm-string">" "</span>.<span class="cm-property">join</span>(<span class="cm-builtin">map</span>(<span class="cm-builtin">str</span>, <span class="cm-variable">contents</span>)) <span class="cm-keyword">if</span> <span class="cm-variable">contents</span> <span class="cm-keyword">else</span> <span class="cm-string">"-1"</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment"># 输入数据：发布者发布的消息时间和内容，以及订阅者的订阅和退订时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">pubArr</span> <span class="cm-operator">=</span> <span class="cm-builtin">list</span>(<span class="cm-builtin">map</span>(<span class="cm-builtin">int</span>, <span class="cm-builtin">input</span>().<span class="cm-property">split</span>()))</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">subArr</span> <span class="cm-operator">=</span> <span class="cm-builtin">list</span>(<span class="cm-builtin">map</span>(<span class="cm-builtin">int</span>, <span class="cm-builtin">input</span>().<span class="cm-property">split</span>()))</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment"># 调用处理函数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">process_messages</span>(<span class="cm-variable">pubArr</span>, <span class="cm-variable">subArr</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 967px;"></div><div class="CodeMirror-gutters" style="display: none; height: 967px;"></div></div></div></pre><h1 id='java解法'><span>java解法</span></h1><ul><li><p><span>解题思路</span></p></li><li><p><span>解题思路</span><span>本题的核心任务是模拟消息发布者和订阅者之间的消息传递。给定两组数组，分别表示发布者的消息时间和内容，订阅者的订阅时间和退订时间。我们需要根据每个发布者发布消息的时间和每个订阅者的订阅和退订时间，判断每个订阅者能够接收到哪些消息。</span></p></li></ul><p><span>解决步骤：</span><span>输入数据：</span></p><p><span>msgArr：包含发布者的消息时间和内容，每两个数表示一个发布者发布消息的时间和消息内容。</span><span>consArr：包含订阅者的订阅时间和退订时间，每两个数表示一个订阅者的订阅时间和退订时间。</span><span>整理数据：</span></p><p><span>将 msgArr 按照每对时间和内容整理成二维数组 msgs，每个元素是一个长度为2的数组，存储消息的时间和内容。</span><span>将 consArr 按照每对订阅时间和退订时间整理成二维数组 consumers，每个元素是一个长度为2的数组，存储订阅时间和退订时间。</span><span>消息排序：</span></p><p><span>将消息按时间升序排列，这样可以确保我们按照发布顺序处理消息。</span><span>模拟消息传递：</span></p><p><span>对于每个消息，检查所有订阅者的有效订阅期（订阅时间小于等于消息时间且退订时间大于等于消息时间）。如果符合条件，订阅者接收到该消息，记录该消息。</span><span>输出结果：</span></p><p><span>对每个订阅者，输出其接收到的消息内容。如果订阅者没有接收到任何消息，输出 -1</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="java" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="java"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">java</span>.<span class="cm-variable">util</span>.<span class="cm-variable">ArrayList</span>;</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">java</span>.<span class="cm-variable">util</span>.<span class="cm-variable">Arrays</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">java</span>.<span class="cm-variable">util</span>.<span class="cm-variable">Scanner</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">java</span>.<span class="cm-variable">util</span>.<span class="cm-variable">StringJoiner</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">public</span> <span class="cm-keyword">class</span> <span class="cm-def">Main</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">public</span> <span class="cm-keyword">static</span> <span class="cm-variable-3">void</span> <span class="cm-variable">main</span>(<span class="cm-variable-3">String</span>[] <span class="cm-variable">args</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">Scanner</span> <span class="cm-variable">scanner</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">Scanner</span>(<span class="cm-variable">System</span>.<span class="cm-variable">in</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 读取并解析消息发布者的数据</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span>[] <span class="cm-variable">msgArr</span> <span class="cm-operator">=</span> <span class="cm-variable">Arrays</span>.<span class="cm-variable">stream</span>(<span class="cm-variable">scanner</span>.<span class="cm-variable">nextLine</span>().<span class="cm-variable">split</span>(<span class="cm-string">" "</span>)).<span class="cm-variable">mapToInt</span>(<span class="cm-variable-3">Integer</span>::<span class="cm-variable">parseInt</span>).<span class="cm-variable">toArray</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 读取并解析订阅者的数据</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span>[] <span class="cm-variable">consArr</span> <span class="cm-operator">=</span> <span class="cm-variable">Arrays</span>.<span class="cm-variable">stream</span>(<span class="cm-variable">scanner</span>.<span class="cm-variable">nextLine</span>().<span class="cm-variable">split</span>(<span class="cm-string">" "</span>)).<span class="cm-variable">mapToInt</span>(<span class="cm-variable-3">Integer</span>::<span class="cm-variable">parseInt</span>).<span class="cm-variable">toArray</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 调用处理消息的函数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">processMessages</span>(<span class="cm-variable">msgArr</span>, <span class="cm-variable">consArr</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 处理消息发布和订阅的函数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">public</span> <span class="cm-keyword">static</span> <span class="cm-variable-3">void</span> <span class="cm-variable">processMessages</span>(<span class="cm-variable-3">int</span>[] <span class="cm-variable">msgArr</span>, <span class="cm-variable-3">int</span>[] <span class="cm-variable">consArr</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">msgLen</span> <span class="cm-operator">=</span> <span class="cm-variable">msgArr</span>.<span class="cm-variable">length</span>; &nbsp;<span class="cm-comment">// 消息发布者数组的长度</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">consLen</span> <span class="cm-operator">=</span> <span class="cm-variable">consArr</span>.<span class="cm-variable">length</span>; &nbsp;<span class="cm-comment">// 订阅者数组的长度</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 将消息发布者的数据按时间和内容分组，存储为二维数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span>[][] <span class="cm-variable">msgs</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable-3">int</span>[<span class="cm-variable">msgLen</span> <span class="cm-operator">/</span> <span class="cm-number">2</span>][];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">mIdx</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">msgLen</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">msgs</span>[<span class="cm-variable">mIdx</span><span class="cm-operator">++</span>] <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable-3">int</span>[] {<span class="cm-variable">msgArr</span>[<span class="cm-variable">i</span>], <span class="cm-variable">msgArr</span>[<span class="cm-variable">i</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>]}; &nbsp;<span class="cm-comment">// 每个发布者的时间和内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">i</span> <span class="cm-operator">+=</span> <span class="cm-number">2</span>; &nbsp;<span class="cm-comment">// 步进2个元素</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 将订阅者的数据按订阅时间和退订时间分组，存储为二维数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span>[][] <span class="cm-variable">consumers</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable-3">int</span>[<span class="cm-variable">consLen</span> <span class="cm-operator">/</span> <span class="cm-number">2</span>][];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">j</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">cIdx</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">j</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">consLen</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">consumers</span>[<span class="cm-variable">cIdx</span><span class="cm-operator">++</span>] <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable-3">int</span>[] {<span class="cm-variable">consArr</span>[<span class="cm-variable">j</span>], <span class="cm-variable">consArr</span>[<span class="cm-variable">j</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>]}; &nbsp;<span class="cm-comment">// 每个订阅者的订阅时间和退订时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">j</span> <span class="cm-operator">+=</span> <span class="cm-number">2</span>; &nbsp;<span class="cm-comment">// 步进2个元素</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 按发布的时间对消息进行升序排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">Arrays</span>.<span class="cm-variable">sort</span>(<span class="cm-variable">msgs</span>, (<span class="cm-variable">x</span>, <span class="cm-variable">y</span>) <span class="cm-operator">-&gt;</span> <span class="cm-variable">x</span>[<span class="cm-number">0</span>] <span class="cm-operator">-</span> <span class="cm-variable">y</span>[<span class="cm-number">0</span>]);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 创建一个 ArrayList 用于存储每个订阅者接收到的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">ArrayList</span><span class="cm-operator">&lt;</span><span class="cm-variable">ArrayList</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">Integer</span><span class="cm-operator">&gt;&gt;</span> <span class="cm-variable">receivedMsgs</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">ArrayList</span><span class="cm-operator">&lt;&gt;</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">k</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">k</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">consumers</span>.<span class="cm-variable">length</span>; <span class="cm-variable">k</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">receivedMsgs</span>.<span class="cm-variable">add</span>(<span class="cm-keyword">new</span> <span class="cm-variable">ArrayList</span><span class="cm-operator">&lt;&gt;</span>()); &nbsp;<span class="cm-comment">// 每个订阅者的消息列表</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 遍历所有发布的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">pubIdx</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">pubIdx</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">msgs</span>.<span class="cm-variable">length</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">msgTime</span> <span class="cm-operator">=</span> <span class="cm-variable">msgs</span>[<span class="cm-variable">pubIdx</span>][<span class="cm-number">0</span>]; &nbsp;<span class="cm-comment">// 获取当前消息的发布时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">msgContent</span> <span class="cm-operator">=</span> <span class="cm-variable">msgs</span>[<span class="cm-variable">pubIdx</span>][<span class="cm-number">1</span>]; &nbsp;<span class="cm-comment">// 获取当前消息的内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 遍历所有订阅者（从后往前遍历，优先分配给有效的订阅者）</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">subIdx</span> <span class="cm-operator">=</span> <span class="cm-variable">consumers</span>.<span class="cm-variable">length</span> <span class="cm-operator">-</span> <span class="cm-number">1</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">subIdx</span> <span class="cm-operator">&gt;=</span> <span class="cm-number">0</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">subTime</span> <span class="cm-operator">=</span> <span class="cm-variable">consumers</span>[<span class="cm-variable">subIdx</span>][<span class="cm-number">0</span>]; &nbsp;<span class="cm-comment">// 订阅者的订阅时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">unsubTime</span> <span class="cm-operator">=</span> <span class="cm-variable">consumers</span>[<span class="cm-variable">subIdx</span>][<span class="cm-number">1</span>]; &nbsp;<span class="cm-comment">// 订阅者的退订时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果消息发布时，订阅者处于有效订阅期</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">msgTime</span> <span class="cm-operator">&gt;=</span> <span class="cm-variable">subTime</span> <span class="cm-operator">&amp;&amp;</span> <span class="cm-variable">msgTime</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">unsubTime</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">receivedMsgs</span>.<span class="cm-variable">get</span>(<span class="cm-variable">subIdx</span>).<span class="cm-variable">add</span>(<span class="cm-variable">msgContent</span>); &nbsp;<span class="cm-comment">// 该订阅者接收到消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">break</span>; &nbsp;<span class="cm-comment">// 找到一个有效的订阅者后就停止继续查找</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subIdx</span><span class="cm-operator">--</span>; &nbsp;<span class="cm-comment">// 检查下一个订阅者</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">pubIdx</span><span class="cm-operator">++</span>; &nbsp;<span class="cm-comment">// 处理下一个发布的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 输出每个订阅者接收到的消息内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">idx</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">idx</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">receivedMsgs</span>.<span class="cm-variable">size</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">ArrayList</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">Integer</span><span class="cm-operator">&gt;</span> <span class="cm-variable">contentList</span> <span class="cm-operator">=</span> <span class="cm-variable">receivedMsgs</span>.<span class="cm-variable">get</span>(<span class="cm-variable">idx</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">contentList</span>.<span class="cm-variable">isEmpty</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">System</span>.<span class="cm-variable">out</span>.<span class="cm-variable">println</span>(<span class="cm-string">"-1"</span>); &nbsp;<span class="cm-comment">// 如果没有接收到任何消息，输出 -1</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  } <span class="cm-keyword">else</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 使用 StringJoiner 格式化输出接收到的消息内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">StringJoiner</span> <span class="cm-variable">sj</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">StringJoiner</span>(<span class="cm-string">" "</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">content</span> : <span class="cm-variable">contentList</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">sj</span>.<span class="cm-variable">add</span>(<span class="cm-variable-3">Integer</span>.<span class="cm-variable">toString</span>(<span class="cm-variable">content</span>)); &nbsp;<span class="cm-comment">// 添加每条消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">System</span>.<span class="cm-variable">out</span>.<span class="cm-variable">println</span>(<span class="cm-variable">sj</span>.<span class="cm-variable">toString</span>()); &nbsp;<span class="cm-comment">// 输出消息内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">idx</span><span class="cm-operator">++</span>; &nbsp;<span class="cm-comment">// 处理下一个订阅者</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 2211px;"></div><div class="CodeMirror-gutters" style="display: none; height: 2211px;"></div></div></div></pre><h1 id='c解法'><span>C++解法</span></h1><ul><li><p><span>解题思路</span></p></li><li><p><span>解题思路</span><span>本题的核心任务是模拟消息队列和订阅者队列的操作，依据每个消息的发送时间和每个订阅者的有效订阅时间判断哪些订阅者能接收到哪些消息。我们将给定的消息和订阅者信息通过排序和检查进行处理，最终输出每个订阅者接收到的消息内容。</span></p></li></ul><p><span>解决步骤：</span><span>数据解析：</span></p><p><span>输入包含两行数据：一行表示消息发布者的消息和时间，另一行表示订阅者的订阅和退订时间。我们需要解析这两行数据，并分别存储为 msgArray 和 subArray。</span><span>消息和订阅者处理：</span></p><p><span>将 msgArray 和 subArray 分别处理成 messageQueue 和 subscriptionQueue。每个队列中的元素是一个时间和内容的对（pair&lt;int, int&gt;），分别代表消息的发送时间和内容，订阅者的订阅时间和退订时间。</span><span>排序：</span></p><p><span>按消息的发送时间对 messageQueue 进行排序，这样可以确保我们按照时间顺序处理消息。</span><span>消息分发：</span></p><p><span>遍历所有消息并检查每个消息是否在某个订阅者的有效订阅时间范围内。如果是，订阅者接收到该消息，并将该消息存储到订阅者对应的接收队列中。</span><span>输出结果：</span></p><p><span>对每个订阅者，输出其接收到的所有消息。如果没有接收到任何消息，输出 -1</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="cpp" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="cpp"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;iostream&gt;</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;vector&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;sstream&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;algorithm&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">using</span> <span class="cm-keyword">namespace</span> <span class="cm-def">std</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable-3">void</span> <span class="cm-def">processQueue</span>(<span class="cm-keyword">const</span> <span class="cm-variable">vector</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">int</span><span class="cm-operator">&gt;&amp;</span> <span class="cm-variable">msgArray</span>, <span class="cm-keyword">const</span> <span class="cm-variable">vector</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">int</span><span class="cm-operator">&gt;&amp;</span> <span class="cm-variable">subArray</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 1: 构建消息队列，将msgArray解析成消息队列</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">vector</span><span class="cm-operator">&lt;</span><span class="cm-variable">pair</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">int</span>, <span class="cm-variable-3">int</span><span class="cm-operator">&gt;&gt;</span> <span class="cm-variable">messageQueue</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">msgArray</span>.<span class="cm-variable">size</span>(); <span class="cm-variable">i</span> <span class="cm-operator">+=</span> <span class="cm-number">2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">messageQueue</span>.<span class="cm-variable">push_back</span>({ <span class="cm-variable">msgArray</span>[<span class="cm-variable">i</span>], <span class="cm-variable">msgArray</span>[<span class="cm-variable">i</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>] });</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 将每对消息时间和内容作为一对push到消息队列</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 2: 构建订阅者队列，将subArray解析成订阅者队列</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">vector</span><span class="cm-operator">&lt;</span><span class="cm-variable">pair</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">int</span>, <span class="cm-variable-3">int</span><span class="cm-operator">&gt;&gt;</span> <span class="cm-variable">subscriptionQueue</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">subArray</span>.<span class="cm-variable">size</span>(); <span class="cm-variable">i</span> <span class="cm-operator">+=</span> <span class="cm-number">2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subscriptionQueue</span>.<span class="cm-variable">push_back</span>({ <span class="cm-variable">subArray</span>[<span class="cm-variable">i</span>], <span class="cm-variable">subArray</span>[<span class="cm-variable">i</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>] });</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 将每对订阅时间和退订时间作为一对push到订阅者队列</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 3: 按消息的发送时间对消息队列进行升序排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">sort</span>(<span class="cm-variable">messageQueue</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">messageQueue</span>.<span class="cm-variable">end</span>(), [](<span class="cm-keyword">const</span> <span class="cm-variable">pair</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">int</span>, <span class="cm-variable-3">int</span><span class="cm-operator">&gt;&amp;</span> <span class="cm-variable">a</span>, <span class="cm-keyword">const</span> <span class="cm-variable">pair</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">int</span>, <span class="cm-variable-3">int</span><span class="cm-operator">&gt;&amp;</span> <span class="cm-variable">b</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">a</span>.<span class="cm-variable">first</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">b</span>.<span class="cm-variable">first</span>; &nbsp;<span class="cm-comment">// 按时间升序排列</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  });</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 4: 为每个订阅者准备一个空的消息接收列表</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">vector</span><span class="cm-operator">&lt;</span><span class="cm-variable">vector</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">int</span><span class="cm-operator">&gt;&gt;</span> <span class="cm-variable">receivedMessages</span>(<span class="cm-variable">subscriptionQueue</span>.<span class="cm-variable">size</span>());</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 5: 遍历消息队列，将消息分配给有效的订阅者</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">const</span> <span class="cm-keyword">auto</span><span class="cm-operator">&amp;</span> [<span class="cm-variable">sendTime</span>, <span class="cm-variable">message</span>] : <span class="cm-variable">messageQueue</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 遍历订阅者队列</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-variable">subscriptionQueue</span>.<span class="cm-variable">size</span>() <span class="cm-operator">-</span> <span class="cm-number">1</span>; <span class="cm-variable">i</span> <span class="cm-operator">&gt;=</span> <span class="cm-number">0</span>; <span class="cm-operator">--</span><span class="cm-variable">i</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-keyword">auto</span><span class="cm-operator">&amp;</span> [<span class="cm-variable">subscribeTime</span>, <span class="cm-variable">unsubscribeTime</span>] <span class="cm-operator">=</span> <span class="cm-variable">subscriptionQueue</span>[<span class="cm-variable">i</span>];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果消息发送时间在订阅者的有效订阅期内</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">sendTime</span> <span class="cm-operator">&gt;=</span> <span class="cm-variable">subscribeTime</span> <span class="cm-operator">&amp;&amp;</span> <span class="cm-variable">sendTime</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">unsubscribeTime</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">receivedMessages</span>[<span class="cm-variable">i</span>].<span class="cm-variable">push_back</span>(<span class="cm-variable">message</span>); &nbsp;<span class="cm-comment">// 该订阅者接收到消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">break</span>; &nbsp;<span class="cm-comment">// 一旦找到了合适的订阅者，就停止查找</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 6: 输出每个订阅者接收到的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">const</span> <span class="cm-keyword">auto</span><span class="cm-operator">&amp;</span> <span class="cm-variable">msgs</span> : <span class="cm-variable">receivedMessages</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">msgs</span>.<span class="cm-variable">empty</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">cout</span> <span class="cm-operator">&lt;&lt;</span> <span class="cm-string">"-1"</span> <span class="cm-operator">&lt;&lt;</span> <span class="cm-variable">endl</span>; &nbsp;<span class="cm-comment">// 如果该订阅者没有接收到消息，输出-1</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">else</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">size_t</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">msgs</span>.<span class="cm-variable">size</span>(); <span class="cm-operator">++</span><span class="cm-variable">i</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">i</span> <span class="cm-operator">&gt;</span> <span class="cm-number">0</span>) <span class="cm-variable">cout</span> <span class="cm-operator">&lt;&lt;</span> <span class="cm-string">" "</span>; &nbsp;<span class="cm-comment">// 处理输出格式，消息之间有空格</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">cout</span> <span class="cm-operator">&lt;&lt;</span> <span class="cm-variable">msgs</span>[<span class="cm-variable">i</span>]; &nbsp;<span class="cm-comment">// 输出消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">cout</span> <span class="cm-operator">&lt;&lt;</span> <span class="cm-variable">endl</span>; &nbsp;<span class="cm-comment">// 输出完一位订阅者的消息后换行</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable-3">int</span> <span class="cm-def">main</span>() {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">string</span> <span class="cm-variable">line</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">vector</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">int</span><span class="cm-operator">&gt;</span> <span class="cm-variable">msgArray</span>, <span class="cm-variable">subArray</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 7: 读取并解析消息发布者的数据</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">getline</span>(<span class="cm-variable">cin</span>, <span class="cm-variable">line</span>); &nbsp;<span class="cm-comment">// 读取第一行数据</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">stringstream</span> <span class="cm-variable">ss1</span>(<span class="cm-variable">line</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">num</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">ss1</span> <span class="cm-operator">&gt;&gt;</span> <span class="cm-variable">num</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">msgArray</span>.<span class="cm-variable">push_back</span>(<span class="cm-variable">num</span>); &nbsp;<span class="cm-comment">// 将消息数据存入msgArray</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 8: 读取并解析订阅者的数据</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">getline</span>(<span class="cm-variable">cin</span>, <span class="cm-variable">line</span>); &nbsp;<span class="cm-comment">// 读取第二行数据</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">stringstream</span> <span class="cm-variable">ss2</span>(<span class="cm-variable">line</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">ss2</span> <span class="cm-operator">&gt;&gt;</span> <span class="cm-variable">num</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subArray</span>.<span class="cm-variable">push_back</span>(<span class="cm-variable">num</span>); &nbsp;<span class="cm-comment">// 将订阅者数据存入subArray</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// Step 9: 处理消息队列和订阅队列</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">processQueue</span>(<span class="cm-variable">msgArray</span>, <span class="cm-variable">subArray</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 1981px;"></div><div class="CodeMirror-gutters" style="display: none; height: 1981px;"></div></div></div></pre><h1 id='c解法-2'><span>C解法</span></h1><ul><li><h3 id='解题思路'><span>解题思路</span></h3></li><li><p><span>解题思路</span><span>本题目要求模拟消息发布与订阅的过程，关键在于处理消息发布的时间与订阅者的订阅时间对比，从而确定订阅者是否接收某些消息。我们通过以下步骤解决这个问题：</span></p></li></ul><p><span>数据输入与解析：</span></p><p><span>输入的消息与订阅数据分别是两个数组。每个消息由一个时间戳和一个内容组成，订阅者由一个订阅开始时间和一个订阅结束时间组成。</span><span>我们首先读取输入数据，将其解析成适当的数据结构。</span><span>排序：</span></p><p><span>将消息按照时间戳升序排序。这样可以确保我们按时间的顺序处理消息，确保消息发送的先后顺序正确。</span><span>消息分发：</span></p><p><span>遍历所有的消息，检查它是否在某个订阅者的有效订阅时间内。如果是，订阅者接收到该消息，并将其添加到订阅者的消息列表中。</span><span>输出结果：</span></p><p><span>对每个订阅者，输出其接收到的所有消息。如果没有接收到任何消息，则输出 -1</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="c" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="c"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;stdio.h&gt;</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;stdlib.h&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 定义消息结构体</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">typedef</span> <span class="cm-keyword">struct</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">time</span>; &nbsp; &nbsp; &nbsp; <span class="cm-comment">// 消息的发送时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">content</span>; &nbsp; &nbsp;<span class="cm-comment">// 消息的内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">} <span class="cm-variable">Pub</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 定义订阅者结构体</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">typedef</span> <span class="cm-keyword">struct</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">subTime</span>; &nbsp; &nbsp;<span class="cm-comment">// 订阅开始时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">unSubTime</span>; &nbsp;<span class="cm-comment">// 退订时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable-3">*</span><span class="cm-variable">messages</span>; &nbsp;<span class="cm-comment">// 存储订阅者接收到的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">count</span>; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 存储消息的数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">} <span class="cm-variable">Sub</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 排序函数：按照消息的发送时间进行升序排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable-3">int</span> <span class="cm-def">comparePub</span>(<span class="cm-keyword">const</span> <span class="cm-variable-3">void*</span> <span class="cm-variable">a</span>, <span class="cm-keyword">const</span> <span class="cm-variable-3">void*</span> <span class="cm-variable">b</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> ((<span class="cm-variable">Pub</span> <span class="cm-operator">*</span>)<span class="cm-variable">a</span>)<span class="cm-operator">-&gt;</span><span class="cm-variable">time</span> <span class="cm-operator">-</span> ((<span class="cm-variable">Pub</span> <span class="cm-operator">*</span>)<span class="cm-variable">b</span>)<span class="cm-operator">-&gt;</span><span class="cm-variable">time</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable-3">int</span> <span class="cm-def">main</span>() {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 定义存储消息和订阅者信息的数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">pubArr</span>[<span class="cm-number">200</span>], <span class="cm-variable">subArr</span>[<span class="cm-number">20</span>];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">pubCount</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>, <span class="cm-variable">subCount</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 读取消息发布信息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">scanf</span>(<span class="cm-string">"%d"</span>, <span class="cm-operator">&amp;</span><span class="cm-variable">pubArr</span>[<span class="cm-variable">pubCount</span>]) <span class="cm-operator">!=</span> <span class="cm-variable">EOF</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">pubCount</span><span class="cm-operator">++</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">getchar</span>() <span class="cm-operator">==</span> <span class="cm-string">'\n'</span>) <span class="cm-keyword">break</span>; &nbsp;<span class="cm-comment">// 一行输入结束后跳出</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 读取订阅者信息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">scanf</span>(<span class="cm-string">"%d"</span>, <span class="cm-operator">&amp;</span><span class="cm-variable">subArr</span>[<span class="cm-variable">subCount</span>]) <span class="cm-operator">!=</span> <span class="cm-variable">EOF</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subCount</span><span class="cm-operator">++</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">getchar</span>() <span class="cm-operator">==</span> <span class="cm-string">'\n'</span>) <span class="cm-keyword">break</span>; &nbsp;<span class="cm-comment">// 一行输入结束后跳出</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 计算消息和订阅者的数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">n</span> <span class="cm-operator">=</span> <span class="cm-variable">pubCount</span> <span class="cm-operator">/</span> <span class="cm-number">2</span>; &nbsp;<span class="cm-comment">// 消息的数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">m</span> <span class="cm-operator">=</span> <span class="cm-variable">subCount</span> <span class="cm-operator">/</span> <span class="cm-number">2</span>; &nbsp;<span class="cm-comment">// 订阅者的数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">Pub</span> <span class="cm-variable">pubs</span>[<span class="cm-variable">n</span>]; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 消息数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">Sub</span> <span class="cm-variable">subs</span>[<span class="cm-variable">m</span>]; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 订阅者数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 填充消息数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>, <span class="cm-variable">j</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">n</span>; <span class="cm-variable">i</span><span class="cm-operator">++</span>, <span class="cm-variable">j</span> <span class="cm-operator">+=</span> <span class="cm-number">2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">pubs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">time</span> <span class="cm-operator">=</span> <span class="cm-variable">pubArr</span>[<span class="cm-variable">j</span>]; &nbsp; &nbsp; &nbsp; <span class="cm-comment">// 消息时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">pubs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">content</span> <span class="cm-operator">=</span> <span class="cm-variable">pubArr</span>[<span class="cm-variable">j</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>]; <span class="cm-comment">// 消息内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 填充订阅者数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>, <span class="cm-variable">j</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">m</span>; <span class="cm-variable">i</span><span class="cm-operator">++</span>, <span class="cm-variable">j</span> <span class="cm-operator">+=</span> <span class="cm-number">2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">subTime</span> <span class="cm-operator">=</span> <span class="cm-variable">subArr</span>[<span class="cm-variable">j</span>]; &nbsp; &nbsp;<span class="cm-comment">// 订阅开始时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">unSubTime</span> <span class="cm-operator">=</span> <span class="cm-variable">subArr</span>[<span class="cm-variable">j</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>]; <span class="cm-comment">// 退订时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">messages</span> <span class="cm-operator">=</span> (<span class="cm-variable-3">int</span> <span class="cm-variable-3">*</span>)<span class="cm-variable">malloc</span>(<span class="cm-variable">n</span> <span class="cm-operator">*</span> <span class="cm-keyword">sizeof</span>(<span class="cm-variable-3">int</span>)); <span class="cm-comment">// 为每个订阅者分配消息存储空间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">count</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; &nbsp;<span class="cm-comment">// 初始化接收到的消息数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 对消息按照时间进行排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">qsort</span>(<span class="cm-variable">pubs</span>, <span class="cm-variable">n</span>, <span class="cm-keyword">sizeof</span>(<span class="cm-variable">Pub</span>), <span class="cm-variable">comparePub</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 分配消息给订阅者</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">n</span>; <span class="cm-variable">i</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">time</span> <span class="cm-operator">=</span> <span class="cm-variable">pubs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">time</span>; &nbsp; <span class="cm-comment">// 当前消息的时间</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">content</span> <span class="cm-operator">=</span> <span class="cm-variable">pubs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">content</span>; &nbsp;<span class="cm-comment">// 当前消息的内容</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 遍历所有订阅者</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">j</span> <span class="cm-operator">=</span> <span class="cm-variable">m</span> <span class="cm-operator">-</span> <span class="cm-number">1</span>; <span class="cm-variable">j</span> <span class="cm-operator">&gt;=</span> <span class="cm-number">0</span>; <span class="cm-variable">j</span><span class="cm-operator">--</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 检查消息是否在订阅者的订阅期内</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">time</span> <span class="cm-operator">&gt;=</span> <span class="cm-variable">subs</span>[<span class="cm-variable">j</span>].<span class="cm-variable">subTime</span> <span class="cm-operator">&amp;&amp;</span> <span class="cm-variable">time</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">subs</span>[<span class="cm-variable">j</span>].<span class="cm-variable">unSubTime</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">subs</span>[<span class="cm-variable">j</span>].<span class="cm-variable">messages</span>[<span class="cm-variable">subs</span>[<span class="cm-variable">j</span>].<span class="cm-variable">count</span><span class="cm-operator">++</span>] <span class="cm-operator">=</span> <span class="cm-variable">content</span>; &nbsp;<span class="cm-comment">// 该订阅者接收到消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">break</span>; &nbsp;<span class="cm-comment">// 一个消息只能被一个订阅者接收，找到合适的订阅者后跳出循环</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 输出每个订阅者接收到的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">m</span>; <span class="cm-variable">i</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">subs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">count</span> <span class="cm-operator">==</span> <span class="cm-number">0</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果订阅者没有接收到任何消息，输出 -1</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">printf</span>(<span class="cm-string">"-1\n"</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  } <span class="cm-keyword">else</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果有消息，输出接收到的所有消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">int</span> <span class="cm-variable">j</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable">j</span> <span class="cm-operator">&lt;</span> <span class="cm-variable">subs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">count</span>; <span class="cm-variable">j</span><span class="cm-operator">++</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">j</span> <span class="cm-operator">&gt;</span> <span class="cm-number">0</span>) <span class="cm-variable">printf</span>(<span class="cm-string">" "</span>); &nbsp;<span class="cm-comment">// 处理格式，消息之间用空格隔开</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">printf</span>(<span class="cm-string">"%d"</span>, <span class="cm-variable">subs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">messages</span>[<span class="cm-variable">j</span>]); &nbsp;<span class="cm-comment">// 输出消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">printf</span>(<span class="cm-string">"\n"</span>); &nbsp;<span class="cm-comment">// 每个订阅者的输出结束后换行</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">free</span>(<span class="cm-variable">subs</span>[<span class="cm-variable">i</span>].<span class="cm-variable">messages</span>); &nbsp;<span class="cm-comment">// 释放订阅者消息数组的内存</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 2257px;"></div><div class="CodeMirror-gutters" style="display: none; height: 2257px;"></div></div></div></pre><p>&nbsp;</p><h1 id='js解法'><span>JS解法</span></h1><ul><li><h3 id='解题思路-2'><span>解题思路</span></h3></li><li><p><span>解题思路</span><span>本题目要求模拟消息发布与订阅的过程，其中涉及到两类数据：消息（包括发送时间和内容）与订阅者（包括订阅时间段）。每个订阅者能够接收在其订阅时间范围内发布的消息。通过以下步骤，我们可以解决这个问题：</span></p></li></ul><p><span>数据输入：</span></p><p><span>第一行输入消息数据，每对数据由发送时间和消息内容组成。</span><span>第二行输入订阅者数据，每对数据由订阅开始时间和退订时间组成。</span><span>排序：</span></p><p><span>将消息按照时间顺序排序，确保消息按发送时间处理。</span><span>消息分发：</span></p><p><span>对每个消息，检查它是否在某个订阅者的订阅时间范围内。如果在范围内，订阅者接收到该消息。</span><span>输出结果：</span></p><p><span>对每个订阅者，输出其接收到的所有消息。如果没有接收到任何消息，则输出 -1</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">readline</span> <span class="cm-operator">=</span> <span class="cm-variable">require</span>(<span class="cm-string">"readline"</span>);</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 创建接口以读取输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">rl</span> <span class="cm-operator">=</span> <span class="cm-variable">readline</span>.<span class="cm-property">createInterface</span>({</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-property">input</span>: <span class="cm-variable">process</span>.<span class="cm-property">stdin</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-property">output</span>: <span class="cm-variable">process</span>.<span class="cm-property">stdout</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">});</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 用于存储所有输入数据</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">inputs</span> <span class="cm-operator">=</span> [];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 监听每行输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">rl</span>.<span class="cm-property">on</span>(<span class="cm-string">"line"</span>, (<span class="cm-def">line</span>) <span class="cm-operator">=&gt;</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-variable">inputs</span>.<span class="cm-property">push</span>(<span class="cm-variable-2">line</span>); &nbsp;<span class="cm-comment">// 将每行输入数据存储到数组中</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-comment">// 当接收到两行输入数据后，处理消息和订阅数据</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">inputs</span>.<span class="cm-property">length</span> <span class="cm-operator">===</span> <span class="cm-number">2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">msgArray</span> <span class="cm-operator">=</span> <span class="cm-variable">inputs</span>[<span class="cm-number">0</span>].<span class="cm-property">split</span>(<span class="cm-string">" "</span>).<span class="cm-property">map</span>(<span class="cm-variable">Number</span>); &nbsp;<span class="cm-comment">// 消息数组（时间和内容）</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">subArray</span> <span class="cm-operator">=</span> <span class="cm-variable">inputs</span>[<span class="cm-number">1</span>].<span class="cm-property">split</span>(<span class="cm-string">" "</span>).<span class="cm-property">map</span>(<span class="cm-variable">Number</span>); &nbsp;<span class="cm-comment">// 订阅者数组（订阅和退订时间）</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">processQueue</span>(<span class="cm-variable-2">msgArray</span>, <span class="cm-variable-2">subArray</span>); &nbsp;<span class="cm-comment">// 处理消息分发</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">inputs</span>.<span class="cm-property">length</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; &nbsp;<span class="cm-comment">// 重置输入数组，等待下一次输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">});</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 处理消息分发的函数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">function</span> <span class="cm-def">processQueue</span>(<span class="cm-def">msgArray</span>, <span class="cm-def">subArray</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-comment">// 创建存储消息队列的数组，每条消息是一个包含时间和内容的数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">messageQueue</span> <span class="cm-operator">=</span> [];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">let</span> <span class="cm-def">i</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable-2">i</span> <span class="cm-operator">&lt;</span> <span class="cm-variable-2">msgArray</span>.<span class="cm-property">length</span>; <span class="cm-variable-2">i</span> <span class="cm-operator">+=</span> <span class="cm-number">2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-2">messageQueue</span>.<span class="cm-property">push</span>([<span class="cm-variable-2">msgArray</span>[<span class="cm-variable-2">i</span>], <span class="cm-variable-2">msgArray</span>[<span class="cm-variable-2">i</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>]]);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-comment">// 创建存储订阅者队列的数组，每个订阅者是一个包含订阅时间和退订时间的数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">subscriptionQueue</span> <span class="cm-operator">=</span> [];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">let</span> <span class="cm-def">j</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>; <span class="cm-variable-2">j</span> <span class="cm-operator">&lt;</span> <span class="cm-variable-2">subArray</span>.<span class="cm-property">length</span>; <span class="cm-variable-2">j</span> <span class="cm-operator">+=</span> <span class="cm-number">2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-2">subscriptionQueue</span>.<span class="cm-property">push</span>([<span class="cm-variable-2">subArray</span>[<span class="cm-variable-2">j</span>], <span class="cm-variable-2">subArray</span>[<span class="cm-variable-2">j</span> <span class="cm-operator">+</span> <span class="cm-number">1</span>]]);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-comment">// 按照消息的发送时间对消息队列进行排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-variable-2">messageQueue</span>.<span class="cm-property">sort</span>((<span class="cm-def">a</span>, <span class="cm-def">b</span>) <span class="cm-operator">=&gt;</span> <span class="cm-variable-2">a</span>[<span class="cm-number">0</span>] <span class="cm-operator">-</span> <span class="cm-variable-2">b</span>[<span class="cm-number">0</span>]);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-comment">// 为每个订阅者创建一个空数组，用于存储该订阅者接收到的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">receivedMessages</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">Array</span>(<span class="cm-variable-2">subscriptionQueue</span>.<span class="cm-property">length</span>).<span class="cm-property">fill</span>(<span class="cm-number">0</span>).<span class="cm-property">map</span>(() <span class="cm-operator">=&gt;</span> []);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-comment">// 遍历消息队列，检查每条消息是否符合订阅者的接收条件</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">let</span> [<span class="cm-def">sendTime</span>, <span class="cm-def">message</span>] <span class="cm-keyword">of</span> <span class="cm-variable-2">messageQueue</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 从后往前遍历订阅者队列，确保优先分配给后面的订阅者</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">let</span> <span class="cm-def">i</span> <span class="cm-operator">=</span> <span class="cm-variable-2">subscriptionQueue</span>.<span class="cm-property">length</span> <span class="cm-operator">-</span> <span class="cm-number">1</span>; <span class="cm-variable-2">i</span> <span class="cm-operator">&gt;=</span> <span class="cm-number">0</span>; <span class="cm-variable-2">i</span><span class="cm-operator">--</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;<span class="cm-keyword">let</span> [<span class="cm-def">subscribeTime</span>, <span class="cm-def">unsubscribeTime</span>] <span class="cm-operator">=</span> <span class="cm-variable-2">subscriptionQueue</span>[<span class="cm-variable-2">i</span>];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果消息的发送时间在订阅者的订阅时间段内，则该订阅者接收到此消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable-2">sendTime</span> <span class="cm-operator">&gt;=</span> <span class="cm-variable-2">subscribeTime</span> <span class="cm-operator">&amp;&amp;</span> <span class="cm-variable-2">sendTime</span> <span class="cm-operator">&lt;</span> <span class="cm-variable-2">unsubscribeTime</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">receivedMessages</span>[<span class="cm-variable-2">i</span>].<span class="cm-property">push</span>(<span class="cm-variable-2">message</span>); &nbsp;<span class="cm-comment">// 记录接收到的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">break</span>; &nbsp;<span class="cm-comment">// 一个消息只会分配给一个订阅者，处理完后跳出循环</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-comment">// 输出每个订阅者接收到的消息</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;<span class="cm-variable-2">receivedMessages</span>.<span class="cm-property">forEach</span>((<span class="cm-def">msgs</span>) <span class="cm-operator">=&gt;</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable-2">msgs</span>.<span class="cm-property">length</span> <span class="cm-operator">===</span> <span class="cm-number">0</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;<span class="cm-variable">console</span>.<span class="cm-property">log</span>(<span class="cm-string">"-1"</span>); &nbsp;<span class="cm-comment">// 如果订阅者没有接收到任何消息，输出 -1</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  } <span class="cm-keyword">else</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;<span class="cm-variable">console</span>.<span class="cm-property">log</span>(<span class="cm-variable-2">msgs</span>.<span class="cm-property">join</span>(<span class="cm-string">" "</span>)); &nbsp;<span class="cm-comment">// 输出接收到的所有消息，以空格分隔</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">  });</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 1635px;"></div><div class="CodeMirror-gutters" style="display: none; height: 1635px;"></div></div></div></pre><h1 id='注意'><span>注意：</span></h1><p><strong><span>如果发现代码有用例覆盖不到的情况，欢迎反馈！会在第一时间修正，更新。</span></strong><strong><span>解题不易，如对您有帮助，欢迎点赞/收藏</span></strong></p></div></div><script>(function(){function e(e,n,i){document.addEventListener(e,function(e){if(!e.defaultPrevented)for(var t=e.target;t&&t!=this;t=t.parentNode)if(t.matches(n)){!1===i.call(t,e)&&(e.preventDefault(),e.stopPropagation());break}},!1)}var t=document.body.parentElement,i=[],r=null,o=document.body.classList.contains("typora-export-collapse-outline");function a(){return t.scrollTop}e("click",".outline-expander",function(e){var t=this.closest(".outline-item-wrapper").classList;return t.contains("outline-item-open")?t.remove("outline-item-open"):t.add("outline-item-open"),u(),!1}),e("click",".outline-item",function(e){var t=this.querySelector(".outline-label");location.hash="#"+t.getAttribute("href"),o&&((t=this.closest(".outline-item-wrapper").classList).contains("outline-item-open")||t.add("outline-item-open"),d(),t.add("outline-item-active"))});function s(){var e=a();r=null;for(var t=0;t<i.length&&i[t][1]-e<60;t++)r=i[t]}function n(){c=setTimeout(function(){var n;i=[],n=a(),document.querySelector("#write").querySelectorAll("h1, h2, h3, h4, h5, h6").forEach(e=>{var t=e.getAttribute("id");i.push([t,n+e.getBoundingClientRect().y])}),s(),u()},300)}var l,c,d=function(){document.querySelectorAll(".outline-item-active").forEach(e=>e.classList.remove("outline-item-active")),document.querySelectorAll(".outline-item-single.outline-item-open").forEach(e=>e.classList.remove("outline-item-open"))},u=function(){if(r&&(d(),t=document.querySelector('.outline-label[href="#'+(CSS.escape?CSS.escape(r[0]):r[0])+'"]')))if(o){var e=t.closest(".outline-item-open>ul>.outline-item-wrapper");if(e)e.classList.add("outline-item-active");else{for(var t,n=(t=t.closest(".outline-item-wrapper")).parentElement.closest(".outline-item-wrapper");n;)n=(t=n).parentElement.closest(".outline-item-wrapper");t.classList.add("outline-item-active")}}else t.closest(".outline-item-wrapper").classList.add("outline-item-active")};window.addEventListener("scroll",function(e){l&&clearTimeout(l),l=setTimeout(function(){s(),u()},300)});window.addEventListener("resize",function(e){c&&clearTimeout(c),n()}),n()})();</script></body></html>