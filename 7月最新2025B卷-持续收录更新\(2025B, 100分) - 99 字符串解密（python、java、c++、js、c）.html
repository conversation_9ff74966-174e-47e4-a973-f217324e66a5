<!doctype html><html><head><meta charset='UTF-8'><meta name='viewport' content='width=device-width initial-scale=1'><link href='https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext' rel='stylesheet' type='text/css' /><style type='text/css'>html {overflow-x: initial !important;}:root { --bg-color: #ffffff; --text-color: #333333; --select-text-bg-color: #B5D6FC; --select-text-font-color: auto; --monospace: "Lucida Console",Consolas,"Courier",monospace; --title-bar-height: 20px; }.mac-os-11 { --title-bar-height: 28px; }html { font-size: 14px; background-color: var(--bg-color); color: var(--text-color); font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; -webkit-font-smoothing: antialiased; }h1, h2, h3, h4, h5 { white-space: pre-wrap; }body { margin: 0px; padding: 0px; height: auto; inset: 0px; font-size: 1rem; line-height: 1.42857; overflow-x: hidden; background: inherit; }iframe { margin: auto; }a.url { word-break: break-all; }a:active, a:hover { outline: 0px; }.in-text-selection, ::selection { text-shadow: none; background: var(--select-text-bg-color); color: var(--select-text-font-color); }#write { margin: 0px auto; height: auto; width: inherit; word-break: normal; overflow-wrap: break-word; position: relative; white-space: normal; overflow-x: visible; padding-top: 36px; }#write.first-line-indent p { text-indent: 2em; }#write.first-line-indent li p, #write.first-line-indent p * { text-indent: 0px; }#write.first-line-indent li { margin-left: 2em; }.for-image #write { padding-left: 8px; padding-right: 8px; }body.typora-export { padding-left: 30px; padding-right: 30px; }.typora-export .footnote-line, .typora-export li, .typora-export p { white-space: pre-wrap; }.typora-export .task-list-item input { pointer-events: none; }@media screen and (max-width: 500px) {  body.typora-export { padding-left: 0px; padding-right: 0px; }  #write { padding-left: 20px; padding-right: 20px; }}#write li > figure:last-child { margin-bottom: 0.5rem; }#write ol, #write ul { position: relative; }img { max-width: 100%; vertical-align: middle; image-orientation: from-image; }button, input, select, textarea { color: inherit; font: inherit; }input[type="checkbox"], input[type="radio"] { line-height: normal; padding: 0px; }*, ::after, ::before { box-sizing: border-box; }#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p, #write pre { width: inherit; }#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p { position: relative; }p { line-height: inherit; }h1, h2, h3, h4, h5, h6 { break-after: avoid-page; break-inside: avoid; orphans: 4; }p { orphans: 4; }h1 { font-size: 2rem; }h2 { font-size: 1.8rem; }h3 { font-size: 1.6rem; }h4 { font-size: 1.4rem; }h5 { font-size: 1.2rem; }h6 { font-size: 1rem; }.md-math-block, .md-rawblock, h1, h2, h3, h4, h5, h6, p { margin-top: 1rem; margin-bottom: 1rem; }.hidden { display: none; }.md-blockmeta { color: rgb(204, 204, 204); font-weight: 700; font-style: italic; }a { cursor: pointer; }sup.md-footnote { padding: 2px 4px; background-color: rgba(238, 238, 238, 0.7); color: rgb(85, 85, 85); border-radius: 4px; cursor: pointer; }sup.md-footnote a, sup.md-footnote a:hover { color: inherit; text-transform: inherit; text-decoration: inherit; }#write input[type="checkbox"] { cursor: pointer; width: inherit; height: inherit; }figure { overflow-x: auto; margin: 1.2em 0px; max-width: calc(100% + 16px); padding: 0px; }figure > table { margin: 0px; }thead, tr { break-inside: avoid; break-after: auto; }thead { display: table-header-group; }table { border-collapse: collapse; border-spacing: 0px; width: 100%; overflow: auto; break-inside: auto; text-align: left; }table.md-table td { min-width: 32px; }.CodeMirror-gutters { border-right: 0px; background-color: inherit; }.CodeMirror-linenumber { user-select: none; }.CodeMirror { text-align: left; }.CodeMirror-placeholder { opacity: 0.3; }.CodeMirror pre { padding: 0px 4px; }.CodeMirror-lines { padding: 0px; }div.hr:focus { cursor: none; }#write pre { white-space: pre-wrap; }#write.fences-no-line-wrapping pre { white-space: pre; }#write pre.ty-contain-cm { white-space: normal; }.CodeMirror-gutters { margin-right: 4px; }.md-fences { font-size: 0.9rem; display: block; break-inside: avoid; text-align: left; overflow: visible; white-space: pre; background: inherit; position: relative !important; }.md-fences-adv-panel { width: 100%; margin-top: 10px; text-align: center; padding-top: 0px; padding-bottom: 8px; overflow-x: auto; }#write .md-fences.mock-cm { white-space: pre-wrap; }.md-fences.md-fences-with-lineno { padding-left: 0px; }#write.fences-no-line-wrapping .md-fences.mock-cm { white-space: pre; overflow-x: auto; }.md-fences.mock-cm.md-fences-with-lineno { padding-left: 8px; }.CodeMirror-line, twitterwidget { break-inside: avoid; }svg { break-inside: avoid; }.footnotes { opacity: 0.8; font-size: 0.9rem; margin-top: 1em; margin-bottom: 1em; }.footnotes + .footnotes { margin-top: 0px; }.md-reset { margin: 0px; padding: 0px; border: 0px; outline: 0px; vertical-align: top; background: 0px 0px; text-decoration: none; text-shadow: none; float: none; position: static; width: auto; height: auto; white-space: nowrap; cursor: inherit; -webkit-tap-highlight-color: transparent; line-height: normal; font-weight: 400; text-align: left; box-sizing: content-box; direction: ltr; }li div { padding-top: 0px; }blockquote { margin: 1rem 0px; }li .mathjax-block, li p { margin: 0.5rem 0px; }li blockquote { margin: 1rem 0px; }li { margin: 0px; position: relative; }blockquote > :last-child { margin-bottom: 0px; }blockquote > :first-child, li > :first-child { margin-top: 0px; }.footnotes-area { color: rgb(136, 136, 136); margin-top: 0.714rem; padding-bottom: 0.143rem; white-space: normal; }#write .footnote-line { white-space: pre-wrap; }@media print {  body, html { border: 1px solid transparent; height: 99%; break-after: avoid; break-before: avoid; font-variant-ligatures: no-common-ligatures; }  #write { margin-top: 0px; border-color: transparent !important; padding-top: 0px !important; padding-bottom: 0px !important; }  .typora-export * { -webkit-print-color-adjust: exact; }  .typora-export #write { break-after: avoid; }  .typora-export #write::after { height: 0px; }  .is-mac table { break-inside: avoid; }  #write > p:nth-child(1) { margin-top: 0px; }  .typora-export-show-outline .typora-export-sidebar { display: none; }  figure { overflow-x: visible; }}.footnote-line { margin-top: 0.714em; font-size: 0.7em; }a img, img a { cursor: pointer; }pre.md-meta-block { font-size: 0.8rem; min-height: 0.8rem; white-space: pre-wrap; background: rgb(204, 204, 204); display: block; overflow-x: hidden; }p > .md-image:only-child:not(.md-img-error) img, p > img:only-child { display: block; margin: auto; }#write.first-line-indent p > .md-image:only-child:not(.md-img-error) img { left: -2em; position: relative; }p > .md-image:only-child { display: inline-block; width: 100%; }#write .MathJax_Display { margin: 0.8em 0px 0px; }.md-math-block { width: 100%; }.md-math-block:not(:empty)::after { display: none; }.MathJax_ref { fill: currentcolor; }[contenteditable="true"]:active, [contenteditable="true"]:focus, [contenteditable="false"]:active, [contenteditable="false"]:focus { outline: 0px; box-shadow: none; }.md-task-list-item { position: relative; list-style-type: none; }.task-list-item.md-task-list-item { padding-left: 0px; }.md-task-list-item > input { position: absolute; top: 0px; left: 0px; margin-left: -1.2em; margin-top: calc(1em - 10px); border: none; }.math { font-size: 1rem; }.md-toc { min-height: 3.58rem; position: relative; font-size: 0.9rem; border-radius: 10px; }.md-toc-content { position: relative; margin-left: 0px; }.md-toc-content::after, .md-toc::after { display: none; }.md-toc-item { display: block; color: rgb(65, 131, 196); }.md-toc-item a { text-decoration: none; }.md-toc-inner:hover { text-decoration: underline; }.md-toc-inner { display: inline-block; cursor: pointer; }.md-toc-h1 .md-toc-inner { margin-left: 0px; font-weight: 700; }.md-toc-h2 .md-toc-inner { margin-left: 2em; }.md-toc-h3 .md-toc-inner { margin-left: 4em; }.md-toc-h4 .md-toc-inner { margin-left: 6em; }.md-toc-h5 .md-toc-inner { margin-left: 8em; }.md-toc-h6 .md-toc-inner { margin-left: 10em; }@media screen and (max-width: 48em) {  .md-toc-h3 .md-toc-inner { margin-left: 3.5em; }  .md-toc-h4 .md-toc-inner { margin-left: 5em; }  .md-toc-h5 .md-toc-inner { margin-left: 6.5em; }  .md-toc-h6 .md-toc-inner { margin-left: 8em; }}a.md-toc-inner { font-size: inherit; font-style: inherit; font-weight: inherit; line-height: inherit; }.footnote-line a:not(.reversefootnote) { color: inherit; }.reversefootnote { font-family: ui-monospace, sans-serif; }.md-attr { display: none; }.md-fn-count::after { content: "."; }code, pre, samp, tt { font-family: var(--monospace); }kbd { margin: 0px 0.1em; padding: 0.1em 0.6em; font-size: 0.8em; color: rgb(36, 39, 41); background: rgb(255, 255, 255); border: 1px solid rgb(173, 179, 185); border-radius: 3px; box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px, rgb(255, 255, 255) 0px 0px 0px 2px inset; white-space: nowrap; vertical-align: middle; }.md-comment { color: rgb(162, 127, 3); opacity: 0.6; font-family: var(--monospace); }code { text-align: left; vertical-align: initial; }a.md-print-anchor { white-space: pre !important; border-width: initial !important; border-style: none !important; border-color: initial !important; display: inline-block !important; position: absolute !important; width: 1px !important; right: 0px !important; outline: 0px !important; background: 0px 0px !important; text-decoration: initial !important; text-shadow: initial !important; }.os-windows.monocolor-emoji .md-emoji { font-family: "Segoe UI Symbol", sans-serif; }.md-diagram-panel > svg { max-width: 100%; }[lang="flow"] svg, [lang="mermaid"] svg { max-width: 100%; height: auto; }[lang="mermaid"] .node text { font-size: 1rem; }table tr th { border-bottom: 0px; }video { max-width: 100%; display: block; margin: 0px auto; }iframe { max-width: 100%; width: 100%; border: none; }.highlight td, .highlight tr { border: 0px; }mark { background: rgb(255, 255, 0); color: rgb(0, 0, 0); }.md-html-inline .md-plain, .md-html-inline strong, mark .md-inline-math, mark strong { color: inherit; }.md-expand mark .md-meta { opacity: 0.3 !important; }mark .md-meta { color: rgb(0, 0, 0); }@media print {  .typora-export h1, .typora-export h2, .typora-export h3, .typora-export h4, .typora-export h5, .typora-export h6 { break-inside: avoid; }}.md-diagram-panel .messageText { stroke: none !important; }.md-diagram-panel .start-state { fill: var(--node-fill); }.md-diagram-panel .edgeLabel rect { opacity: 1 !important; }.md-fences.md-fences-math { font-size: 1em; }.md-fences-advanced:not(.md-focus) { padding: 0px; white-space: nowrap; border: 0px; }.md-fences-advanced:not(.md-focus) { background: inherit; }.typora-export-show-outline .typora-export-content { max-width: 1440px; margin: auto; display: flex; flex-direction: row; }.typora-export-sidebar { width: 300px; font-size: 0.8rem; margin-top: 80px; margin-right: 18px; }.typora-export-show-outline #write { --webkit-flex: 2; flex: 2 1 0%; }.typora-export-sidebar .outline-content { position: fixed; top: 0px; max-height: 100%; overflow: hidden auto; padding-bottom: 30px; padding-top: 60px; width: 300px; }@media screen and (max-width: 1024px) {  .typora-export-sidebar, .typora-export-sidebar .outline-content { width: 240px; }}@media screen and (max-width: 800px) {  .typora-export-sidebar { display: none; }}.outline-content li, .outline-content ul { margin-left: 0px; margin-right: 0px; padding-left: 0px; padding-right: 0px; list-style: none; overflow-wrap: anywhere; }.outline-content ul { margin-top: 0px; margin-bottom: 0px; }.outline-content strong { font-weight: 400; }.outline-expander { width: 1rem; height: 1.42857rem; position: relative; display: table-cell; vertical-align: middle; cursor: pointer; padding-left: 4px; }.outline-expander::before { content: ""; position: relative; font-family: Ionicons; display: inline-block; font-size: 8px; vertical-align: middle; }.outline-item { padding-top: 3px; padding-bottom: 3px; cursor: pointer; }.outline-expander:hover::before { content: ""; }.outline-h1 > .outline-item { padding-left: 0px; }.outline-h2 > .outline-item { padding-left: 1em; }.outline-h3 > .outline-item { padding-left: 2em; }.outline-h4 > .outline-item { padding-left: 3em; }.outline-h5 > .outline-item { padding-left: 4em; }.outline-h6 > .outline-item { padding-left: 5em; }.outline-label { cursor: pointer; display: table-cell; vertical-align: middle; text-decoration: none; color: inherit; }.outline-label:hover { text-decoration: underline; }.outline-item:hover { border-color: rgb(245, 245, 245); background-color: var(--item-hover-bg-color); }.outline-item:hover { margin-left: -28px; margin-right: -28px; border-left: 28px solid transparent; border-right: 28px solid transparent; }.outline-item-single .outline-expander::before, .outline-item-single .outline-expander:hover::before { display: none; }.outline-item-open > .outline-item > .outline-expander::before { content: ""; }.outline-children { display: none; }.info-panel-tab-wrapper { display: none; }.outline-item-open > .outline-children { display: block; }.typora-export .outline-item { padding-top: 1px; padding-bottom: 1px; }.typora-export .outline-item:hover { margin-right: -8px; border-right: 8px solid transparent; }.typora-export .outline-expander::before { content: "+"; font-family: inherit; top: -1px; }.typora-export .outline-expander:hover::before, .typora-export .outline-item-open > .outline-item > .outline-expander::before { content: "−"; }.typora-export-collapse-outline .outline-children { display: none; }.typora-export-collapse-outline .outline-item-open > .outline-children, .typora-export-no-collapse-outline .outline-children { display: block; }.typora-export-no-collapse-outline .outline-expander::before { content: "" !important; }.typora-export-show-outline .outline-item-active > .outline-item .outline-label { font-weight: 700; }.md-inline-math-container mjx-container { zoom: 0.95; }mjx-container { break-inside: avoid; }.md-alert.md-alert-note { border-left-color: rgb(9, 105, 218); }.md-alert.md-alert-important { border-left-color: rgb(130, 80, 223); }.md-alert.md-alert-warning { border-left-color: rgb(154, 103, 0); }.md-alert.md-alert-tip { border-left-color: rgb(31, 136, 61); }.md-alert.md-alert-caution { border-left-color: rgb(207, 34, 46); }.md-alert { padding: 0px 1em; margin-bottom: 16px; color: inherit; border-left: 0.25em solid rgb(0, 0, 0); }.md-alert-text-note { color: rgb(9, 105, 218); }.md-alert-text-important { color: rgb(130, 80, 223); }.md-alert-text-warning { color: rgb(154, 103, 0); }.md-alert-text-tip { color: rgb(31, 136, 61); }.md-alert-text-caution { color: rgb(207, 34, 46); }.md-alert-text { font-size: 0.9rem; font-weight: 700; }.md-alert-text svg { fill: currentcolor; position: relative; top: 0.125em; margin-right: 1ch; overflow: visible; }.md-alert-text-container::after { content: attr(data-text); text-transform: capitalize; pointer-events: none; margin-right: 1ch; }.CodeMirror { height: auto; }.CodeMirror.cm-s-inner { background: inherit; }.CodeMirror-scroll { overflow: auto hidden; z-index: 3; }.CodeMirror-gutter-filler, .CodeMirror-scrollbar-filler { background-color: rgb(255, 255, 255); }.CodeMirror-gutters { border-right: 1px solid rgb(221, 221, 221); background: inherit; white-space: nowrap; }.CodeMirror-linenumber { padding: 0px 3px 0px 5px; text-align: right; color: rgb(153, 153, 153); }.cm-s-inner .cm-keyword { color: rgb(119, 0, 136); }.cm-s-inner .cm-atom, .cm-s-inner.cm-atom { color: rgb(34, 17, 153); }.cm-s-inner .cm-number { color: rgb(17, 102, 68); }.cm-s-inner .cm-def { color: rgb(0, 0, 255); }.cm-s-inner .cm-variable { color: rgb(0, 0, 0); }.cm-s-inner .cm-variable-2 { color: rgb(0, 85, 170); }.cm-s-inner .cm-variable-3 { color: rgb(0, 136, 85); }.cm-s-inner .cm-string { color: rgb(170, 17, 17); }.cm-s-inner .cm-property { color: rgb(0, 0, 0); }.cm-s-inner .cm-operator { color: rgb(152, 26, 26); }.cm-s-inner .cm-comment, .cm-s-inner.cm-comment { color: rgb(170, 85, 0); }.cm-s-inner .cm-string-2 { color: rgb(255, 85, 0); }.cm-s-inner .cm-meta { color: rgb(85, 85, 85); }.cm-s-inner .cm-qualifier { color: rgb(85, 85, 85); }.cm-s-inner .cm-builtin { color: rgb(51, 0, 170); }.cm-s-inner .cm-bracket { color: rgb(153, 153, 119); }.cm-s-inner .cm-tag { color: rgb(17, 119, 0); }.cm-s-inner .cm-attribute { color: rgb(0, 0, 204); }.cm-s-inner .cm-header, .cm-s-inner.cm-header { color: rgb(0, 0, 255); }.cm-s-inner .cm-quote, .cm-s-inner.cm-quote { color: rgb(0, 153, 0); }.cm-s-inner .cm-hr, .cm-s-inner.cm-hr { color: rgb(153, 153, 153); }.cm-s-inner .cm-link, .cm-s-inner.cm-link { color: rgb(0, 0, 204); }.cm-negative { color: rgb(221, 68, 68); }.cm-positive { color: rgb(34, 153, 34); }.cm-header, .cm-strong { font-weight: 700; }.cm-del { text-decoration: line-through; }.cm-em { font-style: italic; }.cm-link { text-decoration: underline; }.cm-error { color: red; }.cm-invalidchar { color: red; }.cm-constant { color: rgb(38, 139, 210); }.cm-defined { color: rgb(181, 137, 0); }div.CodeMirror span.CodeMirror-matchingbracket { color: rgb(0, 255, 0); }div.CodeMirror span.CodeMirror-nonmatchingbracket { color: rgb(255, 34, 34); }.cm-s-inner .CodeMirror-activeline-background { background: inherit; }.CodeMirror { position: relative; overflow: hidden; }.CodeMirror-scroll { height: 100%; outline: 0px; position: relative; box-sizing: content-box; background: inherit; }.CodeMirror-sizer { position: relative; }.CodeMirror-gutter-filler, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-vscrollbar { position: absolute; z-index: 6; display: none; outline: 0px; }.CodeMirror-vscrollbar { right: 0px; top: 0px; overflow: hidden; }.CodeMirror-hscrollbar { bottom: 0px; left: 0px; overflow: auto hidden; }.CodeMirror-scrollbar-filler { right: 0px; bottom: 0px; }.CodeMirror-gutter-filler { left: 0px; bottom: 0px; }.CodeMirror-gutters { position: absolute; left: 0px; top: 0px; padding-bottom: 10px; z-index: 3; overflow-y: hidden; }.CodeMirror-gutter { white-space: normal; height: 100%; box-sizing: content-box; padding-bottom: 30px; margin-bottom: -32px; display: inline-block; }.CodeMirror-gutter-wrapper { position: absolute; z-index: 4; background: 0px 0px !important; border: none !important; }.CodeMirror-gutter-background { position: absolute; top: 0px; bottom: 0px; z-index: 4; }.CodeMirror-gutter-elt { position: absolute; cursor: default; z-index: 4; }.CodeMirror-lines { cursor: text; }.CodeMirror pre { border-radius: 0px; border-width: 0px; background: 0px 0px; font-family: inherit; font-size: inherit; margin: 0px; white-space: pre; overflow-wrap: normal; color: inherit; z-index: 2; position: relative; overflow: visible; }.CodeMirror-wrap pre { overflow-wrap: break-word; white-space: pre-wrap; word-break: normal; }.CodeMirror-code pre { border-right: 30px solid transparent; width: fit-content; }.CodeMirror-wrap .CodeMirror-code pre { border-right: none; width: auto; }.CodeMirror-linebackground { position: absolute; inset: 0px; z-index: 0; }.CodeMirror-linewidget { position: relative; z-index: 2; overflow: auto; }.CodeMirror-wrap .CodeMirror-scroll { overflow-x: hidden; }.CodeMirror-measure { position: absolute; width: 100%; height: 0px; overflow: hidden; visibility: hidden; }.CodeMirror-measure pre { position: static; }.CodeMirror div.CodeMirror-cursor { position: absolute; visibility: hidden; border-right: none; width: 0px; }.CodeMirror div.CodeMirror-cursor { visibility: hidden; }.CodeMirror-focused div.CodeMirror-cursor { visibility: inherit; }.cm-searching { background: rgba(255, 255, 0, 0.4); }span.cm-underlined { text-decoration: underline; }span.cm-strikethrough { text-decoration: line-through; }.cm-tw-syntaxerror { color: rgb(255, 255, 255); background-color: rgb(153, 0, 0); }.cm-tw-deleted { text-decoration: line-through; }.cm-tw-header5 { font-weight: 700; }.cm-tw-listitem:first-child { padding-left: 10px; }.cm-tw-box { border-style: solid; border-right-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-color: inherit; border-top-width: 0px !important; }.cm-tw-underline { text-decoration: underline; }@media print {  .CodeMirror div.CodeMirror-cursor { visibility: hidden; }}:root {    --side-bar-bg-color: #fafafa;    --control-text-color: #777;}@include-when-export url(https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext);/* open-sans-regular - latin-ext_latin */  /* open-sans-italic - latin-ext_latin */    /* open-sans-700 - latin-ext_latin */    /* open-sans-700italic - latin-ext_latin */  html {    font-size: 16px;    -webkit-font-smoothing: antialiased;}body {    font-family: "Open Sans","Clear Sans", "Helvetica Neue", Helvetica, Arial, 'Segoe UI Emoji', sans-serif;    color: rgb(51, 51, 51);    line-height: 1.6;}#write {    max-width: 860px;  	margin: 0 auto;  	padding: 30px;    padding-bottom: 100px;}@media only screen and (min-width: 1400px) {	#write {		max-width: 1024px;	}}@media only screen and (min-width: 1800px) {	#write {		max-width: 1200px;	}}#write > ul:first-child,#write > ol:first-child{    margin-top: 30px;}a {    color: #4183C4;}h1,h2,h3,h4,h5,h6 {    position: relative;    margin-top: 1rem;    margin-bottom: 1rem;    font-weight: bold;    line-height: 1.4;    cursor: text;}h1:hover a.anchor,h2:hover a.anchor,h3:hover a.anchor,h4:hover a.anchor,h5:hover a.anchor,h6:hover a.anchor {    text-decoration: none;}h1 tt,h1 code {    font-size: inherit;}h2 tt,h2 code {    font-size: inherit;}h3 tt,h3 code {    font-size: inherit;}h4 tt,h4 code {    font-size: inherit;}h5 tt,h5 code {    font-size: inherit;}h6 tt,h6 code {    font-size: inherit;}h1 {    font-size: 2.25em;    line-height: 1.2;    border-bottom: 1px solid #eee;}h2 {    font-size: 1.75em;    line-height: 1.225;    border-bottom: 1px solid #eee;}/*@media print {    .typora-export h1,    .typora-export h2 {        border-bottom: none;        padding-bottom: initial;    }    .typora-export h1::after,    .typora-export h2::after {        content: "";        display: block;        height: 100px;        margin-top: -96px;        border-top: 1px solid #eee;    }}*/h3 {    font-size: 1.5em;    line-height: 1.43;}h4 {    font-size: 1.25em;}h5 {    font-size: 1em;}h6 {   font-size: 1em;    color: #777;}p,blockquote,ul,ol,dl,table{    margin: 0.8em 0;}li>ol,li>ul {    margin: 0 0;}hr {    height: 2px;    padding: 0;    margin: 16px 0;    background-color: #e7e7e7;    border: 0 none;    overflow: hidden;    box-sizing: content-box;}li p.first {    display: inline-block;}ul,ol {    padding-left: 30px;}ul:first-child,ol:first-child {    margin-top: 0;}ul:last-child,ol:last-child {    margin-bottom: 0;}blockquote {    border-left: 4px solid #dfe2e5;    padding: 0 15px;    color: #777777;}blockquote blockquote {    padding-right: 0;}table {    padding: 0;    word-break: initial;}table tr {    border: 1px solid #dfe2e5;    margin: 0;    padding: 0;}table tr:nth-child(2n),thead {    background-color: #f8f8f8;}table th {    font-weight: bold;    border: 1px solid #dfe2e5;    border-bottom: 0;    margin: 0;    padding: 6px 13px;}table td {    border: 1px solid #dfe2e5;    margin: 0;    padding: 6px 13px;}table th:first-child,table td:first-child {    margin-top: 0;}table th:last-child,table td:last-child {    margin-bottom: 0;}.CodeMirror-lines {    padding-left: 4px;}.code-tooltip {    box-shadow: 0 1px 1px 0 rgba(0,28,36,.3);    border-top: 1px solid #eef2f2;}.md-fences,code,tt {    border: 1px solid #e7eaed;    background-color: #f8f8f8;    border-radius: 3px;    padding: 0;    padding: 2px 4px 0px 4px;    font-size: 0.9em;}code {    background-color: #f3f4f4;    padding: 0 2px 0 2px;}.md-fences {    margin-bottom: 15px;    margin-top: 15px;    padding-top: 8px;    padding-bottom: 6px;}.md-task-list-item > input {  margin-left: -1.3em;}@media print {    html {        font-size: 13px;    }    pre {        page-break-inside: avoid;        word-wrap: break-word;    }}.md-fences {	background-color: #f8f8f8;}#write pre.md-meta-block {	padding: 1rem;    font-size: 85%;    line-height: 1.45;    background-color: #f7f7f7;    border: 0;    border-radius: 3px;    color: #777777;    margin-top: 0 !important;}.mathjax-block>.code-tooltip {	bottom: .375rem;}.md-mathjax-midline {    background: #fafafa;}#write>h3.md-focus:before{	left: -1.5625rem;	top: .375rem;}#write>h4.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}#write>h5.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}#write>h6.md-focus:before{	left: -1.5625rem;	top: .285714286rem;}.md-image>.md-meta {    /*border: 1px solid #ddd;*/    border-radius: 3px;    padding: 2px 0px 0px 4px;    font-size: 0.9em;    color: inherit;}.md-tag {    color: #a7a7a7;    opacity: 1;}.md-toc {     margin-top:20px;    padding-bottom:20px;}.sidebar-tabs {    border-bottom: none;}#typora-quick-open {    border: 1px solid #ddd;    background-color: #f8f8f8;}#typora-quick-open-item {    background-color: #FAFAFA;    border-color: #FEFEFE #e5e5e5 #e5e5e5 #eee;    border-style: solid;    border-width: 1px;}/** focus mode */.on-focus-mode blockquote {    border-left-color: rgba(85, 85, 85, 0.12);}header, .context-menu, .megamenu-content, footer{    font-family: "Segoe UI", "Arial", sans-serif;}.file-node-content:hover .file-node-icon,.file-node-content:hover .file-node-open-state{    visibility: visible;}.mac-seamless-mode #typora-sidebar {    background-color: #fafafa;    background-color: var(--side-bar-bg-color);}.mac-os #write{    caret-color: AccentColor;}.md-lang {    color: #b4654d;}/*.html-for-mac {    --item-hover-bg-color: #E6F0FE;}*/#md-notification .btn {    border: 0;}.dropdown-menu .divider {    border-color: #e5e5e5;    opacity: 0.4;}.ty-preferences .window-content {    background-color: #fafafa;}.ty-preferences .nav-group-item.active {    color: white;    background: #999;}.menu-item-container a.menu-style-btn {    background-color: #f5f8fa;    background-image: linear-gradient( 180deg , hsla(0, 0%, 100%, 0.8), hsla(0, 0%, 100%, 0)); }</style><title>【华为OD-E卷 - 99 字符串解密 100分（python、java、c++、js、c）】</title></head><body class='typora-export os-windows typora-export-show-outline typora-export-no-collapse-outline'><div class='typora-export-content'><div class="typora-export-sidebar"><div class="outline-content"><li class="outline-item-wrapper outline-h2"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#注意点此-链接-其他算法题目详细解法">注意：点此 链接 其他算法题目详细解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h2"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#投稿交流错误反馈-v">投稿、交流、错误反馈 +V：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#"></a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#华为od-e卷---字符串解密-100分pythonjavacjsc）">【华为OD-E卷 - 字符串解密 100分（python、java、c++、js、c）】</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#题目">题目</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h3"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入描述">输入描述</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h3"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出描述">输出描述</a></div><ul class="outline-children"></ul></li></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例">用例</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h4"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例一">用例一：</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入">输入：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出">输出：</a></div><ul class="outline-children"></ul></li></ul></li><li class="outline-item-wrapper outline-h4"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#用例二">用例二：</a></div><ul class="outline-children"><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输入-2">输入：</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h5"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#输出-2">输出：</a></div><ul class="outline-children"></ul></li></ul></li></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#python解法">python解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#java解法">java解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#c解法">C++解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#c解法-2">C解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#js解法">JS解法</a></div><ul class="outline-children"></ul></li><li class="outline-item-wrapper outline-h1"><div class="outline-item"><span class="outline-expander"></span><a class="outline-label" href="#注意">注意：</a></div><ul class="outline-children"></ul></li></div></div><div id='write'  class=''><h2 id='注意点此-链接-其他算法题目详细解法'><a href='https://blog.csdn.net/codeclimb/category_12842753.html?spm=1001.2014.3001.5482'><span>注意：点此 链接 其他算法题目详细解法</span></a></h2><h2 id='投稿交流错误反馈-v'><span>投稿、交流、错误反馈 +V：</span></h2><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="http"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="http"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-error">locally-attached</span></span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h1></h1><h1 id='华为od-e卷---字符串解密-100分pythonjavacjsc）'><span>【华为OD-E卷 - 字符串解密 100分（python、java、c++、js、c）】</span></h1><h1 id='题目'><span>题目</span></h1><p><strong><span>给定两个字符串string1和string2。 string1是一个被加扰的字符串。</span><span>string1由小写英文字母（’a’~ ’z’）和数字字符（’0’~ ’9’）组成，而加扰字符串由’0’~ ’9’、’a’~’f’组成。</span><span>string1里面可能包含0个或多个加扰子串，剩下可能有0个或多个有效子串，这些有效子串被加扰子串隔开。</span><span>string2是一个参考字符串，仅由小写英文字母（’a’~’z’）组成。</span><span>你需要在string1字符串里找到一个有效子串，这个有效子串要同时满足下面两个条件：</span><span>（1）这个有效子串里不同字母的数量不超过且最接近于string2里不同字母的数量，即小于或等于string2里不同字母的数量的同时且最大。</span><span>（2）这个有效子串是满足条件（1）里的所有子串（如果有多个的话）里字典序最大的一个。</span><span>如果没有找到合适条件的子串的话，请输出”Not Found”</span></strong></p><h3 id='输入描述'><span>输入描述</span></h3><ul><li><p><span>input_string1 input_string2</span></p></li></ul><h3 id='输出描述'><span>输出描述</span></h3><ul><li><p><span>output_string</span></p></li></ul><h1 id='用例'><span>用例</span></h1><h4 id='用例一'><span>用例一：</span></h4><h5 id='输入'><span>输入：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">123admyffc79pt</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">ssyy</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 46px;"></div><div class="CodeMirror-gutters" style="display: none; height: 46px;"></div></div></div></pre><h5 id='输出'><span>输出：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">pt</span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h4 id='用例二'><span>用例二：</span></h4><h5 id='输入-2'><span>输入：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">123admyffc79ptaagghi2222smeersst88mnrt</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">ssyyfgh</span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 46px;"></div><div class="CodeMirror-gutters" style="display: none; height: 46px;"></div></div></div></pre><h5 id='输出-2'><span>输出：</span></h5><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang=""><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang=""><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">mnrt</span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><h1 id='python解法'><span>python解法</span></h1><ul><li><p><span>解题思路：</span></p></li><li><p><span>该代码旨在从字符串 s1 中提取不包含十六进制字符（0-9, a-f）的有效子字符串，并从中找到与字符串 s2 的字符集合限制匹配的最长有效子字符串。</span></p></li></ul><p><span>分割字符串 s1</span></p><p><span>使用正则表达式 &quot;[0-9a-f]+&quot; 将字符串 s1 按照十六进制字符分割，提取出不含这些字符的子字符串。</span><span>这些子字符串可能包含重复字符或其他字符。</span><span>筛选有效子字符串</span></p><p><span>一个有效的子字符串必须满足以下条件：</span><span>子字符串中不同字符的数量不超过字符串 s2 的字符种类数。</span><span>通过 set 数据结构来判断子字符串的字符种类数量是否满足要求。</span><span>寻找符合条件的最长子字符串</span></p><p><span>在筛选出的子字符串中，按照以下规则排序并取出第一个字符串作为结果：</span><span>优先按照字符种类数量降序排列（字符种类越多越靠前）。</span><span>如果字符种类数量相同，按字典序降序排列。</span><span>返回结果</span></p><p><span>如果筛选后有符合条件的子字符串，返回最符合条件的字符串。</span><span>如果没有符合条件的子字符串，则返回 &quot;Not Found&quot;。</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="python" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="python"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><span><span>​</span>x</span></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">re</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">def</span> <span class="cm-def">split_valid_strings</span>(<span class="cm-variable">s1</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"""</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  分割字符串 s1，提取不包含十六进制字符 (0-9, a-f) 的子字符串。</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  """</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 定义正则表达式匹配十六进制字符</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">pattern</span> <span class="cm-operator">=</span> <span class="cm-string">r"[0-9a-f]+"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 使用 re.split 分割字符串并返回分割结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">re</span>.<span class="cm-property">split</span>(<span class="cm-variable">pattern</span>, <span class="cm-variable">s1</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">def</span> <span class="cm-def">filter_valid_strings</span>(<span class="cm-variable">valid_strings</span>, <span class="cm-variable">limit</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"""</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  过滤有效的子字符串：</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  - 排除空字符串。</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  - 子字符串中字符种类数不超过 limit。</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  """</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 使用 filter 和 lambda 表达式过滤符合条件的字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-builtin">list</span>(<span class="cm-builtin">filter</span>(<span class="cm-keyword">lambda</span> <span class="cm-variable">v</span>: <span class="cm-variable">v</span> <span class="cm-operator">!=</span> <span class="cm-string">""</span> <span class="cm-keyword">and</span> <span class="cm-builtin">len</span>(<span class="cm-builtin">set</span>(<span class="cm-variable">v</span>)) <span class="cm-operator">&lt;=</span> <span class="cm-variable">limit</span>, <span class="cm-variable">valid_strings</span>))</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">def</span> <span class="cm-def">find_largest_valid_string</span>(<span class="cm-variable">s1</span>, <span class="cm-variable">s2</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"""</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  从 s1 中提取有效子字符串，筛选符合 s2 字符种类限制的最长有效字符串。</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  """</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 分割字符串 s1，获取不包含十六进制字符的子字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">valid_strings</span> <span class="cm-operator">=</span> <span class="cm-variable">split_valid_strings</span>(<span class="cm-variable">s1</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 计算 s2 中的字符种类数，作为子字符串的字符种类限制</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">limit</span> <span class="cm-operator">=</span> <span class="cm-builtin">len</span>(<span class="cm-builtin">set</span>(<span class="cm-variable">s2</span>))</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 筛选出符合字符种类限制的子字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">filtered_strings</span> <span class="cm-operator">=</span> <span class="cm-variable">filter_valid_strings</span>(<span class="cm-variable">valid_strings</span>, <span class="cm-variable">limit</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 如果有符合条件的子字符串，则找出最长的子字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">if</span> <span class="cm-variable">filtered_strings</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 按以下规则排序：</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 1. 字符种类数（降序）。</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 2. 字符串字典序（降序）。</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">filtered_strings</span>.<span class="cm-property">sort</span>(<span class="cm-variable">key</span><span class="cm-operator">=</span><span class="cm-keyword">lambda</span> <span class="cm-variable">x</span>: (<span class="cm-operator">-</span><span class="cm-builtin">len</span>(<span class="cm-builtin">set</span>(<span class="cm-variable">x</span>)), [<span class="cm-operator">-</span><span class="cm-builtin">ord</span>(<span class="cm-variable">c</span>) <span class="cm-keyword">for</span> <span class="cm-variable">c</span> <span class="cm-keyword">in</span> <span class="cm-variable">x</span>]))</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">filtered_strings</span>[<span class="cm-number">0</span>]</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">else</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment"># 如果没有符合条件的子字符串，返回 "Not Found"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-string">"Not Found"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">def</span> <span class="cm-def">main</span>():</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"""</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  主函数：处理输入和输出。</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-string"> &nbsp;  """</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 输入字符串 s1 和 s2</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">s1</span> <span class="cm-operator">=</span> <span class="cm-builtin">input</span>()</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">s2</span> <span class="cm-operator">=</span> <span class="cm-builtin">input</span>()</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 调用主逻辑函数计算结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">result</span> <span class="cm-operator">=</span> <span class="cm-variable">find_largest_valid_string</span>(<span class="cm-variable">s1</span>, <span class="cm-variable">s2</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment"># 输出结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-builtin">print</span>(<span class="cm-variable">result</span>)</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">if</span> <span class="cm-variable">__name__</span> <span class="cm-operator">==</span> <span class="cm-string">"__main__"</span>:</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">main</span>()</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 1474px;"></div><div class="CodeMirror-gutters" style="display: none; height: 1474px;"></div></div></div></pre><h1 id='java解法'><span>java解法</span></h1><ul><li><p><span>解题思路</span></p></li><li><p><span>该代码旨在从字符串 s1 中提取出不包含十六进制字符（0-9, a-f）的子字符串，并从中找到符合字符串 s2 的字符种类限制的最长有效子字符串。</span></p></li></ul><p><span>分割字符串 s1</span></p><p><span>使用正则表达式 &quot;[0-9a-f]+&quot; 将字符串 s1 按照十六进制字符分割，提取出不含这些字符的子字符串。</span><span>结果存储在 validParts 数组中。</span><span>计算 s2 的字符种类</span></p><p><span>遍历 s2 中的所有字符，统计唯一字符的数量，作为字符种类限制 targetCount。</span><span>使用一个 HashSet 来存储字符，从而快速计算唯一字符数量。</span><span>筛选符合条件的子字符串</span></p><p><span>遍历 validParts 中的每个子字符串：</span><span>如果子字符串为空，则跳过。</span><span>计算子字符串中的唯一字符数量 uniqueInPart。</span><span>如果 uniqueInPart 不超过 targetCount：</span><span>检查是否比当前结果更优：</span><span>如果 uniqueInPart 更大，则更新结果。</span><span>如果 uniqueInPart 相同，则按字典序比较，选择较大的字符串。</span><span>返回结果</span></p><p><span>如果有符合条件的子字符串，返回最符合条件的字符串。</span><span>如果没有符合条件的子字符串，返回 &quot;Not Found&quot;。</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="java" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="java"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">import</span> <span class="cm-variable">java</span>.<span class="cm-variable">util</span>.<span class="cm-operator">*</span>;</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">public</span> <span class="cm-keyword">class</span> <span class="cm-def">Main</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">public</span> <span class="cm-keyword">static</span> <span class="cm-variable-3">void</span> <span class="cm-variable">main</span>(<span class="cm-variable-3">String</span>[] <span class="cm-variable">args</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">Scanner</span> <span class="cm-variable">sc</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">Scanner</span>(<span class="cm-variable">System</span>.<span class="cm-variable">in</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 输入两个字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">String</span> <span class="cm-variable">s1</span> <span class="cm-operator">=</span> <span class="cm-variable">sc</span>.<span class="cm-variable">next</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">String</span> <span class="cm-variable">s2</span> <span class="cm-operator">=</span> <span class="cm-variable">sc</span>.<span class="cm-variable">next</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 调用方法计算并输出结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">System</span>.<span class="cm-variable">out</span>.<span class="cm-variable">println</span>(<span class="cm-variable">findSub</span>(<span class="cm-variable">s1</span>, <span class="cm-variable">s2</span>));</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">/**</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">* 从字符串 s1 中找到符合 s2 字符种类限制的最长有效子字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">*</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">* @param s1 原始字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">* @param s2 用于限制字符种类的字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">* @return 符合条件的最长子字符串，如果没有，返回 "Not Found"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">*/</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">public</span> <span class="cm-keyword">static</span> <span class="cm-variable-3">String</span> <span class="cm-variable">findSub</span>(<span class="cm-variable-3">String</span> <span class="cm-variable">s1</span>, <span class="cm-variable-3">String</span> <span class="cm-variable">s2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 分割字符串 s1，提取不包含十六进制字符的部分</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">String</span>[] <span class="cm-variable">validParts</span> <span class="cm-operator">=</span> <span class="cm-variable">s1</span>.<span class="cm-variable">split</span>(<span class="cm-string">"[0-9a-f]+"</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 计算 s2 中的字符种类限制</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">targetCount</span> <span class="cm-operator">=</span> <span class="cm-variable">uniqueCount</span>(<span class="cm-variable">s2</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 初始化结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">String</span> <span class="cm-variable">result</span> <span class="cm-operator">=</span> <span class="cm-string">"Not Found"</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">maxUnique</span> <span class="cm-operator">=</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 遍历所有有效部分</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">String</span> <span class="cm-variable">part</span> : <span class="cm-variable">validParts</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-operator">!</span><span class="cm-variable">part</span>.<span class="cm-variable">isEmpty</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 计算当前部分的字符种类数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">uniqueInPart</span> <span class="cm-operator">=</span> <span class="cm-variable">uniqueCount</span>(<span class="cm-variable">part</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果当前部分满足字符种类限制</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">uniqueInPart</span> <span class="cm-operator">&lt;=</span> <span class="cm-variable">targetCount</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 判断是否比当前结果更优</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">uniqueInPart</span> <span class="cm-operator">&gt;</span> <span class="cm-variable">maxUnique</span> <span class="cm-operator">||</span> (<span class="cm-variable">uniqueInPart</span> <span class="cm-operator">==</span> <span class="cm-variable">maxUnique</span> <span class="cm-operator">&amp;&amp;</span> <span class="cm-variable">part</span>.<span class="cm-variable">compareTo</span>(<span class="cm-variable">result</span>) <span class="cm-operator">&gt;</span> <span class="cm-number">0</span>)) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">result</span> <span class="cm-operator">=</span> <span class="cm-variable">part</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">maxUnique</span> <span class="cm-operator">=</span> <span class="cm-variable">uniqueInPart</span>; <span class="cm-comment">// 更新最大字符种类数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 返回结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">result</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">/**</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">* 计算字符串中的唯一字符数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">*</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">* @param s 输入字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">* @return 唯一字符的数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; <span class="cm-comment">*/</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">private</span> <span class="cm-keyword">static</span> <span class="cm-variable-3">int</span> <span class="cm-variable">uniqueCount</span>(<span class="cm-variable-3">String</span> <span class="cm-variable">s</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 使用 HashSet 统计唯一字符</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">Set</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">Character</span><span class="cm-operator">&gt;</span> <span class="cm-variable">uniqueChars</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">HashSet</span><span class="cm-operator">&lt;&gt;</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-variable-3">char</span> <span class="cm-variable">c</span> : <span class="cm-variable">s</span>.<span class="cm-variable">toCharArray</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">uniqueChars</span>.<span class="cm-variable">add</span>(<span class="cm-variable">c</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">uniqueChars</span>.<span class="cm-variable">size</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 1589px;"></div><div class="CodeMirror-gutters" style="display: none; height: 1589px;"></div></div></div></pre><h1 id='c解法'><span>C++解法</span></h1><ul><li><p><span>解题思路</span></p></li><li><p><span>这段代码的目标是，从输入的字符串 str1 中找到一个最长的子串，且该子串中唯一字符的数量不能超过另一个输入字符串 str2 中的唯一字符数量。如果没有满足条件的子串，返回 &quot;Not Found&quot;。</span></p></li></ul><p><span>主要步骤：</span></p><p><span>使用正则表达式将 str1 切分为非十六进制格式的子串。</span><span>将 str2 转换为一个集合，获取其中唯一字符的数量。</span><span>遍历切分出的所有子串，筛选出唯一字符数不超过 str2 中唯一字符数的子串。</span><span>按照以下规则对筛选出的子串进行排序：</span><span>首先比较子串的唯一字符数量，数量多的优先。</span><span>如果唯一字符数量相同，长度更长的优先。</span><span>返回符合条件的最长子串；如果没有符合条件的子串，返回 &quot;Not Found&quot;。</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="cpp" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="cpp"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;iostream&gt;</span></span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;string&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;vector&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;set&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;algorithm&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-meta">#include &lt;regex&gt;</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">using</span> <span class="cm-keyword">namespace</span> <span class="cm-def">std</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 获取结果的函数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">string</span> <span class="cm-def">getResult</span>(<span class="cm-keyword">const</span> <span class="cm-variable">string</span><span class="cm-operator">&amp;</span> <span class="cm-variable">str1</span>, <span class="cm-keyword">const</span> <span class="cm-variable">string</span><span class="cm-operator">&amp;</span> <span class="cm-variable">str2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 使用正则表达式匹配非16进制子串（十六进制字符包括0-9和a-f）</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">regex</span> <span class="cm-variable">pattern</span>(<span class="cm-string">"[0-9a-f]+"</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 使用正则表达式分割字符串，获取非16进制的子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">sregex_token_iterator</span> <span class="cm-variable">it</span>(<span class="cm-variable">str1</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">str1</span>.<span class="cm-variable">end</span>(), <span class="cm-variable">pattern</span>, <span class="cm-operator">-</span><span class="cm-number">1</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">sregex_token_iterator</span> <span class="cm-variable">end</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 计算str2中字符的唯一数量，将其存入集合以去重</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">set</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">char</span><span class="cm-operator">&gt;</span> <span class="cm-variable">str2Set</span>(<span class="cm-variable">str2</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">str2</span>.<span class="cm-variable">end</span>());</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable-3">int</span> <span class="cm-variable">count</span> <span class="cm-operator">=</span> <span class="cm-variable">str2Set</span>.<span class="cm-variable">size</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 保存符合条件的子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">vector</span><span class="cm-operator">&lt;</span><span class="cm-variable">string</span><span class="cm-operator">&gt;</span> <span class="cm-variable">valids</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">while</span> (<span class="cm-variable">it</span> <span class="cm-operator">!=</span> <span class="cm-variable">end</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">string</span> <span class="cm-variable">valid</span> <span class="cm-operator">=</span> <span class="cm-operator">*</span><span class="cm-variable">it</span>; <span class="cm-comment">// 获取当前子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 计算子串中唯一字符的集合</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">set</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">char</span><span class="cm-operator">&gt;</span> <span class="cm-variable">validSet</span>(<span class="cm-variable">valid</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">valid</span>.<span class="cm-variable">end</span>());</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果子串不为空且唯一字符数量不超过str2的唯一字符数量，则加入结果列表</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-operator">!</span><span class="cm-variable">valid</span>.<span class="cm-variable">empty</span>() <span class="cm-operator">&amp;&amp;</span> <span class="cm-variable">validSet</span>.<span class="cm-variable">size</span>() <span class="cm-operator">&lt;=</span> <span class="cm-variable">count</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">valids</span>.<span class="cm-variable">push_back</span>(<span class="cm-variable">valid</span>);</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-operator">++</span><span class="cm-variable">it</span>; <span class="cm-comment">// 继续下一个子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 如果存在符合条件的子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-operator">!</span><span class="cm-variable">valids</span>.<span class="cm-variable">empty</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 自定义排序规则：按唯一字符数排序，若相同则按子串长度排序</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">sort</span>(<span class="cm-variable">valids</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">valids</span>.<span class="cm-variable">end</span>(), [<span class="cm-operator">&amp;</span>](<span class="cm-keyword">const</span> <span class="cm-variable">string</span><span class="cm-operator">&amp;</span> <span class="cm-variable">a</span>, <span class="cm-keyword">const</span> <span class="cm-variable">string</span><span class="cm-operator">&amp;</span> <span class="cm-variable">b</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">set</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">char</span><span class="cm-operator">&gt;</span>(<span class="cm-variable">a</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">a</span>.<span class="cm-variable">end</span>()).<span class="cm-variable">size</span>() <span class="cm-operator">!=</span> <span class="cm-variable">set</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">char</span><span class="cm-operator">&gt;</span>(<span class="cm-variable">b</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">b</span>.<span class="cm-variable">end</span>()).<span class="cm-variable">size</span>()) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">set</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">char</span><span class="cm-operator">&gt;</span>(<span class="cm-variable">a</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">a</span>.<span class="cm-variable">end</span>()).<span class="cm-variable">size</span>() <span class="cm-operator">&gt;</span> <span class="cm-variable">set</span><span class="cm-operator">&lt;</span><span class="cm-variable-3">char</span><span class="cm-operator">&gt;</span>(<span class="cm-variable">b</span>.<span class="cm-variable">begin</span>(), <span class="cm-variable">b</span>.<span class="cm-variable">end</span>()).<span class="cm-variable">size</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">a</span>.<span class="cm-variable">size</span>() <span class="cm-operator">&gt;</span> <span class="cm-variable">b</span>.<span class="cm-variable">size</span>();</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  });</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable">valids</span>[<span class="cm-number">0</span>]; &nbsp;<span class="cm-comment">// 返回符合条件的第一个最长子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  } <span class="cm-keyword">else</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-string">"Not Found"</span>; &nbsp;<span class="cm-comment">// 如果没有符合条件的子串，返回Not Found</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable-3">int</span> <span class="cm-def">main</span>() {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 输入字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">string</span> <span class="cm-variable">str1</span>, <span class="cm-variable">str2</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">getline</span>(<span class="cm-variable">cin</span>, <span class="cm-variable">str1</span>); <span class="cm-comment">// 获取第一行输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">getline</span>(<span class="cm-variable">cin</span>, <span class="cm-variable">str2</span>); <span class="cm-comment">// 获取第二行输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 调用函数计算结果并输出</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">cout</span> <span class="cm-operator">&lt;&lt;</span> <span class="cm-variable">getResult</span>(<span class="cm-variable">str1</span>, <span class="cm-variable">str2</span>) <span class="cm-operator">&lt;&lt;</span> <span class="cm-variable">endl</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-number">0</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 1474px;"></div><div class="CodeMirror-gutters" style="display: none; height: 1474px;"></div></div></div></pre><h1 id='c解法-2'><span>C解法</span></h1><ul><li><h3 id='解题思路'><span>解题思路</span></h3></li><li><p>&nbsp;</p></li></ul><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="c"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="c"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation"><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">更新中</span></span></pre></div></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 23px;"></div><div class="CodeMirror-gutters" style="display: none; height: 23px;"></div></div></div></pre><p>&nbsp;</p><h1 id='js解法'><span>JS解法</span></h1><ul><li><h3 id='解题思路-2'><span>解题思路</span></h3></li><li><p><span>目标是从字符串 str1 中找到一个最长的子串，且该子串中的唯一字符数量不能超过字符串 str2 中的唯一字符数量。如果没有满足条件的子串，则返回 &quot;Not Found&quot;。</span></p></li></ul><p><span>具体解题步骤如下：</span></p><p><span>提取非十六进制子串：</span></p><p><span>使用正则表达式匹配 str1 中所有非十六进制字符的子串。</span><span>十六进制字符包括 0-9 和 a-f。</span><span>提取出的子串需排除空子串。</span><span>计算基准字符集：</span></p><p><span>统计字符串 str2 中的唯一字符数量，作为条件基准。</span><span>筛选有效子串：</span></p><p><span>遍历提取出的所有子串，检查其唯一字符数量是否小于或等于 str2 中的唯一字符数量。</span><span>如果符合条件，将其作为候选子串进行比较。</span><span>排序和优先级：</span></p><p><span>如果子串的唯一字符数量更高，则优先。</span><span>如果唯一字符数量相同，选择更大的字典序子串（按照字母顺序比较）。</span><span>返回结果：</span></p><p><span>如果有有效子串，返回符合条件的最长子串。</span><span>如果没有符合条件的子串，返回 &quot;Not Found&quot;</span></p><pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="javascript" style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="javascript"><div style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.51562px; left: 8px;"><textarea autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0" style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: none;"></textarea></div><div class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler" cm-not-content="true"></div><div class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer" style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation" style="position: relative; outline: none;"><div class="CodeMirror-measure"><pre><span>xxxxxxxxxx</span></pre></div><div class="CodeMirror-measure"></div><div style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div class="CodeMirror-activeline" style="position: relative;"><div class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">readline</span> <span class="cm-operator">=</span> <span class="cm-variable">require</span>(<span class="cm-string">"readline"</span>);</span></pre></div><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 创建读取输入的接口</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">const</span> <span class="cm-def">rl</span> <span class="cm-operator">=</span> <span class="cm-variable">readline</span>.<span class="cm-property">createInterface</span>({</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-property">input</span>: <span class="cm-variable">process</span>.<span class="cm-property">stdin</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-property">output</span>: <span class="cm-variable">process</span>.<span class="cm-property">stdout</span>,</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">});</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">let</span> <span class="cm-def">lines</span> <span class="cm-operator">=</span> [];</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 按行读取输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-variable">rl</span>.<span class="cm-property">on</span>(<span class="cm-string">"line"</span>, (<span class="cm-def">line</span>) <span class="cm-operator">=&gt;</span> {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">lines</span>.<span class="cm-property">push</span>(<span class="cm-variable-2">line</span>); <span class="cm-comment">// 将每行输入存入数组</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable">lines</span>.<span class="cm-property">length</span> <span class="cm-operator">===</span> <span class="cm-number">2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">str1</span> <span class="cm-operator">=</span> <span class="cm-variable">lines</span>[<span class="cm-number">0</span>]; <span class="cm-comment">// 第一行输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">str2</span> <span class="cm-operator">=</span> <span class="cm-variable">lines</span>[<span class="cm-number">1</span>]; <span class="cm-comment">// 第二行输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">console</span>.<span class="cm-property">log</span>(<span class="cm-variable">findLargestSubstring</span>(<span class="cm-variable-2">str1</span>, <span class="cm-variable-2">str2</span>)); <span class="cm-comment">// 计算并输出结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable">lines</span> <span class="cm-operator">=</span> []; <span class="cm-comment">// 清空输入数组以便处理下一组输入</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">});</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-comment">// 查找最长有效子串的函数</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span class="cm-keyword">function</span> <span class="cm-def">findLargestSubstring</span>(<span class="cm-def">str1</span>, <span class="cm-def">str2</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 正则表达式匹配十六进制子串，分割出非十六进制子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">reg</span> <span class="cm-operator">=</span> <span class="cm-string-2">/[0-9a-f]+/g</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">validSubs</span> <span class="cm-operator">=</span> <span class="cm-variable-2">str1</span>.<span class="cm-property">split</span>(<span class="cm-variable-2">reg</span>).<span class="cm-property">filter</span>(<span class="cm-variable">Boolean</span>); <span class="cm-comment">// 分割后过滤掉空字符串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 计算str2中唯一字符的数量，使用Set去重</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">refCount</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">Set</span>(<span class="cm-variable-2">str2</span>).<span class="cm-property">size</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 初始化结果变量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">let</span> <span class="cm-def">maxSub</span> <span class="cm-operator">=</span> <span class="cm-string">"Not Found"</span>; <span class="cm-comment">// 最终返回的最长有效子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">let</span> <span class="cm-def">maxUniqueCount</span> <span class="cm-operator">=</span> <span class="cm-operator">-</span><span class="cm-number">1</span>; <span class="cm-comment">// 最大唯一字符数，用于比较</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 遍历所有有效子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">for</span> (<span class="cm-keyword">const</span> <span class="cm-def">sub</span> <span class="cm-keyword">of</span> <span class="cm-variable-2">validSubs</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 计算当前子串的唯一字符数量</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">const</span> <span class="cm-def">uniqueCount</span> <span class="cm-operator">=</span> <span class="cm-keyword">new</span> <span class="cm-variable">Set</span>(<span class="cm-variable-2">sub</span>).<span class="cm-property">size</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 判断是否满足条件并更新结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-keyword">if</span> (<span class="cm-variable-2">uniqueCount</span> <span class="cm-operator">&lt;=</span> <span class="cm-variable-2">refCount</span> <span class="cm-operator">&amp;&amp;</span> <span class="cm-variable-2">uniqueCount</span> <span class="cm-operator">&gt;</span> <span class="cm-variable-2">maxUniqueCount</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果唯一字符数小于基准且大于当前最大值，更新结果</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">maxUniqueCount</span> <span class="cm-operator">=</span> <span class="cm-variable-2">uniqueCount</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">maxSub</span> <span class="cm-operator">=</span> <span class="cm-variable-2">sub</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  } <span class="cm-keyword">else</span> <span class="cm-keyword">if</span> (<span class="cm-variable-2">uniqueCount</span> <span class="cm-operator">===</span> <span class="cm-variable-2">maxUniqueCount</span> <span class="cm-operator">&amp;&amp;</span> <span class="cm-variable-2">sub</span> <span class="cm-operator">&gt;</span> <span class="cm-variable-2">maxSub</span>) {</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-comment">// 如果唯一字符数相同，按字典序比较，取更大的子串</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span class="cm-variable-2">maxSub</span> <span class="cm-operator">=</span> <span class="cm-variable-2">sub</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp; &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp;  }</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-comment">// 返回最长子串或"Not Found"</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-keyword">return</span> <span class="cm-variable-2">maxSub</span>;</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;">}</span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp=""></span></span></pre></div></div></div></div></div><div style="position: absolute; height: 0px; width: 1px; border-bottom: 0px solid transparent; top: 1244px;"></div><div class="CodeMirror-gutters" style="display: none; height: 1244px;"></div></div></div></pre><h1 id='注意'><span>注意：</span></h1><p><strong><span>如果发现代码有用例覆盖不到的情况，欢迎反馈！会在第一时间修正，更新。</span></strong><strong><span>解题不易，如对您有帮助，欢迎点赞/收藏</span></strong></p></div></div><script>(function(){function e(e,n,i){document.addEventListener(e,function(e){if(!e.defaultPrevented)for(var t=e.target;t&&t!=this;t=t.parentNode)if(t.matches(n)){!1===i.call(t,e)&&(e.preventDefault(),e.stopPropagation());break}},!1)}var t=document.body.parentElement,i=[],r=null,o=document.body.classList.contains("typora-export-collapse-outline");function a(){return t.scrollTop}e("click",".outline-expander",function(e){var t=this.closest(".outline-item-wrapper").classList;return t.contains("outline-item-open")?t.remove("outline-item-open"):t.add("outline-item-open"),u(),!1}),e("click",".outline-item",function(e){var t=this.querySelector(".outline-label");location.hash="#"+t.getAttribute("href"),o&&((t=this.closest(".outline-item-wrapper").classList).contains("outline-item-open")||t.add("outline-item-open"),d(),t.add("outline-item-active"))});function s(){var e=a();r=null;for(var t=0;t<i.length&&i[t][1]-e<60;t++)r=i[t]}function n(){c=setTimeout(function(){var n;i=[],n=a(),document.querySelector("#write").querySelectorAll("h1, h2, h3, h4, h5, h6").forEach(e=>{var t=e.getAttribute("id");i.push([t,n+e.getBoundingClientRect().y])}),s(),u()},300)}var l,c,d=function(){document.querySelectorAll(".outline-item-active").forEach(e=>e.classList.remove("outline-item-active")),document.querySelectorAll(".outline-item-single.outline-item-open").forEach(e=>e.classList.remove("outline-item-open"))},u=function(){if(r&&(d(),t=document.querySelector('.outline-label[href="#'+(CSS.escape?CSS.escape(r[0]):r[0])+'"]')))if(o){var e=t.closest(".outline-item-open>ul>.outline-item-wrapper");if(e)e.classList.add("outline-item-active");else{for(var t,n=(t=t.closest(".outline-item-wrapper")).parentElement.closest(".outline-item-wrapper");n;)n=(t=n).parentElement.closest(".outline-item-wrapper");t.classList.add("outline-item-active")}}else t.closest(".outline-item-wrapper").classList.add("outline-item-active")};window.addEventListener("scroll",function(e){l&&clearTimeout(l),l=setTimeout(function(){s(),u()},300)});window.addEventListener("resize",function(e){c&&clearTimeout(c),n()}),n()})();</script></body></html>