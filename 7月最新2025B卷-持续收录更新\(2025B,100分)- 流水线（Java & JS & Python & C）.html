<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(B卷,100分)- 流水线（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>一个工厂有m条流水线&#xff0c;来并行完成n个独立的作业&#xff0c;该工厂设置了一个调度系统&#xff0c;在安排作业时&#xff0c;总是优先执行处理时间最短的作业。</p> 
<p>现给定流水线个数m&#xff0c;需要完成的作业数n, 每个作业的处理时间分别为t1,t2…tn。请你编程计算处理完所有作业的耗时为多少&#xff1f;</p> 
<p>当n&gt;m时&#xff0c;首先处理时间短的m个作业进入流水线&#xff0c;其他的等待&#xff0c;当某个作业完成时&#xff0c;依次从剩余作业中取处理时间最短的进入处理。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>第一行为2个整数&#xff08;采用空格分隔&#xff09;&#xff0c;分别表示流水线个数m和作业数n&#xff1b;</p> 
<p>第二行输入n个整数&#xff08;采用空格分隔&#xff09;&#xff0c;表示每个作业的处理时长t1,t2…tn。</p> 
<p>0&lt; m,n&lt;100&#xff0c;0&lt;t1,t2…tn&lt;100。</p> 
<p>注&#xff1a;保证输入都是合法的。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>输出处理完所有作业的总时长。</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td>输入</td><td> <p>3 5<br /> 8 4 3 2 10</p> </td></tr><tr><td>输出</td><td>13</td></tr><tr><td>说明</td><td> <p>1、先安排时间为2、3、4的3个作业。</p> <p>2、第一条流水线先完成作业&#xff0c;然后调度剩余时间最短的作业8。</p> <p>3、第二条流水线完成作业&#xff0c;然后调度剩余时间最短的作业10。</p> <p>4、总工耗时就是第二条流水线完成作业的时间13&#xff08;3&#43;10&#xff09;。</p> </td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>简单的逻辑题。解题思路如下&#xff1a;</p> 
<p>题目说“总是优先执行处理时间最短的作业”&#xff0c;因此我们可以将8 4 3 2 10 进行升序排序变为2 3 4 8 10&#xff0c;然后依次将排序后元素投入对应流水线中&#xff0c;如下图所示</p> 
<p><img alt="" height="189" src="https://img-blog.csdnimg.cn/cb8a58e14b9b4d9db62131a2cbe18bb6.png" width="281" /></p> 
<p> 计算每条流水线的时间总和&#xff0c;最大的个就是题解。</p> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.Arrays;
import java.util.Scanner;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    int m &#61; sc.nextInt();
    int n &#61; sc.nextInt();

    int[] times &#61; new int[n];
    for (int i &#61; 0; i &lt; n; i&#43;&#43;) times[i] &#61; sc.nextInt();

    System.out.println(getResult(m, n, times));
  }

  public static int getResult(int m, int n, int[] times) {
    Arrays.sort(times);

    int[] mArr &#61; new int[m];
    for (int i &#61; 0; i &lt; n; i&#43;&#43;) {
      mArr[i % m] &#43;&#61; times[i];
    }

    return Arrays.stream(mArr).max().orElse(0);
  }
}
</code></pre> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JS算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const lines &#61; [];
rl.on(&#34;line&#34;, (line) &#61;&gt; {
  lines.push(line);

  if (lines.length &#61;&#61;&#61; 2) {
    let [m, n] &#61; lines[0].split(&#34; &#34;).map((ele) &#61;&gt; parseInt(ele));
    let times &#61; lines[1]
      .split(&#34; &#34;)
      .slice(0, n)
      .map((ele) &#61;&gt; parseInt(ele));

    times.sort((a, b) &#61;&gt; a - b);

    let mArr &#61; new Array(m).fill(0);

    times.forEach((time, idx) &#61;&gt; {
      mArr[idx % m] &#43;&#61; time;
    });

    console.log(Math.max(...mArr));

    lines.length &#61; 0;
  }
});
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
m, n &#61; map(int, input().split())
times &#61; list(map(int, input().split()))


# 算法入口
def getResult():
    times.sort()

    mArr &#61; [0]*m

    for i in range(len(times)):
        mArr[i % m] &#43;&#61; times[i]

    return max(mArr)


# 算法调用
print(getResult())
</code></pre> 
<p></p> 
<h4>C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;

#define MAX(a,b) ((a) &gt; (b) ? (a) : (b))

int cmp(const void* a, const void* b) {
    return (*(int*) a) - (*(int*) b);
}

int main() {
    int m, n;
    scanf(&#34;%d %d&#34;, &amp;m, &amp;n);

    int times[n];
    for(int i&#61;0; i&lt;n; i&#43;&#43;) {
        scanf(&#34;%d&#34;, &amp;times[i]);
    }

    qsort(times, n, sizeof(int), cmp);

    int* mArr &#61; (int*) calloc(m, sizeof(int));
    for(int i&#61;0; i&lt;n; i&#43;&#43;) {
        mArr[i % m] &#43;&#61; times[i];
    }

    int ans &#61; mArr[0];
    for(int i&#61;1; i&lt;m; i&#43;&#43;) {
        ans &#61; MAX(ans, mArr[i]);
    }

    printf(&#34;%d\n&#34;, ans);

    return 0;
}</code></pre> 
<p> </p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>