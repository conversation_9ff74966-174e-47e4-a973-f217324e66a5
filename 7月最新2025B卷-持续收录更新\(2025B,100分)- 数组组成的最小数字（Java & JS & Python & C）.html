<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,100分)- 数组组成的最小数字（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>给定一个整型数组&#xff0c;请从该数组中选择3个元素组成最小数字并输出</p> 
<p>&#xff08;如果数组长度小于3&#xff0c;则选择数组中所有元素来组成最小数字&#xff09;。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>一行用半角逗号分割的字符串记录的整型数组&#xff0c;0 &lt; 数组长度 &lt;&#61; 100&#xff0c;0 &lt; 整数的取值范围 &lt;&#61; 10000。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>由3个元素组成的最小数字&#xff0c;如果数组长度小于3&#xff0c;则选择数组中所有元素来组成最小数字。</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">21,30,62,5,31</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">21305</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">数组长度超过3&#xff0c;需要选3个元素组成最小数字&#xff0c;21305由21,30,5三个元素组成的数字&#xff0c;为所有组合中最小的数字。</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">5,21</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">215</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">数组长度小于3&#xff0c; 选择所有元素来组成最小值&#xff0c;215为最小值。</td></tr></tbody></table> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>此题可以使用暴力法&#xff0c;求n个数取3个全排列&#xff0c;也就是O(n^3)的时间复杂度&#xff0c;但是题目提示0 &lt; 数组长度 &lt;&#61; 100&#xff0c;这个数据规模很容易超时&#xff0c;因此我们应该想一想更优化的方法。</p> 
<p></p> 
<p>我们知道Array.prototype.sort默认排序是按照Unicode值从小到大排的&#xff0c;<s>因此对于只有两个数的情况&#xff0c;我们直接按照sort字典序升序&#xff0c;比如5,21&#xff0c;字典序升序后就是21,5&#xff0c;而215就是最小组合数</s>。</p> 
<blockquote> 
 <p>2023.02.03 这里直接对数组进行字典序升序&#xff0c;拼接后得到的组合数&#xff0c;不一定是最小的&#xff0c;比如数组 [3, 32, 321]&#xff0c;此时按照字典序升序后&#xff0c;还是 [3, 32, 321]&#xff0c;拼接出来为332321&#xff0c;而这显然不是最小的组合数&#xff0c;最小的组合数应该是321323。</p> 
 <p>此处&#xff0c;得到最小组合数的正确排序规则应该是&#xff1a;请看下面博客解析<a href="https://blog.csdn.net/qfc_128220/article/details/128612401" title="华为OD机试 - 组合出合法最小数_伏城之外的博客-CSDN博客">华为OD机试 - 组合出合法最小数_伏城之外的博客-CSDN博客</a></p> 
</blockquote> 
<p></p> 
<p>对于三个数及以上的数组&#xff0c;我们需要从中取出3个数&#xff0c;这个3个数&#xff0c;首先需要保证总长度最短&#xff0c;即保证组合数的位数最少&#xff0c;其值才能最小&#xff0c;因此我们需要将数组升序&#xff0c;这样小数在前&#xff0c;大数在后&#xff0c;我们只要取前三位即可&#xff0c;比如21,30,62,5,31升序为 5,21,30,31,62&#xff0c;取前3个&#xff0c;5,21,30然后进行sort默认排序&#xff0c;变为21&#xff0c;30&#xff0c;5&#xff0c;而21305就是最小值。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.on(&#34;line&#34;, (line) &#61;&gt; {
  const strs &#61; line.split(&#34;,&#34;);
  console.log(getResult(strs));
});

function getResult(strs) {
  strs.sort((a, b) &#61;&gt; a - b);

  return strs
    .slice(0, 3)
    .sort((a, b) &#61;&gt; {
      const s1 &#61; a &#43; b;
      const s2 &#61; b &#43; a;
      return s1 &#61;&#61; s2 ? 0 : s1 &gt; s2 ? 1 : -1;
    })
    .join(&#34;&#34;);
}
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.Arrays;
import java.util.Scanner;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    String[] strs &#61; sc.nextLine().split(&#34;,&#34;);
    System.out.println(getResult(strs));
  }

  public static String getResult(String[] strs) {
    Arrays.sort(strs, (a, b) -&gt; Integer.parseInt(a) - Integer.parseInt(b));

    String[] tmp &#61; Arrays.copyOfRange(strs, 0, Math.min(3, strs.length));
    Arrays.sort(tmp, (a, b) -&gt; (a &#43; b).compareTo(b &#43; a));

    StringBuilder sb &#61; new StringBuilder();
    for (String s : tmp) {
      sb.append(s);
    }

    return sb.toString();
  }
}
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python">import functools

# 输入获取
strs &#61; input().split(&#34;,&#34;)


# 算法入口
def cmp(a, b):
    s1 &#61; a &#43; b
    s2 &#61; b &#43; a
    return 0 if s1 &#61;&#61; s2 else 1 if s1 &gt; s2 else -1


def getResult(strs):
    strs.sort(key&#61;lambda x: int(x))
    tmp &#61; strs[:3]
    tmp.sort(key&#61;functools.cmp_to_key(cmp))
    return &#34;&#34;.join(tmp)


# 算法调用
print(getResult(strs))
</code></pre> 
<p> </p> 
<h4>C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;
#include &lt;string.h&gt;

#define MIN(a,b) ((a) &lt; (b) ? (a) : (b))

#define MAX_SIZE 100

int cmp1(const void* a, const void* b) {
    int A;
    sscanf(*(char**) a, &#34;%d&#34;, &amp;A);

    int B;
    sscanf(*(char**) b, &#34;%d&#34;, &amp;B);

    return A - B;
}

int cmp2(const void* a, const void* b) {
    char* A &#61; *((char**) a);
    char* B &#61; *((char**) b);

    char AB[10000] &#61; {&#39;\0&#39;};
    strcat(AB, A);
    strcat(AB, B);

    char BA[10000] &#61; {&#39;\0&#39;};
    strcat(BA, B);
    strcat(BA, A);

    return strcmp(AB, BA);
}

int main() {
    char line[10000];
    gets(line);

    char* ss[MAX_SIZE];
    int ss_size &#61; 0;

    char* token &#61; strtok(line, &#34;,&#34;);
    while(token !&#61; NULL) {
        ss[ss_size&#43;&#43;] &#61; token;
        token &#61; strtok(NULL, &#34;,&#34;);
    }

    qsort(ss, ss_size, sizeof(char*), cmp1);

    int size &#61; MIN(3, ss_size);
    qsort(ss, size, sizeof(char*), cmp2);

    char res[10000];
    for(int i&#61;0; i&lt;size; i&#43;&#43;) {
        strcat(res, ss[i]);
    }

    puts(res);

    return 0;
}</code></pre> 
<p> </p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>