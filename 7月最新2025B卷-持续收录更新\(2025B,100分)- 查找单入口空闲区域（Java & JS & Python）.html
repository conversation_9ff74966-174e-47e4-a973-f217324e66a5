<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(A卷,100分)- 查找单入口空闲区域（Java & JS & Python）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>给定一个 m x n 的矩阵&#xff0c;由若干字符 ‘X’ 和 ‘O’构成&#xff0c;’X’表示该处已被占据&#xff0c;’O’表示该处空闲&#xff0c;请找到最大的单入口空闲区域。</p> 
<p>解释&#xff1a;</p> 
<p>空闲区域是由连通的’O’组成的区域&#xff0c;位于边界的’O’可以构成入口&#xff0c;</p> 
<p>单入口空闲区域即有且只有一个位于边界的’O’作为入口的由连通的’O’组成的区域。<br /> 如果两个元素在水平或垂直方向相邻&#xff0c;则称它们是“连通”的。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>第一行输入为两个数字&#xff0c;第一个数字为行数m&#xff0c;第二个数字为列数n&#xff0c;两个数字以空格分隔&#xff0c;1&lt;&#61;m,n&lt;&#61;200。</p> 
<p>剩余各行为矩阵各行元素&#xff0c;元素为‘X’或‘O’&#xff0c;各元素间以空格分隔。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>若有唯一符合要求的最大单入口空闲区域&#xff0c;输出三个数字</p> 
<ul><li>第一个数字为入口行坐标&#xff08;0~m-1&#xff09;</li><li>第二个数字为入口列坐标&#xff08;0~n-1&#xff09;</li><li>第三个数字为区域大小</li></ul> 
<p>三个数字以空格分隔&#xff1b;</p> 
<p>若有多个符合要求&#xff0c;则输出区域大小最大的&#xff0c;若多个符合要求的单入口区域的区域大小相同&#xff0c;则此时只需要输出区域大小&#xff0c;不需要输出入口坐标。</p> 
<p>若没有&#xff0c;输出NULL。</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">4 4<br /> X X X X<br /> X O O X<br /> X O O X<br /> X O X X</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">3 1 5</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">存在最大单入口区域&#xff0c;入口坐标(3,1)&#xff0c;区域大小5</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:87px;">输入</td><td style="width:411px;">4 5<br /> X X X X X<br /> O O O O X<br /> X O O O X<br /> X O X X O</td></tr><tr><td style="width:87px;">输出</td><td style="width:411px;">3 4 1</td></tr><tr><td style="width:87px;">说明</td><td style="width:411px;">存在最大单入口区域&#xff0c;入口坐标&#xff08;3,4&#xff09;&#xff0c;区域大小1</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:84px;">输入</td><td style="width:414px;">5 4<br /> X X X X<br /> X O O O<br /> X O O O<br /> X O O X<br /> X X X X</td></tr><tr><td style="width:84px;">输出</td><td style="width:414px;">NULL</td></tr><tr><td style="width:84px;">说明</td><td style="width:414px;">不存在最大单入口区域</td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:84px;">输入</td><td style="width:414px;">5 4<br /> X X X X<br /> X O O O<br /> X X X X<br /> X O O O<br /> X X X X</td></tr><tr><td style="width:84px;">输出</td><td style="width:414px;">3</td></tr><tr><td style="width:84px;">说明</td><td style="width:414px;">存在两个大小为3的最大单入口区域&#xff0c;两个入口坐标分别为(1,3)、(3,3)</td></tr></tbody></table> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>本题可以使用深度优先搜索来解题。</p> 
<p>首先&#xff0c;我们可以遍历矩阵元素&#xff0c;当遍历到“O”时&#xff0c;已该“O”的坐标位置开始向其上、下、左、右方向开始深度优先搜索&#xff0c;每搜索到一个“O”&#xff0c;则该空闲区域数量&#43;1&#xff0c;如果搜索到的“O”的坐标位置处于矩阵第一列&#xff0c;或最后一列&#xff0c;或者第一行&#xff0c;或者最后一行&#xff0c;那么该“O”位置就是空闲区域的入口位置&#xff0c;我们将其缓存到out数组中。</p> 
<p>当所有深度优先搜索的分支都搜索完了&#xff0c;则判断out统计的入口数量&#xff0c;</p> 
<ol><li>如果只有1个&#xff0c;则该空闲区域是符合题意得单入口空闲区域&#xff0c;输出入口坐标位置&#xff0c;以及空闲区域数量。</li><li>如果有多个&#xff0c;则该区域不符合要求</li></ol> 
<p>另外&#xff0c;我们还需要定义一个check集合来缓存&#xff0c;已经被递归过的&#34;O&#34;位置&#xff0c;避免重复的深度优先搜索。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const lines &#61; [];
let n, m;
rl.on(&#34;line&#34;, (line) &#61;&gt; {
  lines.push(line);

  if (lines.length &#61;&#61;&#61; 1) {
    [n, m] &#61; lines[0].split(&#34; &#34;).map(Number);
  }

  if (n &amp;&amp; lines.length &#61;&#61;&#61; n &#43; 1) {
    lines.shift();
    const matrix &#61; lines.map((line) &#61;&gt; line.split(&#34; &#34;));
    console.log(getResult(matrix, n, m));
    lines.length &#61; 0;
  }
});

function getResult(matrix, n, m) {
  const checked &#61; new Set();

  const offset &#61; [
    [0, -1],
    [0, 1],
    [-1, 0],
    [1, 0],
  ];

  function dfs(i, j, count, out) {
    const pos &#61; &#96;${i}-${j}&#96;;

    if (
      i &lt; 0 ||
      i &gt;&#61; n ||
      j &lt; 0 ||
      j &gt;&#61; m ||
      matrix[i][j] &#61;&#61;&#61; &#34;X&#34; ||
      checked.has(pos)
    )
      return count;

    checked.add(pos);

    if (i &#61;&#61;&#61; 0 || i &#61;&#61;&#61; n - 1 || j &#61;&#61;&#61; 0 || j &#61;&#61;&#61; m - 1) out.push([i, j]);

    count&#43;&#43;;

    for (let k &#61; 0; k &lt; offset.length; k&#43;&#43;) {
      const [offsetX, offsetY] &#61; offset[k];
      const newI &#61; i &#43; offsetX;
      const newJ &#61; j &#43; offsetY;
      count &#61; dfs(newI, newJ, count, out);
    }

    return count;
  }

  const ans &#61; [];

  for (let i &#61; 0; i &lt; n; i&#43;&#43;) {
    for (let j &#61; 0; j &lt; m; j&#43;&#43;) {
      if (matrix[i][j] &#61;&#61;&#61; &#34;O&#34; &amp;&amp; !checked.has(&#96;${i}-${j}&#96;)) {
        const out &#61; [];
        const count &#61; dfs(i, j, 0, out);
        if (out.length &#61;&#61;&#61; 1) {
          ans.push([...out[0], count]);
        }
      }
    }
  }

  if (!ans.length) return &#34;NULL&#34;;

  ans.sort((a, b) &#61;&gt; b[2] - a[2]);

  if (ans.length &#61;&#61;&#61; 1 || ans[0][2] &gt; ans[1][2]) {
    return ans[0].join(&#34; &#34;);
  } else {
    return ans[0][2];
  }
}
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.*;

public class Main {
    static int n;
    static int m;
    static String[][] matrix;
    static int[][] offset &#61; {<!-- -->{0, -1}, {0, 1}, {-1, 0}, {1, 0}};
    static HashSet&lt;String&gt; checked &#61; new HashSet&lt;&gt;();

    public static void main(String[] args) {
        Scanner sc &#61; new Scanner(System.in);
        n &#61; sc.nextInt();
        m &#61; sc.nextInt();

        matrix &#61; new String[n][m];
        for (int i &#61; 0; i &lt; n; i&#43;&#43;) {
            for (int j &#61; 0; j &lt; m; j&#43;&#43;) {
                matrix[i][j] &#61; sc.next();
            }
        }

        System.out.println(getResult(matrix, n, m));
    }

    public static String getResult(String[][] matrix, int n, int m) {
        ArrayList&lt;Integer[]&gt; ans &#61; new ArrayList&lt;&gt;();

        for (int i &#61; 0; i &lt; n; i&#43;&#43;) {
            for (int j &#61; 0; j &lt; m; j&#43;&#43;) {
                if (&#34;O&#34;.equals(matrix[i][j]) &amp;&amp; !checked.contains(i &#43; &#34;-&#34; &#43; j)) {
                    ArrayList&lt;Integer[]&gt; enter &#61; new ArrayList&lt;&gt;();
                    int count &#61; dfs(i, j, 0, enter);
                    if (enter.size() &#61;&#61; 1) {
                        Integer[] pos &#61; enter.get(0);
                        Integer[] an &#61; {pos[0], pos[1], count};
                        ans.add(an);
                    }
                }
            }
        }

        if (ans.size() &#61;&#61; 0) return &#34;NULL&#34;;
        ans.sort((a, b) -&gt; b[2] - a[2]);

        if (ans.size() &#61;&#61; 1 || ans.get(0)[2] &gt; ans.get(1)[2]) {
            StringJoiner sj &#61; new StringJoiner(&#34; &#34;, &#34;&#34;, &#34;&#34;);
            for (Integer ele : ans.get(0)) {
                sj.add(ele &#43; &#34;&#34;);
            }
            return sj.toString();
        } else {
            return ans.get(0)[2] &#43; &#34;&#34;;
        }

    }

    public static int dfs(int i, int j, int count, ArrayList&lt;Integer[]&gt; enter) {
        String pos &#61; i &#43; &#34;-&#34; &#43; j;

        if (i &lt; 0 || i &gt;&#61; n || j &lt; 0 || j &gt;&#61; m || &#34;X&#34;.equals(matrix[i][j]) || checked.contains(pos)) {
            return count;
        }

        checked.add(pos);

        if (i &#61;&#61; 0 || i &#61;&#61; n - 1 || j &#61;&#61; 0 || j &#61;&#61; m - 1) enter.add(new Integer[]{i, j});

        count&#43;&#43;;

        for (int k &#61; 0; k &lt; offset.length; k&#43;&#43;) {
            int offsetX &#61; offset[k][0];
            int offsetY &#61; offset[k][1];

            int newI &#61; i &#43; offsetX;
            int newJ &#61; j &#43; offsetY;
            count &#61; dfs(newI, newJ, count, enter);
        }

        return count;
    }
}</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
m, n &#61; map(int, input().split())
matrix &#61; [input().split() for i in range(m)]


# 算法入口
def getResult(matrix, m, n):
    checked &#61; set()

    offsets &#61; ((0, -1), (0, 1), (-1, 0), (1, 0))

    def dfs(i, j, count, out):
        pos &#61; f&#34;{i}-{j}&#34;

        if i &lt; 0 or i &gt;&#61; m or j &lt; 0 or j &gt;&#61; n or matrix[i][j] &#61;&#61; &#34;X&#34; or pos in checked:
            return count

        checked.add(pos)

        if i &#61;&#61; 0 or i &#61;&#61; m - 1 or j &#61;&#61; 0 or j &#61;&#61; n - 1:
            out.append([i, j])

        count &#43;&#61; 1

        for offsetX, offsetY in offsets:
            newI &#61; i &#43; offsetX
            newJ &#61; j &#43; offsetY
            count &#61; dfs(newI, newJ, count, out)

        return count

    ans &#61; []

    for i in range(m):
        for j in range(n):
            if matrix[i][j] &#61;&#61; &#34;O&#34; and f&#34;{i}-{j}&#34; not in checked:
                out &#61; []
                count &#61; dfs(i, j, 0, out)
                if len(out) &#61;&#61; 1:
                    tmp &#61; out[0][:]
                    tmp.append(count)
                    ans.append(tmp)

    if len(ans) &#61;&#61; 0:
        return &#34;NULL&#34;

    ans.sort(key&#61;lambda x: -x[2])

    if len(ans) &#61;&#61; 1 or ans[0][2] &gt; ans[1][2]:
        return &#34; &#34;.join(map(str, ans[0]))
    else:
        return ans[0][2]


# 算法调用
print(getResult(matrix, m, n))
</code></pre>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>