<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,100分)- 游戏分组（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>部门准备举办一场王者荣耀表演赛&#xff0c;有 10 名游戏爱好者参与&#xff0c;分为两队&#xff0c;每队 5 人。</p> 
<p>每位参与者都有一个评分&#xff0c;代表着他的游戏水平。为了表演赛尽可能精彩&#xff0c;我们需要把 10 名参赛者分为示例尽量相近的两队。</p> 
<p>一队的实力可以表示为这一队 5 名队员的评分总和。</p> 
<p>现在给你 10 名参与者的游戏水平评分&#xff0c;请你根据上述要求分队&#xff0c;最后输出这两组的实力差绝对值。</p> 
<p>例&#xff1a;10 名参赛者的评分分别为&#xff1a;5 1 8 3 4 6 7 10 9 2&#xff0c;分组为&#xff08;1 3 5 8 10&#xff09;和&#xff08;2 4 6 7 9&#xff09;&#xff0c;两组实力差最小&#xff0c;差值为1。有多种分法&#xff0c;但是实力差的绝对值最小为1。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>10个整数&#xff0c;表示10名参与者的游戏水平评分。范围在 [1, 10000] 之间。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>1个整数&#xff0c;表示分组后两组实力差绝对值的最小值。</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">1 2 3 4 5 6 7 8 9 10</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">1</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">10名队员分为两组&#xff0c;两组实力差绝对值最小为1</td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>本题和<a href="https://blog.csdn.net/qfc_128220/article/details/127710678" title="华为OD机试 - 篮球比赛&#xff08;Java &amp; JS &amp; Python &amp; C&#xff09;-CSDN博客">华为OD机试 - 篮球比赛&#xff08;Java &amp; JS &amp; Python &amp; C&#xff09;-CSDN博客</a></p> 
<p>类似。具体解析请参考上面博客。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">const rl &#61; require(&#34;readline&#34;).createInterface({ input: process.stdin });
var iter &#61; rl[Symbol.asyncIterator]();
const readline &#61; async () &#61;&gt; (await iter.next()).value;

void (async function () {
  const arr &#61; (await readline()).split(&#34; &#34;).map(Number);

  arr.sort((a, b) &#61;&gt; a - b);

  const res &#61; [];
  // dfs求10选5的去重组合&#xff0c;并将组合之和记录进res中&#xff0c;即res中记录的是所有可能性的5人小队实力值之和
  dfs(arr, 0, 0, 0, res);

  const sum &#61; arr.reduce((p, c) &#61;&gt; p &#43; c);

  // 某队实力为subSum&#xff0c;则另一队实力为sum - subSum&#xff0c;则两队实力差为 abs((sum - subSum) - subSum)&#xff0c;先求最小实力差
  const ans &#61; res
    .map((subSum) &#61;&gt; Math.abs(sum - 2 * subSum))
    .sort((a, b) &#61;&gt; a - b)[0];

  console.log(ans);
})();

// 求解去重组合
function dfs(arr, index, level, sum, res) {
  if (level &#61;&#61;&#61; 5) {
    return res.push(sum);
  }

  for (let i &#61; index; i &lt; 10; i&#43;&#43;) {
    if (i &gt; index &amp;&amp; arr[i] &#61;&#61; arr[i - 1]) continue; // arr已经升序&#xff0c;这里进行树层去重
    dfs(arr, i &#43; 1, level &#43; 1, sum &#43; arr[i], res);
  }
}
</code></pre> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.ArrayList;
import java.util.Arrays;
import java.util.Scanner;
 
public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);
 
    int[] arr &#61; new int[10];
    for (int i &#61; 0; i &lt; 10; i&#43;&#43;) {
      arr[i] &#61; sc.nextInt();
    }
 
    System.out.println(getResult(arr));
  }
 
  public static int getResult(int[] arr) {
    Arrays.sort(arr);
 
    ArrayList&lt;Integer&gt; res &#61; new ArrayList&lt;&gt;();
    // dfs求10选5的去重组合&#xff0c;并将组合之和记录进res中&#xff0c;即res中记录的是所有可能性的5人小队实力值之和
    dfs(arr, 0, 0, 0, res);
 
    int sum &#61; Arrays.stream(arr).reduce(Integer::sum).orElse(0);
    // 某队实力为subSum&#xff0c;则另一队实力为sum - subSum&#xff0c;则两队实力差为 abs((sum - subSum) - subSum)&#xff0c;先求最小实力差
    return res.stream().map(subSum -&gt; Math.abs(sum - 2 * subSum)).min((a, b) -&gt; a - b).orElse(0);
  }
 
  // 求解去重组合
  public static void dfs(int[] arr, int index, int level, int sum, ArrayList&lt;Integer&gt; res) {
    if (level &#61;&#61; 5) {
      res.add(sum);
      return;
    }
 
    for (int i &#61; index; i &lt; 10; i&#43;&#43;) {
      if (i &gt; index &amp;&amp; arr[i] &#61;&#61; arr[i - 1]) continue; // arr已经升序&#xff0c;这里进行树层去重
      dfs(arr, i &#43; 1, level &#43; 1, sum &#43; arr[i], res);
    }
  }
}</code></pre> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
arr &#61; list(map(int, input().split()))
 
 
# 求解去重组合
def dfs(arr, index, level, sumV, res):
    if level &#61;&#61; 5:
        res.append(sumV)
        return
 
    for i in range(index, 10):
        if i &gt; index and arr[i] &#61;&#61; arr[i - 1]: # arr已经升序&#xff0c;这里进行树层去重
            continue
        dfs(arr, i &#43; 1, level &#43; 1, sumV &#43; arr[i], res)
 
 
# 算法入口
def getResult(arr):
    arr.sort()
 
    res &#61; []
    # dfs求10选5的去重组合&#xff0c;并将组合之和记录进res中&#xff0c;即res中记录的是所有可能性的5人小队实力值之和
    dfs(arr, 0, 0, 0, res)
 
    sumV &#61; sum(arr)
    #  某队实力为subSum&#xff0c;则另一队实力为sum - subSum&#xff0c;则两队实力差为 abs((sum - subSum) - subSum)&#xff0c;先求最小实力差
    return min(map(lambda subSum: abs(sumV - 2 * subSum), res))
 
 
# 算法调用
print(getResult(arr))</code></pre> 
<h4>C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;
#include &lt;limits.h&gt;
 
#define MIN(a,b) (a) &lt; (b) ? (a) : (b)
 
typedef struct ListNode {
    int ele;
    struct ListNode *next;
} ListNode;
 
typedef struct LinkedList {
    int size;
    ListNode *head;
    ListNode *tail;
} LinkedList;
 
LinkedList *new_LinkedList();
void addLast_LinkedList(LinkedList *link, int ele);
 
void dfs(int arr[], int index, int level, int sum, LinkedList *res);
int getResult(int arr[]);
 
int main() {
    int arr[10];
    for (int i &#61; 0; i &lt; 10; i&#43;&#43;) {
        scanf(&#34;%d&#34;, &amp;arr[i]);
    }
 
    printf(&#34;%d\n&#34;, getResult(arr));
 
    return 0;
}
 
int cmp(const void *a, const void *b) {
    return (*(int *) a) - (*(int *) b);
}
 
int getResult(int arr[]) {
    qsort(arr, 10, sizeof(int), cmp);
 
    LinkedList *res &#61; new_LinkedList();
    // dfs求10选5的去重组合&#xff0c;并将组合之和记录进res中&#xff0c;即res中记录的是所有可能性的5人小队实力值之和
    dfs(arr, 0, 0, 0, res);
 
    int sum &#61; 0;
    for (int i &#61; 0; i &lt; 10; i&#43;&#43;) sum &#43;&#61; arr[i];
 
    int ans &#61; INT_MAX;
 
    // 某队实力为subSum&#xff0c;则另一队实力为sum - subSum&#xff0c;则两队实力差为 abs((sum - subSum) - subSum)&#xff0c;先求最小实力差
    ListNode *cur &#61; res-&gt;head;
    while (cur !&#61; NULL) {
        ans &#61; MIN(ans, abs(sum - 2 * cur-&gt;ele));
        cur &#61; cur-&gt;next;
    }
 
    return ans;
}
 
// 求解去重组合
void dfs(int arr[], int index, int level, int sum, LinkedList *res) {
    if (level &#61;&#61; 5) {
        addLast_LinkedList(res, sum);
        return;
    }
 
    for (int i &#61; index; i &lt; 10; i&#43;&#43;) {
        if (i &gt; index &amp;&amp; arr[i] &#61;&#61; arr[i - 1]) continue; // arr已经升序&#xff0c;这里进行树层去重
        dfs(arr, i &#43; 1, level &#43; 1, sum &#43; arr[i], res);
    }
 
}
 
LinkedList *new_LinkedList() {
    LinkedList *link &#61; (LinkedList *) malloc(sizeof(LinkedList));
 
    link-&gt;size &#61; 0;
    link-&gt;head &#61; NULL;
    link-&gt;tail &#61; NULL;
 
    return link;
}
 
void addLast_LinkedList(LinkedList *link, int ele) {
    ListNode *node &#61; (ListNode *) malloc(sizeof(ListNode));
 
    node-&gt;ele &#61; ele;
    node-&gt;next &#61; NULL;
 
    if (link-&gt;size &#61;&#61; 0) {
        link-&gt;head &#61; node;
        link-&gt;tail &#61; node;
    } else {
        link-&gt;tail-&gt;next &#61; node;
        link-&gt;tail &#61; node;
    }
 
    link-&gt;size&#43;&#43;;
}</code></pre> 
<p></p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>