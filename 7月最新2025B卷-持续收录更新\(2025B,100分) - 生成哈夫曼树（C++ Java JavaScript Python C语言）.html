<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box"><div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
             
                <div id="content_views" class="markdown_views prism-atom-one-light">
                    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
                        <path stroke-linecap="round" d="M5,0 0,2.5 5,5z" id="raphael-marker-block" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path>
                    </svg>
                    <h2><a name="t0"></a><a id="OD_0"></a>最新华为OD机试</h2> 

<h2><a name="t1"></a><a id="_4"></a>题目描述</h2> 
<p>给定长度为 n nn 的无序的数字数组，每个数字代表二叉树的叶子节点的权值，数字数组的值均大于等于 1 11 。请完成一个函数，根据输入的数字数组，生成<a href="https://so.csdn.net/so/search?q=%E5%93%88%E5%A4%AB%E6%9B%BC%E6%A0%91&amp;spm=1001.2101.3001.7020" target="_blank" class="hl hl-1" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7020&quot;,&quot;dest&quot;:&quot;https://so.csdn.net/so/search?q=%E5%93%88%E5%A4%AB%E6%9B%BC%E6%A0%91&amp;spm=1001.2101.3001.7020&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;哈夫曼树\&quot;}&quot;}" data-tit="哈夫曼树" data-pretit="哈夫曼树">哈夫曼树</a>，并将哈夫曼树按照中序遍历输出。</p> 
<p>为了保证输出的<a href="https://so.csdn.net/so/search?q=%E4%BA%8C%E5%8F%89%E6%A0%91%E4%B8%AD%E5%BA%8F%E9%81%8D%E5%8E%86&amp;spm=1001.2101.3001.7020" target="_blank" class="hl hl-1" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7020&quot;,&quot;dest&quot;:&quot;https://so.csdn.net/so/search?q=%E4%BA%8C%E5%8F%89%E6%A0%91%E4%B8%AD%E5%BA%8F%E9%81%8D%E5%8E%86&amp;spm=1001.2101.3001.7020&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;二叉树中序遍历\&quot;}&quot;}" data-tit="二叉树中序遍历" data-pretit="二叉树中序遍历">二叉树中序遍历</a>结果统一，增加以下限制:又树节点中，左节点权值小于等于右节点权值，根节点权值为左右节点权值之和。当左右节点权值相同时，左子树高度高度小于等于右子树。</p> 
<p>注意: 所有用例保证有效，并能生成哈夫曼树提醒:哈夫曼树又称最优二叉树，是一种带权路径长度最短的一叉树。</p> 
<p>所谓树的带权路径长度，就是树中所有的叶结点的权值乘上其到根结点的路径长度(若根结点为 0 00 层，叶结点到根结点的路径长度为叶结点的层数)</p> 
<h2><a name="t2"></a><a id="_14"></a>输入描述</h2> 
<p>例如：由叶子节点 5 15 40 30 10 生成的最优二叉树如下图所示，该树的最短带权路径长度为 40 * 1 + 30 * 2 +5 * 4 + 10 * 4 = 205 。<br> <img src="https://i-blog.csdnimg.cn/blog_migrate/2b9bfd525085fc0fa50f901973abbd59.png" alt="image-20231218210700458"></p> 
<h2><a name="t3"></a><a id="_21"></a>输出描述</h2> 
<p>输出一个哈夫曼的中序遍历数组，数值间以空格分隔</p> 
<h2><a name="t4"></a><a id="1_25"></a>示例1</h2> 
<p>输入</p> 
<pre data-index="0" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">5
5 15 40 30 10
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<p>输出</p> 
<pre data-index="1" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">40 100 30 60 15 30 5 15 10
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<h2><a name="t5"></a><a id="_46"></a>解题思路</h2> 
<h3><a name="t6"></a><a id="_48"></a>模拟计算</h3> 
<p><strong>请结合上图阅读！</strong> 计算过程如下：</p> 
<ol><li>输入的5个数是：5, 15, 40, 30, 10。</li><li>将这些数作为节点值创建节点，并将节点添加到优先队列中。</li><li>构建哈夫曼树： 
  <ul><li>弹出两个最小的节点，值为5和10，合并为一个新节点值为15，将新节点添加回优先队列。</li><li>弹出两个最小的节点，值为15（新合成的）和15（原始的），合并为一个新节点值为30，将新节点添加回优先队列。</li><li>弹出两个最小的节点，值为30（新合成的）和30（原始的），合并为一个新节点值为60，将新节点添加回优先队列。</li><li>弹出两个最小的节点，值为40和60，合并为一个新节点值为100，将新节点添加回优先队列。</li><li>此时队列中只剩下一个节点，这就是树的根节点，值为100。</li></ul> </li><li>对哈夫曼树进行中序遍历： 
  <ul><li>访问左子树，值为40，它是一个叶子节点，输出40。</li><li>访问根节点，值为100，输出100。</li><li>访问右子树，值为60，它不是叶子节点，继续中序遍历： 
    <ul><li>访问左子树，值为30，它不是叶子节点，继续中序遍历： 
      <ul><li>访问左子树，值为15，它是一个叶子节点，输出15。</li><li>访问根节点，值为30，输出30。</li><li>访问右子树，值为15，它是一个叶子节点，输出15。</li></ul> </li><li>访问根节点，值为60，输出60。</li><li>访问右子树，值为30，它不是叶子节点，继续中序遍历： 
      <ul><li>访问左子树，值为10，它是一个叶子节点，输出10。</li><li>访问根节点，值为30，输出30。</li><li>右子树为空，无输出。</li></ul> </li></ul> </li></ul> </li><li>最终输出的结果是：40 100 15 30 60 10 30。</li></ol> 
<h3><a name="t7"></a><a id="_75"></a>思路</h3> 
<p>小根堆（最小堆）：实现一个小根堆，用于在构建哈夫曼树的过程中维护节点的顺序，确保每次都能从中取出权值最小的节点。</p> 
<p>贪心算法：构建哈夫曼树的过程本身是一个贪心算法的应用，每次选择两个权值最小的节点合并，以确保最终树的带权路径长度最短。</p> 
<p>DFS（深度优先搜索）：在进行中序遍历时，使用了递归方法。</p> 
<h2><a name="t8"></a><a id="Java_83"></a>Java</h2> 
<pre data-index="2" class="set-code-hide prettyprint"><code class="prism language-java has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token class-name">PriorityQueue</span></span><span class="token punctuation">;</span>
<span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token class-name">Comparator</span></span><span class="token punctuation">;</span>
<span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token class-name">Scanner</span></span><span class="token punctuation">;</span>
<span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token class-name">ArrayList</span></span><span class="token punctuation">;</span>

<span class="token comment">// 定义一个节点类来表示哈夫曼树中的节点</span>
<span class="token keyword">class</span> <span class="token class-name">Node</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> value<span class="token punctuation">;</span>       <span class="token comment">// 节点存储的权值</span>
    <span class="token class-name">Node</span> left<span class="token punctuation">;</span>       <span class="token comment">// 指向左子节点的引用</span>
    <span class="token class-name">Node</span> right<span class="token punctuation">;</span>      <span class="token comment">// 指向右子节点的引用</span>
    <span class="token keyword">int</span> height<span class="token punctuation">;</span>      <span class="token comment">// 节点的高度，用于处理相等权值的情况</span>

    <span class="token comment">// 构造函数</span>
    <span class="token keyword">public</span> <span class="token class-name">Node</span><span class="token punctuation">(</span><span class="token keyword">int</span> v<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        value <span class="token operator">=</span> v<span class="token punctuation">;</span>
        left <span class="token operator">=</span> <span class="token keyword">null</span><span class="token punctuation">;</span>
        right <span class="token operator">=</span> <span class="token keyword">null</span><span class="token punctuation">;</span>
        height <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 实现比较器，用于优先队列的比较逻辑</span>
<span class="token keyword">class</span> <span class="token class-name">Compare</span> <span class="token keyword">implements</span> <span class="token class-name">Comparator</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">Node</span><span class="token punctuation">&gt;</span></span> <span class="token punctuation">{<!-- --></span>
    <span class="token annotation punctuation">@Override</span>
    <span class="token keyword">public</span> <span class="token keyword">int</span> <span class="token function">compare</span><span class="token punctuation">(</span><span class="token class-name">Node</span> a<span class="token punctuation">,</span> <span class="token class-name">Node</span> b<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 首先比较节点的权值，若权值相等则比较高度</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>a<span class="token punctuation">.</span>value <span class="token operator">&gt;</span> b<span class="token punctuation">.</span>value<span class="token punctuation">)</span> <span class="token keyword">return</span> <span class="token number">1</span><span class="token punctuation">;</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>a<span class="token punctuation">.</span>value <span class="token operator">&lt;</span> b<span class="token punctuation">.</span>value<span class="token punctuation">)</span> <span class="token keyword">return</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">;</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>a<span class="token punctuation">.</span>height <span class="token operator">&gt;</span> b<span class="token punctuation">.</span>height<span class="token punctuation">)</span> <span class="token keyword">return</span> <span class="token number">1</span><span class="token punctuation">;</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>a<span class="token punctuation">.</span>height <span class="token operator">&lt;</span> b<span class="token punctuation">.</span>height<span class="token punctuation">)</span> <span class="token keyword">return</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">;</span>
        <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token keyword">public</span> <span class="token keyword">class</span> <span class="token class-name">Main</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 构建哈夫曼树的函数</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token class-name">Node</span> <span class="token function">buildHuffmanTree</span><span class="token punctuation">(</span><span class="token class-name">ArrayList</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">Integer</span><span class="token punctuation">&gt;</span></span> values<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token class-name">PriorityQueue</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">Node</span><span class="token punctuation">&gt;</span></span> pq <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">PriorityQueue</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token punctuation">&gt;</span></span><span class="token punctuation">(</span><span class="token keyword">new</span> <span class="token class-name">Compare</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 使用优先队列存储节点</span>
        <span class="token comment">// 为每个权值创建一个节点并添加到优先队列中</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> value <span class="token operator">:</span> values<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            pq<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span><span class="token keyword">new</span> <span class="token class-name">Node</span><span class="token punctuation">(</span>value<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// 当队列中至少有两个节点时，执行循环</span>
        <span class="token keyword">while</span> <span class="token punctuation">(</span>pq<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token class-name">Node</span> left <span class="token operator">=</span> pq<span class="token punctuation">.</span><span class="token function">poll</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 弹出最小的节点作为左子节点</span>
            <span class="token class-name">Node</span> right <span class="token operator">=</span> pq<span class="token punctuation">.</span><span class="token function">poll</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 弹出次小的节点作为右子节点</span>

            <span class="token comment">// 创建一个新节点，其权值为左右子节点的权值之和</span>
            <span class="token class-name">Node</span> parent <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Node</span><span class="token punctuation">(</span>left<span class="token punctuation">.</span>value <span class="token operator">+</span> right<span class="token punctuation">.</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token comment">// 确保左子节点权值不大于右子节点权值，若相等则比较高度</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>left<span class="token punctuation">.</span>value <span class="token operator">&gt;</span> right<span class="token punctuation">.</span>value <span class="token operator">||</span> <span class="token punctuation">(</span>left<span class="token punctuation">.</span>value <span class="token operator">==</span> right<span class="token punctuation">.</span>value <span class="token operator">&amp;&amp;</span> left<span class="token punctuation">.</span>height <span class="token operator">&gt;</span> right<span class="token punctuation">.</span>height<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token class-name">Node</span> temp <span class="token operator">=</span> left<span class="token punctuation">;</span>
                left <span class="token operator">=</span> right<span class="token punctuation">;</span>
                right <span class="token operator">=</span> temp<span class="token punctuation">;</span>  <span class="token comment">// 交换左右子节点</span>
            <span class="token punctuation">}</span>
            parent<span class="token punctuation">.</span>left <span class="token operator">=</span> left<span class="token punctuation">;</span>
            parent<span class="token punctuation">.</span>right <span class="token operator">=</span> right<span class="token punctuation">;</span>
            parent<span class="token punctuation">.</span>height <span class="token operator">=</span> <span class="token class-name">Math</span><span class="token punctuation">.</span><span class="token function">max</span><span class="token punctuation">(</span>left<span class="token punctuation">.</span>height<span class="token punctuation">,</span> right<span class="token punctuation">.</span>height<span class="token punctuation">)</span> <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">;</span>  <span class="token comment">// 计算新节点的高度</span>
            pq<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span>parent<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 将新节点加入优先队列</span>
        <span class="token punctuation">}</span>
        <span class="token comment">// 返回优先队列中剩余的最后一个节点，即哈夫曼树的根节点</span>
        <span class="token keyword">return</span> pq<span class="token punctuation">.</span><span class="token function">peek</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 中序遍历哈夫曼树，并将遍历结果保存为字符串</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">inorderTraversal</span><span class="token punctuation">(</span><span class="token class-name">Node</span> root<span class="token punctuation">,</span> <span class="token class-name">StringBuilder</span> result<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>root <span class="token operator">!=</span> <span class="token keyword">null</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token punctuation">.</span>left<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 遍历左子树</span>
            result<span class="token punctuation">.</span><span class="token function">append</span><span class="token punctuation">(</span>root<span class="token punctuation">.</span>value<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">append</span><span class="token punctuation">(</span><span class="token string">" "</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 访问当前节点</span>
            <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token punctuation">.</span>right<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 遍历右子树</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 主函数</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> args<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token class-name">Scanner</span> scanner <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Scanner</span><span class="token punctuation">(</span><span class="token class-name">System</span><span class="token punctuation">.</span>in<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">int</span> n <span class="token operator">=</span> scanner<span class="token punctuation">.</span><span class="token function">nextInt</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 读取节点数量</span>
        <span class="token class-name">ArrayList</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">Integer</span><span class="token punctuation">&gt;</span></span> values <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">ArrayList</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token punctuation">&gt;</span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 存储所有节点的权值</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            values<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span>scanner<span class="token punctuation">.</span><span class="token function">nextInt</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 读取权值</span>
        <span class="token punctuation">}</span>
        <span class="token class-name">Node</span> root <span class="token operator">=</span> <span class="token function">buildHuffmanTree</span><span class="token punctuation">(</span>values<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 构建哈夫曼树</span>
        <span class="token class-name">StringBuilder</span> result <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">StringBuilder</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 用于存储中序遍历结果</span>
        <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 执行中序遍历</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>result<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            result<span class="token punctuation">.</span><span class="token function">deleteCharAt</span><span class="token punctuation">(</span>result<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 移除最后的空格</span>
        <span class="token punctuation">}</span>
        <span class="token class-name">System</span><span class="token punctuation">.</span>out<span class="token punctuation">.</span><span class="token function">println</span><span class="token punctuation">(</span>result<span class="token punctuation">.</span><span class="token function">toString</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输出中序遍历结果</span>
        scanner<span class="token punctuation">.</span><span class="token function">close</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li><li style="color: rgb(153, 153, 153);">80</li><li style="color: rgb(153, 153, 153);">81</li><li style="color: rgb(153, 153, 153);">82</li><li style="color: rgb(153, 153, 153);">83</li><li style="color: rgb(153, 153, 153);">84</li><li style="color: rgb(153, 153, 153);">85</li><li style="color: rgb(153, 153, 153);">86</li><li style="color: rgb(153, 153, 153);">87</li><li style="color: rgb(153, 153, 153);">88</li><li style="color: rgb(153, 153, 153);">89</li><li style="color: rgb(153, 153, 153);">90</li><li style="color: rgb(153, 153, 153);">91</li><li style="color: rgb(153, 153, 153);">92</li><li style="color: rgb(153, 153, 153);">93</li></ul></pre> 
<h2><a name="t9"></a><a id="Python_181"></a>Python</h2> 
<pre data-index="3" class="set-code-hide prettyprint"><code class="prism language-python has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">import</span> heapq

<span class="token comment"># 定义Node类，用于构建哈夫曼树的节点</span>
<span class="token keyword">class</span> <span class="token class-name">Node</span><span class="token punctuation">:</span>
    <span class="token keyword">def</span> <span class="token function">__init__</span><span class="token punctuation">(</span>self<span class="token punctuation">,</span> value<span class="token punctuation">)</span><span class="token punctuation">:</span>
        self<span class="token punctuation">.</span>value <span class="token operator">=</span> value  <span class="token comment"># 节点存储的数值</span>
        self<span class="token punctuation">.</span>left <span class="token operator">=</span> <span class="token boolean">None</span>  <span class="token comment"># 节点的左子节点</span>
        self<span class="token punctuation">.</span>right <span class="token operator">=</span> <span class="token boolean">None</span>  <span class="token comment"># 节点的右子节点</span>
        self<span class="token punctuation">.</span>height <span class="token operator">=</span> <span class="token number">0</span>  <span class="token comment"># 节点的高度</span>

    <span class="token comment"># 重载小于操作符，用于优先队列中比较Node对象</span>
    <span class="token keyword">def</span> <span class="token function">__lt__</span><span class="token punctuation">(</span>self<span class="token punctuation">,</span> other<span class="token punctuation">)</span><span class="token punctuation">:</span>
        <span class="token comment"># 首先比较节点的权值，如果权值相同，则比较高度</span>
        <span class="token keyword">if</span> self<span class="token punctuation">.</span>value <span class="token operator">==</span> other<span class="token punctuation">.</span>value<span class="token punctuation">:</span>
            <span class="token keyword">return</span> self<span class="token punctuation">.</span>height <span class="token operator">&lt;</span> other<span class="token punctuation">.</span>height
        <span class="token keyword">return</span> self<span class="token punctuation">.</span>value <span class="token operator">&lt;</span> other<span class="token punctuation">.</span>value

<span class="token comment"># 构建哈夫曼树的函数</span>
<span class="token keyword">def</span> <span class="token function">build_huffman_tree</span><span class="token punctuation">(</span>values<span class="token punctuation">)</span><span class="token punctuation">:</span>
    pq <span class="token operator">=</span> <span class="token punctuation">[</span>Node<span class="token punctuation">(</span>value<span class="token punctuation">)</span> <span class="token keyword">for</span> value <span class="token keyword">in</span> values<span class="token punctuation">]</span>  <span class="token comment"># 创建Node对象列表</span>
    heapq<span class="token punctuation">.</span>heapify<span class="token punctuation">(</span>pq<span class="token punctuation">)</span>  <span class="token comment"># 将列表转换为最小堆</span>
    <span class="token keyword">while</span> <span class="token builtin">len</span><span class="token punctuation">(</span>pq<span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">1</span><span class="token punctuation">:</span>  <span class="token comment"># 当堆中有多于一个节点时</span>
        left <span class="token operator">=</span> heapq<span class="token punctuation">.</span>heappop<span class="token punctuation">(</span>pq<span class="token punctuation">)</span>  <span class="token comment"># 弹出两个数值最小的节点</span>
        right <span class="token operator">=</span> heapq<span class="token punctuation">.</span>heappop<span class="token punctuation">(</span>pq<span class="token punctuation">)</span>
        parent <span class="token operator">=</span> Node<span class="token punctuation">(</span>left<span class="token punctuation">.</span>value <span class="token operator">+</span> right<span class="token punctuation">.</span>value<span class="token punctuation">)</span>  <span class="token comment"># 创建新节点，其数值为两个子节点数值之和</span>
        parent<span class="token punctuation">.</span>left <span class="token operator">=</span> left  <span class="token comment"># 设置新节点的左子节点</span>
        parent<span class="token punctuation">.</span>right <span class="token operator">=</span> right  <span class="token comment"># 设置新节点的右子节点</span>
        parent<span class="token punctuation">.</span>height <span class="token operator">=</span> <span class="token builtin">max</span><span class="token punctuation">(</span>left<span class="token punctuation">.</span>height<span class="token punctuation">,</span> right<span class="token punctuation">.</span>height<span class="token punctuation">)</span> <span class="token operator">+</span> <span class="token number">1</span>  <span class="token comment"># 更新节点的高度</span>
        heapq<span class="token punctuation">.</span>heappush<span class="token punctuation">(</span>pq<span class="token punctuation">,</span> parent<span class="token punctuation">)</span>  <span class="token comment"># 将新节点加入堆中</span>
    <span class="token keyword">return</span> pq<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span>  <span class="token comment"># 返回堆中剩下的最后一个节点，即哈夫曼树的根节点</span>

<span class="token comment"># 中序遍历哈夫曼树的函数</span>
<span class="token keyword">def</span> <span class="token function">inorder_traversal</span><span class="token punctuation">(</span>root<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token keyword">if</span> root <span class="token keyword">is</span> <span class="token keyword">not</span> <span class="token boolean">None</span><span class="token punctuation">:</span>  <span class="token comment"># 如果当前节点不为空</span>
        <span class="token keyword">yield</span> <span class="token keyword">from</span> inorder_traversal<span class="token punctuation">(</span>root<span class="token punctuation">.</span>left<span class="token punctuation">)</span>  <span class="token comment"># 递归遍历左子树</span>
        <span class="token keyword">yield</span> root<span class="token punctuation">.</span>value  <span class="token comment"># 返回当前节点的值</span>
        <span class="token keyword">yield</span> <span class="token keyword">from</span> inorder_traversal<span class="token punctuation">(</span>root<span class="token punctuation">.</span>right<span class="token punctuation">)</span>  <span class="token comment"># 递归遍历右子树</span>
 
n <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span><span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment"># 从标准输入读取数字的个数</span>
values <span class="token operator">=</span> <span class="token builtin">list</span><span class="token punctuation">(</span><span class="token builtin">map</span><span class="token punctuation">(</span><span class="token builtin">int</span><span class="token punctuation">,</span> <span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span>split<span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment"># 从标准输入读取数字，并转换为整数列表</span>
root <span class="token operator">=</span> build_huffman_tree<span class="token punctuation">(</span>values<span class="token punctuation">)</span>  <span class="token comment"># 构建哈夫曼树，并获取根节点</span>
result <span class="token operator">=</span> <span class="token string">' '</span><span class="token punctuation">.</span>join<span class="token punctuation">(</span><span class="token builtin">map</span><span class="token punctuation">(</span><span class="token builtin">str</span><span class="token punctuation">,</span> inorder_traversal<span class="token punctuation">(</span>root<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment"># 对哈夫曼树进行中序遍历，并将结果转换为字符串</span>
<span class="token keyword">print</span><span class="token punctuation">(</span>result<span class="token punctuation">)</span>  <span class="token comment"># 打印中序遍历的结果</span>



<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li></ul></pre> 
<h2><a name="t10"></a><a id="JavaScript_232"></a>JavaScript</h2> 
<pre data-index="4" class="set-code-hide prettyprint"><code class="prism language-js has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">const</span> readline <span class="token operator">=</span> <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">'readline'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 创建命令行读取接口实例</span>
<span class="token keyword">const</span> rl <span class="token operator">=</span> readline<span class="token punctuation">.</span><span class="token function">createInterface</span><span class="token punctuation">(</span><span class="token punctuation">{<!-- --></span>
    <span class="token literal-property property">input</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdin<span class="token punctuation">,</span>  <span class="token comment">// 标准输入流</span>
    <span class="token literal-property property">output</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdout <span class="token comment">// 标准输出流</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 监听第一行输入事件，获取节点数量</span>
rl<span class="token punctuation">.</span><span class="token function">on</span><span class="token punctuation">(</span><span class="token string">'line'</span><span class="token punctuation">,</span> <span class="token punctuation">(</span><span class="token parameter">n</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 监听第二行输入事件，获取节点权值列表</span>
    rl<span class="token punctuation">.</span><span class="token function">on</span><span class="token punctuation">(</span><span class="token string">'line'</span><span class="token punctuation">,</span> <span class="token punctuation">(</span><span class="token parameter">line</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">const</span> values <span class="token operator">=</span> line<span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">' '</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">(</span>Number<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 将输入的行按空格分割，并将每个元素转换为数字</span>
        <span class="token keyword">const</span> root <span class="token operator">=</span> <span class="token function">buildHuffmanTree</span><span class="token punctuation">(</span>values<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 使用输入的值构建哈夫曼树，并获取根节点</span>
        <span class="token keyword">const</span> result <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 初始化中序遍历结果数组</span>
        <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 对哈夫曼树进行中序遍历</span>
        console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>result<span class="token punctuation">.</span><span class="token function">join</span><span class="token punctuation">(</span><span class="token string">' '</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 将中序遍历的结果数组转换为字符串并打印</span>
        rl<span class="token punctuation">.</span><span class="token function">close</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 关闭读取接口</span>
    <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 定义节点类，用于构建哈夫曼树</span>
<span class="token keyword">class</span> <span class="token class-name">Node</span> <span class="token punctuation">{<!-- --></span>
    <span class="token function">constructor</span><span class="token punctuation">(</span><span class="token parameter">value</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>value <span class="token operator">=</span> value<span class="token punctuation">;</span>  <span class="token comment">// 节点的值</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>left <span class="token operator">=</span> <span class="token keyword">null</span><span class="token punctuation">;</span>    <span class="token comment">// 节点的左子节点</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>right <span class="token operator">=</span> <span class="token keyword">null</span><span class="token punctuation">;</span>   <span class="token comment">// 节点的右子节点</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 定义最小优先队列类</span>
<span class="token keyword">class</span> <span class="token class-name">MinPriorityQueue</span> <span class="token punctuation">{<!-- --></span>
    <span class="token function">constructor</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>elements <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 存储队列元素的数组</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 入队操作</span>
    <span class="token function">enqueue</span><span class="token punctuation">(</span><span class="token parameter">element</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>elements<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>element<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 将新元素添加到数组末尾</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>elements<span class="token punctuation">.</span><span class="token function">sort</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter">a<span class="token punctuation">,</span> b</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> a<span class="token punctuation">.</span>value <span class="token operator">-</span> b<span class="token punctuation">.</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 对数组进行排序，确保最小元素在数组开头</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 出队操作</span>
    <span class="token function">dequeue</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token keyword">this</span><span class="token punctuation">.</span>elements<span class="token punctuation">.</span><span class="token function">shift</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 移除并返回数组第一个元素</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 检查队列是否为空</span>
    <span class="token function">isEmpty</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token keyword">this</span><span class="token punctuation">.</span>elements<span class="token punctuation">.</span>length <span class="token operator">===</span> <span class="token number">0</span><span class="token punctuation">;</span>  <span class="token comment">// 队列为空时数组长度为0</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 构建哈夫曼树的函数</span>
<span class="token keyword">function</span> <span class="token function">buildHuffmanTree</span><span class="token punctuation">(</span><span class="token parameter">values</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">const</span> pq <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">MinPriorityQueue</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 创建最小优先队列实例</span>
    values<span class="token punctuation">.</span><span class="token function">forEach</span><span class="token punctuation">(</span><span class="token parameter">value</span> <span class="token operator">=&gt;</span> pq<span class="token punctuation">.</span><span class="token function">enqueue</span><span class="token punctuation">(</span><span class="token keyword">new</span> <span class="token class-name">Node</span><span class="token punctuation">(</span>value<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 为每个权值创建一个节点并加入队列</span>

    <span class="token keyword">while</span> <span class="token punctuation">(</span>pq<span class="token punctuation">.</span>elements<span class="token punctuation">.</span>length <span class="token operator">&gt;</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">const</span> left <span class="token operator">=</span> pq<span class="token punctuation">.</span><span class="token function">dequeue</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 弹出最小的节点作为左子节点</span>
        <span class="token keyword">const</span> right <span class="token operator">=</span> pq<span class="token punctuation">.</span><span class="token function">dequeue</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 弹出次小的节点作为右子节点</span>

        <span class="token keyword">const</span> parent <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Node</span><span class="token punctuation">(</span>left<span class="token punctuation">.</span>value <span class="token operator">+</span> right<span class="token punctuation">.</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 创建新节点，其权值为左右子节点之和</span>
        parent<span class="token punctuation">.</span>left <span class="token operator">=</span> left<span class="token punctuation">;</span>  <span class="token comment">// 设置新节点的左子节点</span>
        parent<span class="token punctuation">.</span>right <span class="token operator">=</span> right<span class="token punctuation">;</span>  <span class="token comment">// 设置新节点的右子节点</span>
        pq<span class="token punctuation">.</span><span class="token function">enqueue</span><span class="token punctuation">(</span>parent<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 将新节点加入优先队列</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">return</span> pq<span class="token punctuation">.</span><span class="token function">dequeue</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 返回队列中的最后一个节点，即哈夫曼树的根节点</span>
<span class="token punctuation">}</span>

<span class="token comment">// 中序遍历函数</span>
<span class="token keyword">function</span> <span class="token function">inorderTraversal</span><span class="token punctuation">(</span><span class="token parameter">node<span class="token punctuation">,</span> result</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>node<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>node<span class="token punctuation">.</span>left<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 遍历左子树</span>
        result<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>node<span class="token punctuation">.</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 访问当前节点，并加入结果数组</span>
        <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>node<span class="token punctuation">.</span>right<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 遍历右子树</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li></ul></pre> 
<h2><a name="t11"></a><a id="C_317"></a>C++</h2> 
<pre data-index="5" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;iostream&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;vector&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;queue&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;functional&gt;</span></span>
using namespace std<span class="token punctuation">;</span>

<span class="token comment">// Node结构用于表示哈夫曼树中的节点</span>
<span class="token keyword">struct</span> <span class="token class-name">Node</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> value<span class="token punctuation">;</span>       <span class="token comment">// 节点存储的权值</span>
    Node<span class="token operator">*</span> left<span class="token punctuation">;</span>      <span class="token comment">// 指向左子节点的指针</span>
    Node<span class="token operator">*</span> right<span class="token punctuation">;</span>     <span class="token comment">// 指向右子节点的指针</span>
    <span class="token keyword">int</span> height<span class="token punctuation">;</span>      <span class="token comment">// 节点的高度，用于处理相等权值的情况</span>
    <span class="token function">Node</span><span class="token punctuation">(</span><span class="token keyword">int</span> v<span class="token punctuation">)</span> <span class="token operator">:</span> <span class="token function">value</span><span class="token punctuation">(</span>v<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">left</span><span class="token punctuation">(</span>nullptr<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">right</span><span class="token punctuation">(</span>nullptr<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">height</span><span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span><span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Compare结构用于定义优先队列的比较方式</span>
<span class="token keyword">struct</span> <span class="token class-name">Compare</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 重载()操作符，定义比较规则</span>
    bool <span class="token function">operator</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">(</span>Node<span class="token operator">*</span> a<span class="token punctuation">,</span> Node<span class="token operator">*</span> b<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 首先比较节点的权值，若权值相等则比较高度</span>
        <span class="token keyword">return</span> a<span class="token operator">-&gt;</span>value <span class="token operator">&gt;</span> b<span class="token operator">-&gt;</span>value <span class="token operator">||</span> <span class="token punctuation">(</span>a<span class="token operator">-&gt;</span>value <span class="token operator">==</span> b<span class="token operator">-&gt;</span>value <span class="token operator">&amp;&amp;</span> a<span class="token operator">-&gt;</span>height <span class="token operator">&gt;</span> b<span class="token operator">-&gt;</span>height<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// 构建哈夫曼树的函数</span>
Node<span class="token operator">*</span> <span class="token function">buildHuffmanTree</span><span class="token punctuation">(</span><span class="token keyword">const</span> vector<span class="token operator">&lt;</span><span class="token keyword">int</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> values<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    priority_queue<span class="token operator">&lt;</span>Node<span class="token operator">*</span><span class="token punctuation">,</span> vector<span class="token operator">&lt;</span>Node<span class="token operator">*</span><span class="token operator">&gt;</span><span class="token punctuation">,</span> Compare<span class="token operator">&gt;</span> pq<span class="token punctuation">;</span>  <span class="token comment">// 定义优先队列存储节点</span>
    <span class="token comment">// 遍历所有权值，为每个权值创建一个节点</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> value <span class="token operator">:</span> values<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        pq<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>new <span class="token function">Node</span><span class="token punctuation">(</span>value<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 当队列中至少有两个节点时，执行循环</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span>pq<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        Node<span class="token operator">*</span> left <span class="token operator">=</span> pq<span class="token punctuation">.</span><span class="token function">top</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> pq<span class="token punctuation">.</span><span class="token function">pop</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 弹出最小的节点作为左子节点</span>
        Node<span class="token operator">*</span> right <span class="token operator">=</span> pq<span class="token punctuation">.</span><span class="token function">top</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> pq<span class="token punctuation">.</span><span class="token function">pop</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 弹出次小的节点作为右子节点</span>

        <span class="token comment">// 创建一个新节点，其权值为左右子节点的权值之和</span>
        Node<span class="token operator">*</span> parent <span class="token operator">=</span> new <span class="token function">Node</span><span class="token punctuation">(</span>left<span class="token operator">-&gt;</span>value <span class="token operator">+</span> right<span class="token operator">-&gt;</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token comment">// 确保左子节点权值不大于右子节点权值，若相等则比较高度</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>left<span class="token operator">-&gt;</span>value <span class="token operator">&gt;</span> right<span class="token operator">-&gt;</span>value <span class="token operator">||</span> <span class="token punctuation">(</span>left<span class="token operator">-&gt;</span>value <span class="token operator">==</span> right<span class="token operator">-&gt;</span>value <span class="token operator">&amp;&amp;</span> left<span class="token operator">-&gt;</span>height <span class="token operator">&gt;</span> right<span class="token operator">-&gt;</span>height<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token function">swap</span><span class="token punctuation">(</span>left<span class="token punctuation">,</span> right<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 若需要，交换左右子节点</span>
        <span class="token punctuation">}</span>
        parent<span class="token operator">-&gt;</span>left <span class="token operator">=</span> left<span class="token punctuation">;</span>
        parent<span class="token operator">-&gt;</span>right <span class="token operator">=</span> right<span class="token punctuation">;</span>
        parent<span class="token operator">-&gt;</span>height <span class="token operator">=</span> <span class="token function">max</span><span class="token punctuation">(</span>left<span class="token operator">-&gt;</span>height<span class="token punctuation">,</span> right<span class="token operator">-&gt;</span>height<span class="token punctuation">)</span> <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">;</span>  <span class="token comment">// 计算新节点的高度</span>
        pq<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>parent<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 将新节点加入优先队列</span>
    <span class="token punctuation">}</span>
    <span class="token comment">// 返回优先队列中剩余的最后一个节点，即哈夫曼树的根节点</span>
    <span class="token keyword">return</span> pq<span class="token punctuation">.</span><span class="token function">top</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// 中序遍历哈夫曼树，并将遍历结果保存为字符串</span>
<span class="token keyword">void</span> <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>Node<span class="token operator">*</span> root<span class="token punctuation">,</span> string<span class="token operator">&amp;</span> result<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>root<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token operator">-&gt;</span>left<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 遍历左子树</span>
        result <span class="token operator">+=</span> <span class="token function">to_string</span><span class="token punctuation">(</span>root<span class="token operator">-&gt;</span>value<span class="token punctuation">)</span> <span class="token operator">+</span> <span class="token string">" "</span><span class="token punctuation">;</span>  <span class="token comment">// 访问当前节点</span>
        <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token operator">-&gt;</span>right<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 遍历右子树</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 主函数</span>
<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> n<span class="token punctuation">;</span>  <span class="token comment">// 存储节点数量</span>
    cin <span class="token operator">&gt;&gt;</span> n<span class="token punctuation">;</span>
    vector<span class="token operator">&lt;</span><span class="token keyword">int</span><span class="token operator">&gt;</span> <span class="token function">values</span><span class="token punctuation">(</span>n<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 存储所有节点的权值</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> <span class="token operator">++</span>i<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        cin <span class="token operator">&gt;&gt;</span> values<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 输入权值</span>
    <span class="token punctuation">}</span>
    Node<span class="token operator">*</span> root <span class="token operator">=</span> <span class="token function">buildHuffmanTree</span><span class="token punctuation">(</span>values<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 构建哈夫曼树</span>
    string result<span class="token punctuation">;</span>  <span class="token comment">// 用于存储中序遍历结果</span>
    <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token punctuation">,</span> result<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 执行中序遍历</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>result<span class="token punctuation">.</span><span class="token function">empty</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        result<span class="token punctuation">.</span><span class="token function">pop_back</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 移除最后的空格</span>
    <span class="token punctuation">}</span>
    cout <span class="token operator">&lt;&lt;</span> result <span class="token operator">&lt;&lt;</span> endl<span class="token punctuation">;</span>  <span class="token comment">// 输出中序遍历结果</span>
    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li></ul></pre> 
<h2><a name="t12"></a><a id="C_401"></a>C语言</h2> 
<pre data-index="6" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdio.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdlib.h&gt;</span></span>

<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">define</span> <span class="token macro-name">MAX_SIZE</span> <span class="token expression"><span class="token number">1000</span> </span><span class="token comment">// 定义最大节点数量，限制优先队列的容量</span></span>

<span class="token comment">// 哈夫曼树节点结构体定义</span>
<span class="token keyword">typedef</span> <span class="token keyword">struct</span> <span class="token class-name">Node</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> value<span class="token punctuation">;</span>           <span class="token comment">// 节点存储的权值</span>
    <span class="token keyword">struct</span> <span class="token class-name">Node</span> <span class="token operator">*</span>left<span class="token punctuation">;</span>   <span class="token comment">// 指向左子节点的指针</span>
    <span class="token keyword">struct</span> <span class="token class-name">Node</span> <span class="token operator">*</span>right<span class="token punctuation">;</span>  <span class="token comment">// 指向右子节点的指针</span>
    <span class="token keyword">int</span> height<span class="token punctuation">;</span>          <span class="token comment">// 节点的高度，用于处理权值相同的情况</span>
<span class="token punctuation">}</span> Node<span class="token punctuation">;</span>

<span class="token comment">// 优先队列结构体定义</span>
<span class="token keyword">typedef</span> <span class="token keyword">struct</span> <span class="token punctuation">{<!-- --></span>
    Node <span class="token operator">*</span>data<span class="token punctuation">[</span>MAX_SIZE<span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 存储队列元素的数组</span>
    <span class="token keyword">int</span> size<span class="token punctuation">;</span>              <span class="token comment">// 队列当前元素个数</span>
<span class="token punctuation">}</span> PriorityQueue<span class="token punctuation">;</span>

<span class="token comment">// 初始化优先队列</span>
<span class="token keyword">void</span> <span class="token function">initPriorityQueue</span><span class="token punctuation">(</span>PriorityQueue <span class="token operator">*</span>pq<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    pq<span class="token operator">-&gt;</span>size <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// 创建新节点</span>
Node <span class="token operator">*</span><span class="token function">createNode</span><span class="token punctuation">(</span><span class="token keyword">int</span> value<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    Node <span class="token operator">*</span>node <span class="token operator">=</span> <span class="token punctuation">(</span>Node <span class="token operator">*</span><span class="token punctuation">)</span><span class="token function">malloc</span><span class="token punctuation">(</span><span class="token keyword">sizeof</span><span class="token punctuation">(</span>Node<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    node<span class="token operator">-&gt;</span>value <span class="token operator">=</span> value<span class="token punctuation">;</span>
    node<span class="token operator">-&gt;</span>left <span class="token operator">=</span> <span class="token constant">NULL</span><span class="token punctuation">;</span>
    node<span class="token operator">-&gt;</span>right <span class="token operator">=</span> <span class="token constant">NULL</span><span class="token punctuation">;</span>
    node<span class="token operator">-&gt;</span>height <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
    <span class="token keyword">return</span> node<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// 向优先队列添加元素，维持最小堆性质</span>
<span class="token keyword">void</span> <span class="token function">push</span><span class="token punctuation">(</span>PriorityQueue <span class="token operator">*</span>pq<span class="token punctuation">,</span> Node <span class="token operator">*</span>node<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> i <span class="token operator">=</span> pq<span class="token operator">-&gt;</span>size<span class="token operator">++</span><span class="token punctuation">;</span> <span class="token comment">// 插入新元素的位置</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span>i <span class="token operator">&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">int</span> parent <span class="token operator">=</span> <span class="token punctuation">(</span>i <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">/</span> <span class="token number">2</span><span class="token punctuation">;</span> <span class="token comment">// 计算父节点的位置</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>parent<span class="token punctuation">]</span><span class="token operator">-&gt;</span>value <span class="token operator">&lt;</span> node<span class="token operator">-&gt;</span>value <span class="token operator">||</span>
            <span class="token punctuation">(</span>pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>parent<span class="token punctuation">]</span><span class="token operator">-&gt;</span>value <span class="token operator">==</span> node<span class="token operator">-&gt;</span>value <span class="token operator">&amp;&amp;</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>parent<span class="token punctuation">]</span><span class="token operator">-&gt;</span>height <span class="token operator">&lt;=</span> node<span class="token operator">-&gt;</span>height<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">break</span><span class="token punctuation">;</span> <span class="token comment">// 如果当前节点大于父节点，或权值相等但高度不低于父节点，停止调整</span>
        <span class="token punctuation">}</span>
        pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>parent<span class="token punctuation">]</span><span class="token punctuation">;</span> <span class="token comment">// 否则，将父节点下移</span>
        i <span class="token operator">=</span> parent<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> node<span class="token punctuation">;</span> <span class="token comment">// 插入新节点</span>
<span class="token punctuation">}</span>

<span class="token comment">// 从优先队列中弹出最小元素</span>
Node <span class="token operator">*</span><span class="token function">pop</span><span class="token punctuation">(</span>PriorityQueue <span class="token operator">*</span>pq<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    Node <span class="token operator">*</span>min <span class="token operator">=</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    Node <span class="token operator">*</span>last <span class="token operator">=</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span><span class="token operator">--</span>pq<span class="token operator">-&gt;</span>size<span class="token punctuation">]</span><span class="token punctuation">;</span>
    <span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span>i <span class="token operator">*</span> <span class="token number">2</span> <span class="token operator">+</span> <span class="token number">1</span> <span class="token operator">&lt;</span> pq<span class="token operator">-&gt;</span>size<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">int</span> left <span class="token operator">=</span> i <span class="token operator">*</span> <span class="token number">2</span> <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">;</span>
        <span class="token keyword">int</span> right <span class="token operator">=</span> i <span class="token operator">*</span> <span class="token number">2</span> <span class="token operator">+</span> <span class="token number">2</span><span class="token punctuation">;</span>
        <span class="token keyword">int</span> smallest <span class="token operator">=</span> left<span class="token punctuation">;</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>right <span class="token operator">&lt;</span> pq<span class="token operator">-&gt;</span>size <span class="token operator">&amp;&amp;</span> <span class="token punctuation">(</span>pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>right<span class="token punctuation">]</span><span class="token operator">-&gt;</span>value <span class="token operator">&lt;</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>left<span class="token punctuation">]</span><span class="token operator">-&gt;</span>value <span class="token operator">||</span>
                                 <span class="token punctuation">(</span>pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>right<span class="token punctuation">]</span><span class="token operator">-&gt;</span>value <span class="token operator">==</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>left<span class="token punctuation">]</span><span class="token operator">-&gt;</span>value <span class="token operator">&amp;&amp;</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>right<span class="token punctuation">]</span><span class="token operator">-&gt;</span>height <span class="token operator">&lt;</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>left<span class="token punctuation">]</span><span class="token operator">-&gt;</span>height<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            smallest <span class="token operator">=</span> right<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>last<span class="token operator">-&gt;</span>value <span class="token operator">&lt;</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>smallest<span class="token punctuation">]</span><span class="token operator">-&gt;</span>value <span class="token operator">||</span>
            <span class="token punctuation">(</span>last<span class="token operator">-&gt;</span>value <span class="token operator">==</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>smallest<span class="token punctuation">]</span><span class="token operator">-&gt;</span>value <span class="token operator">&amp;&amp;</span> last<span class="token operator">-&gt;</span>height <span class="token operator">&lt;=</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>smallest<span class="token punctuation">]</span><span class="token operator">-&gt;</span>height<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">break</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
        pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>smallest<span class="token punctuation">]</span><span class="token punctuation">;</span>
        i <span class="token operator">=</span> smallest<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    pq<span class="token operator">-&gt;</span>data<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> last<span class="token punctuation">;</span>
    <span class="token keyword">return</span> min<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// 构建哈夫曼树</span>
Node <span class="token operator">*</span><span class="token function">buildHuffmanTree</span><span class="token punctuation">(</span><span class="token keyword">int</span> values<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token keyword">int</span> n<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    PriorityQueue pq<span class="token punctuation">;</span>
    <span class="token function">initPriorityQueue</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>pq<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token function">push</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>pq<span class="token punctuation">,</span> <span class="token function">createNode</span><span class="token punctuation">(</span>values<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span>pq<span class="token punctuation">.</span>size <span class="token operator">&gt;</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        Node <span class="token operator">*</span>left <span class="token operator">=</span> <span class="token function">pop</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>pq<span class="token punctuation">)</span><span class="token punctuation">;</span>
        Node <span class="token operator">*</span>right <span class="token operator">=</span> <span class="token function">pop</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>pq<span class="token punctuation">)</span><span class="token punctuation">;</span>
        Node <span class="token operator">*</span>parent <span class="token operator">=</span> <span class="token function">createNode</span><span class="token punctuation">(</span>left<span class="token operator">-&gt;</span>value <span class="token operator">+</span> right<span class="token operator">-&gt;</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>left<span class="token operator">-&gt;</span>value <span class="token operator">&gt;</span> right<span class="token operator">-&gt;</span>value <span class="token operator">||</span> <span class="token punctuation">(</span>left<span class="token operator">-&gt;</span>value <span class="token operator">==</span> right<span class="token operator">-&gt;</span>value <span class="token operator">&amp;&amp;</span> left<span class="token operator">-&gt;</span>height <span class="token operator">&gt;</span> right<span class="token operator">-&gt;</span>height<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            Node <span class="token operator">*</span>temp <span class="token operator">=</span> left<span class="token punctuation">;</span>
            left <span class="token operator">=</span> right<span class="token punctuation">;</span>
            right <span class="token operator">=</span> temp<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
        parent<span class="token operator">-&gt;</span>left <span class="token operator">=</span> left<span class="token punctuation">;</span>
        parent<span class="token operator">-&gt;</span>right <span class="token operator">=</span> right<span class="token punctuation">;</span>
        parent<span class="token operator">-&gt;</span>height <span class="token operator">=</span> <span class="token punctuation">(</span>left<span class="token operator">-&gt;</span>height <span class="token operator">&gt;</span> right<span class="token operator">-&gt;</span>height <span class="token operator">?</span> left<span class="token operator">-&gt;</span>height <span class="token operator">:</span> right<span class="token operator">-&gt;</span>height<span class="token punctuation">)</span> <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">;</span>
        <span class="token function">push</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>pq<span class="token punctuation">,</span> parent<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">return</span> <span class="token function">pop</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>pq<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// 中序遍历哈夫曼树</span>
<span class="token keyword">void</span> <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>Node <span class="token operator">*</span>root<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>root <span class="token operator">!=</span> <span class="token constant">NULL</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token operator">-&gt;</span>left<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">printf</span><span class="token punctuation">(</span><span class="token string">"%d "</span><span class="token punctuation">,</span> root<span class="token operator">-&gt;</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token operator">-&gt;</span>right<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 主函数</span>
<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> n<span class="token punctuation">,</span> values<span class="token punctuation">[</span>MAX_SIZE<span class="token punctuation">]</span><span class="token punctuation">;</span>
    <span class="token function">scanf</span><span class="token punctuation">(</span><span class="token string">"%d"</span><span class="token punctuation">,</span> <span class="token operator">&amp;</span>n<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token function">scanf</span><span class="token punctuation">(</span><span class="token string">"%d"</span><span class="token punctuation">,</span> <span class="token operator">&amp;</span>values<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    Node <span class="token operator">*</span>root <span class="token operator">=</span> <span class="token function">buildHuffmanTree</span><span class="token punctuation">(</span>values<span class="token punctuation">,</span> n<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token function">inorderTraversal</span><span class="token punctuation">(</span>root<span class="token punctuation">)</span><span class="token punctuation">;</span>
   
    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li><li style="color: rgb(153, 153, 153);">80</li><li style="color: rgb(153, 153, 153);">81</li><li style="color: rgb(153, 153, 153);">82</li><li style="color: rgb(153, 153, 153);">83</li><li style="color: rgb(153, 153, 153);">84</li><li style="color: rgb(153, 153, 153);">85</li><li style="color: rgb(153, 153, 153);">86</li><li style="color: rgb(153, 153, 153);">87</li><li style="color: rgb(153, 153, 153);">88</li><li style="color: rgb(153, 153, 153);">89</li><li style="color: rgb(153, 153, 153);">90</li><li style="color: rgb(153, 153, 153);">91</li><li style="color: rgb(153, 153, 153);">92</li><li style="color: rgb(153, 153, 153);">93</li><li style="color: rgb(153, 153, 153);">94</li><li style="color: rgb(153, 153, 153);">95</li><li style="color: rgb(153, 153, 153);">96</li><li style="color: rgb(153, 153, 153);">97</li><li style="color: rgb(153, 153, 153);">98</li><li style="color: rgb(153, 153, 153);">99</li><li style="color: rgb(153, 153, 153);">100</li><li style="color: rgb(153, 153, 153);">101</li><li style="color: rgb(153, 153, 153);">102</li><li style="color: rgb(153, 153, 153);">103</li><li style="color: rgb(153, 153, 153);">104</li><li style="color: rgb(153, 153, 153);">105</li><li style="color: rgb(153, 153, 153);">106</li><li style="color: rgb(153, 153, 153);">107</li><li style="color: rgb(153, 153, 153);">108</li><li style="color: rgb(153, 153, 153);">109</li><li style="color: rgb(153, 153, 153);">110</li><li style="color: rgb(153, 153, 153);">111</li><li style="color: rgb(153, 153, 153);">112</li><li style="color: rgb(153, 153, 153);">113</li><li style="color: rgb(153, 153, 153);">114</li><li style="color: rgb(153, 153, 153);">115</li><li style="color: rgb(153, 153, 153);">116</li><li style="color: rgb(153, 153, 153);">117</li><li style="color: rgb(153, 153, 153);">118</li><li style="color: rgb(153, 153, 153);">119</li></ul></pre> 

                </div><div data-report-view="{&quot;mod&quot;:&quot;1585297308_001&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6548&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/banxia_frontend/article/details/141950914&quot;,&quot;extend1&quot;:&quot;pc&quot;,&quot;ab&quot;:&quot;new&quot;}"><div></div></div>
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/markdown_views-f23dff6052.css" rel="stylesheet">
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/style-c216769e99.css" rel="stylesheet">
        </div></html>