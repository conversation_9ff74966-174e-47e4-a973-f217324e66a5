<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box"><div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-704d5b9767.css">
             
                <div id="content_views" class="markdown_views prism-atom-one-light">
                    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
                        <path stroke-linecap="round" d="M5,0 0,2.5 5,5z" id="raphael-marker-block" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path>
                    </svg>
                    <h2><a name="t0"></a><a id="OD_0"></a>最新华为OD机试</h2> 

<h2><a name="t1"></a><a id="_4"></a>题目描述</h2> 
<p>记账本上记录了若干条多国货币金额，需要转换成人民币分（fen），汇总后输出。<br> 每行记录一条金额，金额带有货币单位，格式为数字+单位，可能是单独元，或者单独分，或者元与分的组合。<br> 要求将这些货币全部换算成人民币分（fen）后进行汇总，汇总结果仅保留整数，小数部分舍弃。<br> 元和分的换算关系都是1:100，如下：</p> 
<ul><li>1CNY=100fen（1元=100分）</li><li>1HKD=100cents（1港元=100港分）</li><li>1JPY=100sen（1日元=100仙）</li><li>1EUR=100eurocents（1欧元=100欧分）</li><li>1GBP=100pence（1英镑=100便士）</li></ul> 
<p>汇率表如下：</p> 
<p>即：100CNY = 1825JPY = 123HKD = 14EUR = 12GBP</p> 
<div class="table-box"><table><thead><tr><th>CNY</th><th>JPY</th><th>HKD</th><th>EUR</th><th>GBP</th></tr></thead><tbody><tr><td>100</td><td>1825</td><td>123</td><td>14</td><td>12</td></tr></tbody></table></div> 
<h2><a name="t2"></a><a id="_27"></a>输入描述</h2> 
<p>第一行输入为N，N表示记录数。0&lt;N&lt;100</p> 
<p>之后N行，每行表示一条货币记录，且该行只会是一种货币。</p> 
<h2><a name="t3"></a><a id="_36"></a>输出描述</h2> 
<p>将每行货币转换成人民币分（fen）后汇总求和，只保留整数部分。<br> 输出格式只有整数数字，不带小数，不带单位。</p> 
<h2><a name="t4"></a><a id="1_41"></a>示例1</h2> 
<p>输入</p> 
<pre data-index="0" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">1
100CNY
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<p>输出</p> 
<pre data-index="1" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">10000
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<blockquote> 
 <p>100CNY转换后是10000fen，所以输出结果为10000</p> 
</blockquote> 
<h2><a name="t5"></a><a id="2_60"></a>示例2</h2> 
<p>输入</p> 
<pre data-index="2" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">1
3000fen
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<p>输出</p> 
<pre data-index="3" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">3000
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<blockquote> 
 <p>3000fen，结果就是3000</p> 
</blockquote> 
<h2><a name="t6"></a><a id="3_79"></a>示例3</h2> 
<p>输入</p> 
<pre data-index="4" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">1
123HKD
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<p>输出</p> 
<pre data-index="5" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">10000
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<blockquote> 
 <p>HKD与CNY的汇率关系是123:100，所以换算后，输出结果为10000</p> 
</blockquote> 
<h2><a name="t7"></a><a id="4_98"></a>示例4</h2> 
<p>输入</p> 
<pre data-index="6" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">2
20CNY53fen
53HKD87cents
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li></ul></pre> 
<p>输出</p> 
<pre data-index="7" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">6432
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<blockquote> 
 <p>20元53分 + 53港元87港分，换算成人民币分后汇总，为6432</p> 
</blockquote> 
<h2><a name="t8"></a><a id="_118"></a>解题思路</h2> 
<p>这题主要是拆解<a href="https://so.csdn.net/so/search?q=%E5%AD%97%E7%AC%A6%E4%B8%B2&amp;spm=1001.2101.3001.7020" target="_blank" class="hl hl-1" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7020&quot;,&quot;dest&quot;:&quot;https://so.csdn.net/so/search?q=%E5%AD%97%E7%AC%A6%E4%B8%B2&amp;spm=1001.2101.3001.7020&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;字符串\&quot;}&quot;}" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7020&quot;,&quot;dest&quot;:&quot;https://so.csdn.net/so/search?q=%E5%AD%97%E7%AC%A6%E4%B8%B2&amp;spm=1001.2101.3001.7020&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;字符串\&quot;}&quot;}" data-tit="字符串" data-pretit="字符串">字符串</a>，题目本身不难，按照题意写好分支判断即可。如果觉得代码复杂，可以考虑使用正则表达式来做。</p> 
<h2><a name="t9"></a><a id="Java_122"></a>Java</h2> 
<pre data-index="8" class="set-code-hide prettyprint"><code class="prism language-java has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token class-name">Scanner</span></span><span class="token punctuation">;</span>

<span class="token keyword">public</span> <span class="token keyword">class</span> <span class="token class-name">CurrencyConverter</span> <span class="token punctuation">{<!-- --></span>

    <span class="token comment">// 方法：根据货币单位返回其转换为人民币分的汇率</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">double</span> <span class="token function">exChange</span><span class="token punctuation">(</span><span class="token class-name">String</span> unit<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">switch</span> <span class="token punctuation">(</span>unit<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">case</span> <span class="token string">"CNY"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span><span class="token punctuation">;</span> <span class="token comment">// 人民币</span>
            <span class="token keyword">case</span> <span class="token string">"JPY"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 日元</span>
            <span class="token keyword">case</span> <span class="token string">"HKD"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 港元</span>
            <span class="token keyword">case</span> <span class="token string">"EUR"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 欧元</span>
            <span class="token keyword">case</span> <span class="token string">"GBP"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 英镑</span>
            <span class="token keyword">case</span> <span class="token string">"fen"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">1.0</span><span class="token punctuation">;</span> <span class="token comment">// 人民币分</span>
            <span class="token keyword">case</span> <span class="token string">"cents"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span><span class="token punctuation">;</span> <span class="token comment">// 港元分</span>
            <span class="token keyword">case</span> <span class="token string">"sen"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span><span class="token punctuation">;</span> <span class="token comment">// 日元分</span>
            <span class="token keyword">case</span> <span class="token string">"eurocents"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span><span class="token punctuation">;</span> <span class="token comment">// 欧元分</span>
            <span class="token keyword">case</span> <span class="token string">"pence"</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span><span class="token punctuation">;</span> <span class="token comment">// 英镑分</span>
            <span class="token keyword">default</span><span class="token operator">:</span>
                <span class="token keyword">return</span> <span class="token number">0.0</span><span class="token punctuation">;</span> <span class="token comment">// 无效单位返回0</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
 
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> args<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token class-name">Scanner</span> scanner <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Scanner</span><span class="token punctuation">(</span><span class="token class-name">System</span><span class="token punctuation">.</span>in<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 创建Scanner对象用于读取输入</span>
        <span class="token keyword">int</span> n <span class="token operator">=</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token function">parseInt</span><span class="token punctuation">(</span>scanner<span class="token punctuation">.</span><span class="token function">nextLine</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取记录数</span>
       
        
        <span class="token keyword">double</span> totalFen <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 汇总结果</span>

        <span class="token comment">// 处理每一条货币记录</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token class-name">String</span> record <span class="token operator">=</span> scanner<span class="token punctuation">.</span><span class="token function">nextLine</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取每一行的记录</span>
            <span class="token keyword">int</span> amount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 用于保存金额</span>
            <span class="token class-name">StringBuilder</span> unit <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">StringBuilder</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 保存单位</span>

            <span class="token comment">// 遍历当前行，逐个提取金额和单位</span>
            <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> j <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> j <span class="token operator">&lt;</span> record<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> j<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token keyword">char</span> c <span class="token operator">=</span> record<span class="token punctuation">.</span><span class="token function">charAt</span><span class="token punctuation">(</span>j<span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token class-name">Character</span><span class="token punctuation">.</span><span class="token function">isDigit</span><span class="token punctuation">(</span>c<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                    amount <span class="token operator">=</span> amount <span class="token operator">*</span> <span class="token number">10</span> <span class="token operator">+</span> <span class="token punctuation">(</span>c <span class="token operator">-</span> <span class="token char">'0'</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 构建数字</span>
                <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{<!-- --></span>
                    unit<span class="token punctuation">.</span><span class="token function">append</span><span class="token punctuation">(</span>c<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 构建货币单位</span>
                <span class="token punctuation">}</span>

                <span class="token comment">// 当遇到完整的金额+单位时进行换算</span>
                <span class="token keyword">if</span> <span class="token punctuation">(</span>j <span class="token operator">==</span> record<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">||</span> <span class="token class-name">Character</span><span class="token punctuation">.</span><span class="token function">isDigit</span><span class="token punctuation">(</span>record<span class="token punctuation">.</span><span class="token function">charAt</span><span class="token punctuation">(</span>j <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> unit<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                    totalFen <span class="token operator">+=</span> amount <span class="token operator">*</span> <span class="token function">exChange</span><span class="token punctuation">(</span>unit<span class="token punctuation">.</span><span class="token function">toString</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 计算并累加到总数</span>
                    amount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 重置金额</span>
                    unit<span class="token punctuation">.</span><span class="token function">setLength</span><span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 清空单位</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// 输出汇总结果，只保留整数部分</span>
        <span class="token class-name">System</span><span class="token punctuation">.</span>out<span class="token punctuation">.</span><span class="token function">println</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token keyword">int</span><span class="token punctuation">)</span> totalFen<span class="token punctuation">)</span><span class="token punctuation">;</span>
        scanner<span class="token punctuation">.</span><span class="token function">close</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 关闭Scanner对象</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li></ul></pre> 
<h2><a name="t10"></a><a id="Python_195"></a>Python</h2> 
<pre data-index="9" class="set-code-hide prettyprint"><code class="prism language-python has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">def</span> <span class="token function">exChange</span><span class="token punctuation">(</span>unit<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token comment"># 根据货币单位返回其转换为人民币分的汇率</span>
    <span class="token keyword">if</span> unit <span class="token operator">==</span> <span class="token string">"CNY"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span>  <span class="token comment"># 人民币</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"JPY"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span> <span class="token operator">*</span> <span class="token number">100</span>  <span class="token comment"># 日元</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"HKD"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span> <span class="token operator">*</span> <span class="token number">100</span>  <span class="token comment"># 港元</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"EUR"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span> <span class="token operator">*</span> <span class="token number">100</span>  <span class="token comment"># 欧元</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"GBP"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span> <span class="token operator">*</span> <span class="token number">100</span>  <span class="token comment"># 英镑</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"fen"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">1.0</span>  <span class="token comment"># 人民币分</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"cents"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span>  <span class="token comment"># 港元分</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"sen"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span>  <span class="token comment"># 日元分</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"eurocents"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span>  <span class="token comment"># 欧元分</span>
    <span class="token keyword">elif</span> unit <span class="token operator">==</span> <span class="token string">"pence"</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span>  <span class="token comment"># 英镑分</span>
    <span class="token keyword">else</span><span class="token punctuation">:</span>
        <span class="token keyword">return</span> <span class="token number">0.0</span>  <span class="token comment"># 无效单位返回0</span>


<span class="token keyword">def</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    n <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span><span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment"># 读取记录数</span>
    totalFen <span class="token operator">=</span> <span class="token number">0.0</span>  <span class="token comment"># 汇总结果</span>

    <span class="token comment"># 处理每一条货币记录</span>
    <span class="token keyword">for</span> _ <span class="token keyword">in</span> <span class="token builtin">range</span><span class="token punctuation">(</span>n<span class="token punctuation">)</span><span class="token punctuation">:</span>
        record <span class="token operator">=</span> <span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span>  <span class="token comment"># 读取每一行的记录</span>
        amount <span class="token operator">=</span> <span class="token number">0</span>  <span class="token comment"># 用于保存金额</span>
        unit <span class="token operator">=</span> <span class="token string">""</span>  <span class="token comment"># 保存单位</span>

        <span class="token comment"># 遍历当前行，逐个提取金额和单位</span>
        <span class="token keyword">for</span> j<span class="token punctuation">,</span> c <span class="token keyword">in</span> <span class="token builtin">enumerate</span><span class="token punctuation">(</span>record<span class="token punctuation">)</span><span class="token punctuation">:</span>
            <span class="token keyword">if</span> c<span class="token punctuation">.</span>isdigit<span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
                amount <span class="token operator">=</span> amount <span class="token operator">*</span> <span class="token number">10</span> <span class="token operator">+</span> <span class="token builtin">int</span><span class="token punctuation">(</span>c<span class="token punctuation">)</span>  <span class="token comment"># 构建数字</span>
            <span class="token keyword">else</span><span class="token punctuation">:</span>
                unit <span class="token operator">+=</span> c  <span class="token comment"># 构建货币单位</span>

            <span class="token comment"># 当遇到完整的金额+单位时进行换算</span>
            <span class="token keyword">if</span> j <span class="token operator">==</span> <span class="token builtin">len</span><span class="token punctuation">(</span>record<span class="token punctuation">)</span> <span class="token operator">-</span> <span class="token number">1</span> <span class="token keyword">or</span> <span class="token punctuation">(</span>j <span class="token operator">+</span> <span class="token number">1</span> <span class="token operator">&lt;</span> <span class="token builtin">len</span><span class="token punctuation">(</span>record<span class="token punctuation">)</span> <span class="token keyword">and</span> record<span class="token punctuation">[</span>j <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">.</span>isdigit<span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword">and</span> unit<span class="token punctuation">)</span><span class="token punctuation">:</span>
                totalFen <span class="token operator">+=</span> amount <span class="token operator">*</span> exChange<span class="token punctuation">(</span>unit<span class="token punctuation">)</span>  <span class="token comment"># 计算并累加到总数</span>
                amount <span class="token operator">=</span> <span class="token number">0</span>  <span class="token comment"># 重置金额</span>
                unit <span class="token operator">=</span> <span class="token string">""</span>  <span class="token comment"># 清空单位</span>

    <span class="token comment"># 输出汇总结果，只保留整数部分</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token builtin">int</span><span class="token punctuation">(</span>totalFen<span class="token punctuation">)</span><span class="token punctuation">)</span>


<span class="token keyword">if</span> __name__ <span class="token operator">==</span> <span class="token string">"__main__"</span><span class="token punctuation">:</span>
    main<span class="token punctuation">(</span><span class="token punctuation">)</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li></ul></pre> 
<h2><a name="t11"></a><a id="JavaScript_254"></a>JavaScript</h2> 
<pre data-index="10" class="set-code-hide prettyprint"><code class="prism language-js has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">const</span> readline <span class="token operator">=</span> <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">'readline'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 根据货币单位返回其转换为人民币分的汇率</span>
<span class="token keyword">function</span> <span class="token function">exChange</span><span class="token punctuation">(</span><span class="token parameter">unit</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">switch</span> <span class="token punctuation">(</span>unit<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">case</span> <span class="token string">'CNY'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span><span class="token punctuation">;</span> <span class="token comment">// 人民币</span>
        <span class="token keyword">case</span> <span class="token string">'JPY'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 日元</span>
        <span class="token keyword">case</span> <span class="token string">'HKD'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 港元</span>
        <span class="token keyword">case</span> <span class="token string">'EUR'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 欧元</span>
        <span class="token keyword">case</span> <span class="token string">'GBP'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 英镑</span>
        <span class="token keyword">case</span> <span class="token string">'fen'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">1.0</span><span class="token punctuation">;</span> <span class="token comment">// 人民币分</span>
        <span class="token keyword">case</span> <span class="token string">'cents'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span><span class="token punctuation">;</span> <span class="token comment">// 港元分</span>
        <span class="token keyword">case</span> <span class="token string">'sen'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span><span class="token punctuation">;</span> <span class="token comment">// 日元分</span>
        <span class="token keyword">case</span> <span class="token string">'eurocents'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span><span class="token punctuation">;</span> <span class="token comment">// 欧元分</span>
        <span class="token keyword">case</span> <span class="token string">'pence'</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span><span class="token punctuation">;</span> <span class="token comment">// 英镑分</span>
        <span class="token keyword">default</span><span class="token operator">:</span>
            <span class="token keyword">return</span> <span class="token number">0.0</span><span class="token punctuation">;</span> <span class="token comment">// 无效单位返回0</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

 
<span class="token keyword">const</span> rl <span class="token operator">=</span> readline<span class="token punctuation">.</span><span class="token function">createInterface</span><span class="token punctuation">(</span><span class="token punctuation">{<!-- --></span>
    <span class="token literal-property property">input</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdin<span class="token punctuation">,</span>
    <span class="token literal-property property">output</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdout
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

 
<span class="token keyword">let</span> input <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
rl<span class="token punctuation">.</span><span class="token function">on</span><span class="token punctuation">(</span><span class="token string">'line'</span><span class="token punctuation">,</span> <span class="token punctuation">(</span><span class="token parameter">line</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{<!-- --></span>
    input<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span>line<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">on</span><span class="token punctuation">(</span><span class="token string">'close'</span><span class="token punctuation">,</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">const</span> n <span class="token operator">=</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>input<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取记录数</span>
    <span class="token keyword">let</span> totalFen <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 汇总结果</span>

    <span class="token comment">// 处理每一条货币记录</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">let</span> i <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span> i <span class="token operator">&lt;=</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">const</span> record <span class="token operator">=</span> input<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>
        <span class="token keyword">let</span> amount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 用于保存金额</span>
        <span class="token keyword">let</span> unit <span class="token operator">=</span> <span class="token string">''</span><span class="token punctuation">;</span> <span class="token comment">// 保存单位</span>

        <span class="token comment">// 遍历当前行，逐个提取金额和单位</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">let</span> j <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> j <span class="token operator">&lt;</span> record<span class="token punctuation">.</span>length<span class="token punctuation">;</span> j<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">const</span> c <span class="token operator">=</span> record<span class="token punctuation">[</span>j<span class="token punctuation">]</span><span class="token punctuation">;</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token regex"><span class="token regex-delimiter">/</span><span class="token regex-source language-regex">\d</span><span class="token regex-delimiter">/</span></span><span class="token punctuation">.</span><span class="token function">test</span><span class="token punctuation">(</span>c<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                amount <span class="token operator">=</span> amount <span class="token operator">*</span> <span class="token number">10</span> <span class="token operator">+</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>c<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 构建数字</span>
            <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{<!-- --></span>
                unit <span class="token operator">+=</span> c<span class="token punctuation">;</span> <span class="token comment">// 构建货币单位</span>
            <span class="token punctuation">}</span>

            <span class="token comment">// 当遇到完整的金额+单位时进行换算</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>j <span class="token operator">===</span> record<span class="token punctuation">.</span>length <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">||</span> <span class="token punctuation">(</span><span class="token regex"><span class="token regex-delimiter">/</span><span class="token regex-source language-regex">\d</span><span class="token regex-delimiter">/</span></span><span class="token punctuation">.</span><span class="token function">test</span><span class="token punctuation">(</span>record<span class="token punctuation">[</span>j <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> unit<span class="token punctuation">.</span>length <span class="token operator">&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                totalFen <span class="token operator">+=</span> amount <span class="token operator">*</span> <span class="token function">exChange</span><span class="token punctuation">(</span>unit<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 计算并累加到总数</span>
                amount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 重置金额</span>
                unit <span class="token operator">=</span> <span class="token string">''</span><span class="token punctuation">;</span> <span class="token comment">// 清空单位</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 输出汇总结果，只保留整数部分</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>Math<span class="token punctuation">.</span><span class="token function">floor</span><span class="token punctuation">(</span>totalFen<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li></ul></pre> 
<h2><a name="t12"></a><a id="C_331"></a>C++</h2> 
<pre data-index="11" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;iostream&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;string&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;cctype&gt;</span> <span class="token comment">// 用于 isdigit 函数</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;sstream&gt;</span> <span class="token comment">// 用于 stringstream</span></span>

using namespace std<span class="token punctuation">;</span>

<span class="token comment">// 根据货币单位返回其转换为人民币分的汇率</span>
<span class="token keyword">double</span> <span class="token function">exChange</span><span class="token punctuation">(</span><span class="token keyword">const</span> string <span class="token operator">&amp;</span>unit<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"CNY"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span><span class="token punctuation">;</span> <span class="token comment">// 人民币</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"JPY"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 日元</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"HKD"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 港元</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"EUR"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 欧元</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"GBP"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 英镑</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"fen"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">1.0</span><span class="token punctuation">;</span> <span class="token comment">// 人民币分</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"cents"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span><span class="token punctuation">;</span> <span class="token comment">// 港元分</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"sen"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span><span class="token punctuation">;</span> <span class="token comment">// 日元分</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"eurocents"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span><span class="token punctuation">;</span> <span class="token comment">// 欧元分</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span>unit <span class="token operator">==</span> <span class="token string">"pence"</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span><span class="token punctuation">;</span> <span class="token comment">// 英镑分</span>
    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token number">0.0</span><span class="token punctuation">;</span> <span class="token comment">// 无效单位返回0</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> n<span class="token punctuation">;</span>
    cin <span class="token operator">&gt;&gt;</span> n<span class="token punctuation">;</span> <span class="token comment">// 读取记录数</span>
    cin<span class="token punctuation">.</span><span class="token function">ignore</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 忽略换行符</span>

    <span class="token keyword">double</span> totalFen <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 汇总结果</span>

    <span class="token comment">// 处理每一条货币记录</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        string record<span class="token punctuation">;</span>
        <span class="token function">getline</span><span class="token punctuation">(</span>cin<span class="token punctuation">,</span> record<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 读取每一行的记录</span>
        <span class="token keyword">int</span> amount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 用于保存金额</span>
        string unit<span class="token punctuation">;</span> <span class="token comment">// 保存单位</span>

        <span class="token comment">// 遍历当前行，逐个提取金额和单位</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token class-name">size_t</span> j <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> j <span class="token operator">&lt;</span> record<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> j<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">char</span> c <span class="token operator">=</span> record<span class="token punctuation">[</span>j<span class="token punctuation">]</span><span class="token punctuation">;</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">isdigit</span><span class="token punctuation">(</span>c<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                amount <span class="token operator">=</span> amount <span class="token operator">*</span> <span class="token number">10</span> <span class="token operator">+</span> <span class="token punctuation">(</span>c <span class="token operator">-</span> <span class="token char">'0'</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 构建数字</span>
            <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{<!-- --></span>
                unit <span class="token operator">+=</span> c<span class="token punctuation">;</span> <span class="token comment">// 构建货币单位</span>
            <span class="token punctuation">}</span>

            <span class="token comment">// 当遇到完整的金额+单位时进行换算</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>j <span class="token operator">==</span> record<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">||</span> <span class="token punctuation">(</span><span class="token function">isdigit</span><span class="token punctuation">(</span>record<span class="token punctuation">[</span>j <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> <span class="token operator">!</span>unit<span class="token punctuation">.</span><span class="token function">empty</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                totalFen <span class="token operator">+=</span> amount <span class="token operator">*</span> <span class="token function">exChange</span><span class="token punctuation">(</span>unit<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 计算并累加到总数</span>
                amount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> <span class="token comment">// 重置金额</span>
                unit<span class="token punctuation">.</span><span class="token function">clear</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 清空单位</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 输出汇总结果，只保留整数部分</span>
    cout <span class="token operator">&lt;&lt;</span> static_cast<span class="token operator">&lt;</span><span class="token keyword">int</span><span class="token operator">&gt;</span><span class="token punctuation">(</span>totalFen<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> endl<span class="token punctuation">;</span>

    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li></ul></pre> 
<h2><a name="t13"></a><a id="C_407"></a>C语言</h2> 
<pre data-index="12" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdio.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;string.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;ctype.h&gt;</span></span>

<span class="token comment">// 汇率转换函数，返回转换为人民币分的汇率</span>
<span class="token keyword">double</span> <span class="token function">exChange</span><span class="token punctuation">(</span><span class="token keyword">const</span> <span class="token keyword">char</span><span class="token operator">*</span> unit<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"CNY"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span><span class="token punctuation">;</span> <span class="token comment">// 人民币元</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"JPY"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 日元</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"HKD"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 港元</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"EUR"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 欧元</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"GBP"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span> <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">;</span> <span class="token comment">// 英镑</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"fen"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">1.0</span><span class="token punctuation">;</span> <span class="token comment">// 人民币分</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"cents"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">123</span><span class="token punctuation">;</span> <span class="token comment">// 港元分</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"sen"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">1825</span><span class="token punctuation">;</span> <span class="token comment">// 日元分</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"eurocents"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">14</span><span class="token punctuation">;</span> <span class="token comment">// 欧元分</span>
    <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">strcmp</span><span class="token punctuation">(</span>unit<span class="token punctuation">,</span> <span class="token string">"pence"</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token keyword">return</span> <span class="token number">100.0</span> <span class="token operator">/</span> <span class="token number">12</span><span class="token punctuation">;</span> <span class="token comment">// 英镑分</span>
    <span class="token keyword">else</span>
        <span class="token keyword">return</span> <span class="token number">0.0</span><span class="token punctuation">;</span> <span class="token comment">// 无效单位</span>
<span class="token punctuation">}</span>

<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> n<span class="token punctuation">;</span>  <span class="token comment">// 记录数</span>
    <span class="token function">scanf</span><span class="token punctuation">(</span><span class="token string">"%d"</span><span class="token punctuation">,</span> <span class="token operator">&amp;</span>n<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 读取记录数</span>
    <span class="token function">getchar</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 读取换行符，避免影响后续输入</span>

    <span class="token keyword">double</span> totalFen <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>  <span class="token comment">// 汇总结果（人民币分）</span>

    <span class="token comment">// 处理每一条货币记录</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> n<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">char</span> record<span class="token punctuation">[</span><span class="token number">100</span><span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 保存输入的记录</span>
        <span class="token function">fgets</span><span class="token punctuation">(</span>record<span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">,</span> <span class="token constant">stdin</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 读取整行记录</span>

        <span class="token keyword">int</span> amount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>  <span class="token comment">// 保存金额</span>
        <span class="token keyword">char</span> unit<span class="token punctuation">[</span><span class="token number">20</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token string">""</span><span class="token punctuation">;</span>  <span class="token comment">// 保存货币单位</span>
        <span class="token keyword">int</span> len <span class="token operator">=</span> <span class="token function">strlen</span><span class="token punctuation">(</span>record<span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token comment">// 解析每一条记录</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> j <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> j <span class="token operator">&lt;</span> len<span class="token punctuation">;</span> j<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">char</span> c <span class="token operator">=</span> record<span class="token punctuation">[</span>j<span class="token punctuation">]</span><span class="token punctuation">;</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">isdigit</span><span class="token punctuation">(</span>c<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                amount <span class="token operator">=</span> amount <span class="token operator">*</span> <span class="token number">10</span> <span class="token operator">+</span> <span class="token punctuation">(</span>c <span class="token operator">-</span> <span class="token char">'0'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 构建数字</span>
            <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token function">isalpha</span><span class="token punctuation">(</span>c<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token keyword">int</span> k <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
                <span class="token keyword">while</span> <span class="token punctuation">(</span>j <span class="token operator">&lt;</span> len <span class="token operator">&amp;&amp;</span> <span class="token function">isalpha</span><span class="token punctuation">(</span>record<span class="token punctuation">[</span>j<span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                    unit<span class="token punctuation">[</span>k<span class="token operator">++</span><span class="token punctuation">]</span> <span class="token operator">=</span> record<span class="token punctuation">[</span>j<span class="token operator">++</span><span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 构建单位</span>
                <span class="token punctuation">}</span>
                unit<span class="token punctuation">[</span>k<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token char">'\0'</span><span class="token punctuation">;</span>  <span class="token comment">// 添加字符串终止符</span>
                totalFen <span class="token operator">+=</span> amount <span class="token operator">*</span> <span class="token function">exChange</span><span class="token punctuation">(</span>unit<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 计算并累加</span>
                amount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>  <span class="token comment">// 重置金额</span>
                unit<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token char">'\0'</span><span class="token punctuation">;</span>  <span class="token comment">// 清空单位</span>
                j<span class="token operator">--</span><span class="token punctuation">;</span>  <span class="token comment">// 回到当前字符（避免跳过字符）</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        
    <span class="token punctuation">}</span>

    <span class="token comment">// 输出汇总结果，只保留整数部分</span>
    <span class="token function">printf</span><span class="token punctuation">(</span><span class="token string">"%d\n"</span><span class="token punctuation">,</span> <span class="token punctuation">(</span><span class="token keyword">int</span><span class="token punctuation">)</span>totalFen<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li></ul></pre> 
 
<h2><a name="t14"></a><a id="_492"></a>完整用例</h2> 
<h3><a name="t15"></a><a id="1_493"></a>用例1</h3> 
<pre data-index="13" class="set-code-show prettyprint"><code class="prism language-input1 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">1
100CNY
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<h3><a name="t16"></a><a id="2_500"></a>用例2</h3> 
<pre data-index="14" class="set-code-show prettyprint"><code class="prism language-input2 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">1
3000fen
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<h3><a name="t17"></a><a id="3_507"></a>用例3</h3> 
<pre data-index="15" class="set-code-show prettyprint"><code class="prism language-input3 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">1
123HKD
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<h3><a name="t18"></a><a id="4_514"></a>用例4</h3> 
<pre data-index="16" class="set-code-show prettyprint"><code class="prism language-input4 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">2
20CNY53fen
53HKD87cents
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li></ul></pre> 
<h3><a name="t19"></a><a id="5_522"></a>用例5</h3> 
<pre data-index="17" class="set-code-show prettyprint"><code class="prism language-input5 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">2
100CNY
100JPY
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li></ul></pre> 
<h3><a name="t20"></a><a id="6_530"></a>用例6</h3> 
<pre data-index="18" class="set-code-show prettyprint"><code class="prism language-input6 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">3
50EUR
25GBP
1000fen
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li></ul></pre> 
<h3><a name="t21"></a><a id="7_539"></a>用例7</h3> 
<pre data-index="19" class="set-code-show prettyprint"><code class="prism language-input7 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">4
5000sen
1000cents
200eurocents
300pence
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li></ul></pre> 
<h3><a name="t22"></a><a id="8_549"></a>用例8</h3> 
<pre data-index="20" class="set-code-show prettyprint"><code class="prism language-input8 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">3
100HKD
200JPY
50EUR
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li></ul></pre> 
<h3><a name="t23"></a><a id="9_558"></a>用例9</h3> 
<pre data-index="21" class="set-code-show prettyprint"><code class="prism language-input9 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">2
1000pence
5000eurocents
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li></ul></pre> 
<h3><a name="t24"></a><a id="10_566"></a>用例10</h3> 
<pre data-index="22" class="set-code-show prettyprint"><code class="prism language-input10 has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">4
10CNY
20JPY
30HKD
40EUR
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li></ul></pre>
                </div><div data-report-view="{&quot;mod&quot;:&quot;1585297308_001&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6548&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/banxia_frontend/article/details/142380887&quot;,&quot;extend1&quot;:&quot;pc&quot;,&quot;ab&quot;:&quot;new&quot;}"><div></div></div>
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/markdown_views-f23dff6052.css" rel="stylesheet">
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/style-e504d6a974.css" rel="stylesheet">
        </div></html>