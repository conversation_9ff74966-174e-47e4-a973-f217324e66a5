<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box"><div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
             
                <div id="content_views" class="markdown_views prism-atom-one-light">
                    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
                        <path stroke-linecap="round" d="M5,0 0,2.5 5,5z" id="raphael-marker-block" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path>
                    </svg>
                    <h2><a name="t0"></a><a id="OD_1"></a>最新华为OD机试</h2> 

<p><img src="https://i-blog.csdnimg.cn/direct/6e7a73f0404e4c65a2d80024274cd970.png" alt="在这里插入图片描述"></p> 
<h2><a name="t1"></a><a id="_8"></a>题目描述</h2> 
<p>流浪地球计划在赤道上均匀部署了N个转向发动机，按位置顺序编号为0~N-1。</p> 
<ol><li>初始状态下所有的发动机都是未启动状态;</li><li>发动机启动的方式分为”手动启动"和”关联启动"两种方式;</li><li>如果在时刻1一个发动机被启动，下一个时刻2与之相邻的两个发动机就会被”关联启动”;</li><li>如果准备启动某个发动机时，它已经被启动了，则什么都不用做;</li><li>发动机0与发动机N-1是相邻的;</li></ol> 
<p>地球联合政府准备挑选某些发动机在某些时刻进行“手动启动”。当然最终所有的发动机都会被启动。</p> 
<p>哪些发动机最晚被启动呢?</p> 
<h2><a name="t2"></a><a id="_23"></a>输入描述</h2> 
<ul><li>第一行两个数字N和E，中间有空格<br> N代表部署发动机的总个数，E代表计划手动启动的发动机总个数<br> 1&lt;N&lt;=1000,1&lt;=E&lt;=1000,E&lt;=N</li><li>接下来共E行，每行都是两个数字T和P，中间有空格<br> T代表发动机的手动启动时刻，P代表此发动机的位置编号。<br> 0&lt;=T&lt;=N.0&lt;=P&lt;N</li></ul> 
<h2><a name="t3"></a><a id="_33"></a>输出描述</h2> 
<ul><li>第一行一个数字N，以回车结束<br> N代表最后被启动的发动机个数</li><li>第二行N个数字，中间有空格，以回车结束<br> 每个数字代表发动机的位置编号，从小到大排序</li></ul> 
<h2><a name="t4"></a><a id="1_40"></a>示例1</h2> 
<p>输入</p> 
<pre data-index="0" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">8 2
0 2
0 6
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li></ul></pre> 
<p>输出</p> 
<pre data-index="1" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">2
0 4
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<p>说明</p> 
<blockquote> 
 <p>8个发动机，时刻0启动2和6号发动机</p> 
</blockquote> 
<h2><a name="t5"></a><a id="2_63"></a>示例2</h2> 
<p>输入：</p> 
<pre data-index="2" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">8 2
0 0
1 7
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li></ul></pre> 
<p>输出：</p> 
<pre data-index="3" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">1
4
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li></ul></pre> 
<p>说明</p> 
<blockquote> 
 <p>8个发动机，时刻0手动启动0，时刻1手动启动7.</p> 
</blockquote> 
<h2><a name="t6"></a><a id="_84"></a>解题思路</h2> 
<h3><a name="t7"></a><a id="_86"></a>题目解析</h3> 
<ol><li> <p><strong>初始状态</strong>：所有的发动机都是未启动状态。</p> </li><li> <p><strong>启动方式</strong>：</p> 
  <ul><li><strong>手动启动</strong>：你可以在某个时刻手动启动指定位置的发动机。</li><li><strong>关联启动</strong>：如果一个发动机在时刻1被启动，那么它的相邻发动机（左右两个，位置编号上相邻）会在下一时刻（时刻2）被自动启动。</li></ul> </li><li> <p><strong>循环关系</strong>：发动机0和N-1是相邻的，这意味着整个发动机的排列是环形的。</p> </li><li> <p><strong>发动机启动规则</strong>：</p> 
  <ul><li>如果准备启动的发动机已经被启动，那么就不需要做任何操作。</li><li>需要根据手动启动的时刻和位置，推算出所有发动机何时被启动，并确定哪些发动机在最后一个时刻被启动。</li></ul> </li></ol> 
<h3><a name="t8"></a><a id="_102"></a>用例图解</h3> 
<p>如图所示：</p> 
<ul><li>在时刻0，发动机2和6被手动启动。</li><li>在时刻1，发动机1、3、5、7将被关联启动。</li><li>到了时刻2，发动机0和4将被关联启动。</li><li>因此，发动机0和4是最后一批被启动的。</li></ul> 
<p><img src="https://img-blog.csdnimg.cn/img_convert/aa31fb6c2c5524d296ee88c21b0b79d2.png" alt="image-20240817105929077"></p> 
<h2><a name="t9"></a><a id="Java_115"></a>Java</h2> 
<pre data-index="4" class="set-code-hide prettyprint"><code class="prism language-java has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token operator">*</span></span><span class="token punctuation">;</span>

<span class="token keyword">public</span> <span class="token keyword">class</span> <span class="token class-name">EngineManager</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 检查数组中是否有引擎处于未激活状态（即状态为 -1）</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">boolean</span> <span class="token function">hasInactiveEngines</span><span class="token punctuation">(</span><span class="token keyword">int</span><span class="token punctuation">[</span><span class="token punctuation">]</span> engineStatuses<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">return</span> <span class="token class-name">Arrays</span><span class="token punctuation">.</span><span class="token function">stream</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">anyMatch</span><span class="token punctuation">(</span>status <span class="token operator">-&gt;</span> status <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
 
    <span class="token comment">// 激活指定引擎的相邻引擎</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">activateAdjacentEngines</span><span class="token punctuation">(</span><span class="token keyword">int</span><span class="token punctuation">[</span><span class="token punctuation">]</span> engineStatuses<span class="token punctuation">,</span> <span class="token keyword">int</span> currentEngine<span class="token punctuation">,</span> <span class="token keyword">int</span> activationTime<span class="token punctuation">,</span> <span class="token keyword">int</span> totalEngines<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">int</span> leftEngine <span class="token operator">=</span> currentEngine <span class="token operator">==</span> <span class="token number">0</span> <span class="token operator">?</span> totalEngines <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">:</span> <span class="token punctuation">(</span>currentEngine <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 计算左边相邻引擎的索引</span>
        <span class="token keyword">int</span> rightEngine <span class="token operator">=</span> currentEngine <span class="token operator">==</span> totalEngines <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">?</span> <span class="token number">0</span> <span class="token operator">:</span> currentEngine <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">;</span>  <span class="token comment">// 计算右边相邻引擎的索引</span>
        engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span> <span class="token operator">=</span> engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span> <span class="token operator">?</span> activationTime <span class="token operator">:</span> engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 若左引擎未激活，则激活</span>
        engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span> <span class="token operator">=</span> engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span> <span class="token operator">?</span> activationTime <span class="token operator">:</span> engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 若右引擎未激活，则激活</span>
    <span class="token punctuation">}</span>
 
    <span class="token comment">// 更新所有引擎的激活状态，直到所有引擎都被激活</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">updateEngineStatuses</span><span class="token punctuation">(</span><span class="token keyword">int</span><span class="token punctuation">[</span><span class="token punctuation">]</span> engineStatuses<span class="token punctuation">,</span> <span class="token keyword">int</span> startTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">boolean</span> continueUpdate <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span>
        <span class="token keyword">while</span> <span class="token punctuation">(</span>continueUpdate<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> engineStatuses<span class="token punctuation">.</span>length<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> startTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>              
                    <span class="token function">activateAdjacentEngines</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> i<span class="token punctuation">,</span> startTime <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">,</span> engineStatuses<span class="token punctuation">.</span>length<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 激活当前引擎的相邻引擎</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
            startTime<span class="token operator">++</span><span class="token punctuation">;</span>  <span class="token comment">// 增加时间步长，检查下一个时间点</span>
            continueUpdate <span class="token operator">=</span> <span class="token function">hasInactiveEngines</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 检查是否还有未激活的引擎</span>
        <span class="token punctuation">}</span>
        <span class="token keyword">int</span> lastActivationTime <span class="token operator">=</span> <span class="token class-name">Arrays</span><span class="token punctuation">.</span><span class="token function">stream</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">max</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">getAsInt</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 获取最后一个被激活的时间</span>
        <span class="token keyword">int</span> countActiveEngines <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
        <span class="token class-name">StringBuilder</span> enginesReport <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">StringBuilder</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> engineStatuses<span class="token punctuation">.</span>length<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">if</span><span class="token punctuation">(</span>lastActivationTime <span class="token operator">==</span> engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                enginesReport<span class="token punctuation">.</span><span class="token function">append</span><span class="token punctuation">(</span>i <span class="token operator">+</span> <span class="token string">" "</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                countActiveEngines<span class="token operator">++</span><span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
        <span class="token class-name">System</span><span class="token punctuation">.</span>out<span class="token punctuation">.</span><span class="token function">println</span><span class="token punctuation">(</span>countActiveEngines<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输出最后激活时间的引擎数量</span>
        <span class="token class-name">System</span><span class="token punctuation">.</span>out<span class="token punctuation">.</span><span class="token function">println</span><span class="token punctuation">(</span>enginesReport<span class="token punctuation">.</span><span class="token function">toString</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">trim</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输出这些引擎的编号</span>
    <span class="token punctuation">}</span>
 
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> args<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token class-name">Scanner</span> scanner <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Scanner</span><span class="token punctuation">(</span><span class="token class-name">System</span><span class="token punctuation">.</span>in<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">while</span> <span class="token punctuation">(</span>scanner<span class="token punctuation">.</span><span class="token function">hasNextLine</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> inputs <span class="token operator">=</span> scanner<span class="token punctuation">.</span><span class="token function">nextLine</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">" "</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword">int</span> numberOfEngines <span class="token operator">=</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token function">parseInt</span><span class="token punctuation">(</span>inputs<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 引擎总数</span>
            <span class="token keyword">int</span> numberOfEntries <span class="token operator">=</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token function">parseInt</span><span class="token punctuation">(</span>inputs<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输入的数据条数</span>

            <span class="token keyword">int</span><span class="token punctuation">[</span><span class="token punctuation">]</span> engineStatuses <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token keyword">int</span><span class="token punctuation">[</span>numberOfEngines<span class="token punctuation">]</span><span class="token punctuation">;</span>
            <span class="token class-name">Arrays</span><span class="token punctuation">.</span><span class="token function">fill</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 初始状态设置所有引擎为未激活</span>
            <span class="token keyword">int</span> earliestActivation <span class="token operator">=</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token constant">MAX_VALUE</span><span class="token punctuation">;</span>

            <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> numberOfEntries<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> timeIndex <span class="token operator">=</span> scanner<span class="token punctuation">.</span><span class="token function">nextLine</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">" "</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token keyword">int</span> activationTime <span class="token operator">=</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token function">parseInt</span><span class="token punctuation">(</span>timeIndex<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 激活时间</span>
                <span class="token keyword">int</span> engineIndex <span class="token operator">=</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token function">parseInt</span><span class="token punctuation">(</span>timeIndex<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 引擎索引</span>
                engineStatuses<span class="token punctuation">[</span>engineIndex<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span>  <span class="token comment">// 设置激活时间</span>
                earliestActivation <span class="token operator">=</span> <span class="token class-name">Math</span><span class="token punctuation">.</span><span class="token function">min</span><span class="token punctuation">(</span>earliestActivation<span class="token punctuation">,</span> activationTime<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 记录最早的激活时间</span>
            <span class="token punctuation">}</span>
            <span class="token function">updateEngineStatuses</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> earliestActivation<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 根据最早的激活时间开始更新状态</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li></ul></pre> 
<h2><a name="t10"></a><a id="Python_183"></a>Python</h2> 
<pre data-index="5" class="set-code-hide prettyprint"><code class="prism language-python has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">def</span> <span class="token function">has_inactive_engines</span><span class="token punctuation">(</span>engine_statuses<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""检查列表中是否有引擎处于未激活状态（即状态为 -1）。
    返回值为布尔类型，True表示存在未激活的引擎，False则表示所有引擎均已激活。"""</span>
    <span class="token keyword">return</span> <span class="token operator">-</span><span class="token number">1</span> <span class="token keyword">in</span> engine_statuses

<span class="token keyword">def</span> <span class="token function">activate_adjacent_engines</span><span class="token punctuation">(</span>engine_statuses<span class="token punctuation">,</span> current_engine<span class="token punctuation">,</span> activation_time<span class="token punctuation">,</span> total_engines<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""激活指定引擎的相邻引擎。计算并更新左右两边的引擎状态。
    - current_engine: 当前引擎的索引。
    - activation_time: 当前引擎的激活时间。
    - total_engines: 引擎总数，用于计算边界条件。"""</span>
    left_engine <span class="token operator">=</span> total_engines <span class="token operator">-</span> <span class="token number">1</span> <span class="token keyword">if</span> current_engine <span class="token operator">==</span> <span class="token number">0</span> <span class="token keyword">else</span> current_engine <span class="token operator">-</span> <span class="token number">1</span>
    right_engine <span class="token operator">=</span> <span class="token number">0</span> <span class="token keyword">if</span> current_engine <span class="token operator">==</span> total_engines <span class="token operator">-</span> <span class="token number">1</span> <span class="token keyword">else</span> current_engine <span class="token operator">+</span> <span class="token number">1</span>
    <span class="token keyword">if</span> engine_statuses<span class="token punctuation">[</span>left_engine<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">:</span>
        engine_statuses<span class="token punctuation">[</span>left_engine<span class="token punctuation">]</span> <span class="token operator">=</span> activation_time
    <span class="token keyword">if</span> engine_statuses<span class="token punctuation">[</span>right_engine<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">:</span>
        engine_statuses<span class="token punctuation">[</span>right_engine<span class="token punctuation">]</span> <span class="token operator">=</span> activation_time

<span class="token keyword">def</span> <span class="token function">update_engine_statuses</span><span class="token punctuation">(</span>engine_statuses<span class="token punctuation">,</span> start_time<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""更新所有引擎的激活状态，直到所有引擎都被激活。
    进行循环检查，若当前时间点有引擎被激活，则激活其相邻引擎，并递增时间步长。"""</span>
    continue_update <span class="token operator">=</span> <span class="token boolean">True</span>
    <span class="token keyword">while</span> continue_update<span class="token punctuation">:</span>
        <span class="token keyword">for</span> i<span class="token punctuation">,</span> status <span class="token keyword">in</span> <span class="token builtin">enumerate</span><span class="token punctuation">(</span>engine_statuses<span class="token punctuation">)</span><span class="token punctuation">:</span>
            <span class="token keyword">if</span> status <span class="token operator">==</span> start_time<span class="token punctuation">:</span>        
                activate_adjacent_engines<span class="token punctuation">(</span>engine_statuses<span class="token punctuation">,</span> i<span class="token punctuation">,</span> start_time <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">,</span> <span class="token builtin">len</span><span class="token punctuation">(</span>engine_statuses<span class="token punctuation">)</span><span class="token punctuation">)</span>
        start_time <span class="token operator">+=</span> <span class="token number">1</span>
        continue_update <span class="token operator">=</span> has_inactive_engines<span class="token punctuation">(</span>engine_statuses<span class="token punctuation">)</span>
    last_activation_time <span class="token operator">=</span> <span class="token builtin">max</span><span class="token punctuation">(</span>engine_statuses<span class="token punctuation">)</span>
    count_active_engines <span class="token operator">=</span> <span class="token builtin">sum</span><span class="token punctuation">(</span>status <span class="token operator">==</span> last_activation_time <span class="token keyword">for</span> status <span class="token keyword">in</span> engine_statuses<span class="token punctuation">)</span>
    engines_report <span class="token operator">=</span> <span class="token string">' '</span><span class="token punctuation">.</span>join<span class="token punctuation">(</span><span class="token builtin">str</span><span class="token punctuation">(</span>i<span class="token punctuation">)</span> <span class="token keyword">for</span> i<span class="token punctuation">,</span> status <span class="token keyword">in</span> <span class="token builtin">enumerate</span><span class="token punctuation">(</span>engine_statuses<span class="token punctuation">)</span> <span class="token keyword">if</span> status <span class="token operator">==</span> last_activation_time<span class="token punctuation">)</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span>count_active_engines<span class="token punctuation">)</span>  <span class="token comment"># 打印在最后一个激活时间被激活的引擎数量</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span>engines_report<span class="token punctuation">.</span>strip<span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment"># 打印这些引擎的索引</span>

<span class="token comment"># 主循环，持续接受输入直到遇到文件结束符（EOF）</span>
<span class="token keyword">while</span> <span class="token boolean">True</span><span class="token punctuation">:</span>
    <span class="token keyword">try</span><span class="token punctuation">:</span>
        inputs <span class="token operator">=</span> <span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span>split<span class="token punctuation">(</span><span class="token punctuation">)</span>
        number_of_engines <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span>inputs<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">)</span>  <span class="token comment"># 读取引擎总数</span>
        number_of_entries <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span>inputs<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span>  <span class="token comment"># 读取条目数量</span>

        engine_statuses <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">]</span> <span class="token operator">*</span> number_of_engines  <span class="token comment"># 初始化引擎状态数组，初始值为-1表示未激活</span>
        earliest_activation <span class="token operator">=</span> <span class="token builtin">float</span><span class="token punctuation">(</span><span class="token string">'inf'</span><span class="token punctuation">)</span>  <span class="token comment"># 设置最早激活时间为无穷大</span>

        <span class="token keyword">for</span> _ <span class="token keyword">in</span> <span class="token builtin">range</span><span class="token punctuation">(</span>number_of_entries<span class="token punctuation">)</span><span class="token punctuation">:</span>
            time_index <span class="token operator">=</span> <span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">.</span>split<span class="token punctuation">(</span><span class="token punctuation">)</span>
            activation_time <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span>time_index<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">)</span>
            engine_index <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span>time_index<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span>
            engine_statuses<span class="token punctuation">[</span>engine_index<span class="token punctuation">]</span> <span class="token operator">=</span> activation_time  <span class="token comment"># 更新指定引擎的激活时间</span>
            earliest_activation <span class="token operator">=</span> <span class="token builtin">min</span><span class="token punctuation">(</span>earliest_activation<span class="token punctuation">,</span> activation_time<span class="token punctuation">)</span>  <span class="token comment"># 更新最早激活时间</span>

        update_engine_statuses<span class="token punctuation">(</span>engine_statuses<span class="token punctuation">,</span> earliest_activation<span class="token punctuation">)</span>  <span class="token comment"># 根据最早激活时间更新引擎状态</span>
    <span class="token keyword">except</span> EOFError<span class="token punctuation">:</span>
        <span class="token keyword">break</span>  <span class="token comment"># 结束循环，等待输入结束</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li></ul></pre> 
<h2><a name="t11"></a><a id="JavaScript_240"></a>JavaScript</h2> 
<pre data-index="6" class="set-code-hide prettyprint"><code class="prism language-js has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token comment">// 引入 readline 模块并创建接口用于读取来自标准输入（stdin）的数据</span>
<span class="token keyword">const</span> rl <span class="token operator">=</span> <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">"readline"</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">createInterface</span><span class="token punctuation">(</span><span class="token punctuation">{<!-- --></span> <span class="token literal-property property">input</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdin <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 创建异步迭代器，用于按行读取输入</span>
<span class="token keyword">var</span> iter <span class="token operator">=</span> rl<span class="token punctuation">[</span>Symbol<span class="token punctuation">.</span>asyncIterator<span class="token punctuation">]</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 定义一个异步函数用于读取一行输入</span>
<span class="token keyword">const</span> <span class="token function-variable function">readline</span> <span class="token operator">=</span> <span class="token keyword">async</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">(</span><span class="token keyword">await</span> iter<span class="token punctuation">.</span><span class="token function">next</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">.</span>value<span class="token punctuation">;</span>

<span class="token comment">// 立即执行的异步函数</span>
<span class="token keyword">void</span> <span class="token punctuation">(</span><span class="token keyword">async</span> <span class="token keyword">function</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 读取一行输入并按空格分割，获取输入的参数</span>
    <span class="token keyword">const</span> inputs <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token keyword">await</span> <span class="token function">readline</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">" "</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword">const</span> numberOfEngines <span class="token operator">=</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>inputs<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token number">10</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 引擎总数</span>
    <span class="token keyword">const</span> numberOfEntries <span class="token operator">=</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>inputs<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token number">10</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输入的数据条数</span>

    <span class="token comment">// 创建一个数组，初始值为 -1，表示所有引擎初始时都未激活</span>
    <span class="token keyword">const</span> engineStatuses <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Array</span><span class="token punctuation">(</span>numberOfEngines<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">fill</span><span class="token punctuation">(</span><span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword">let</span> earliestActivation <span class="token operator">=</span> <span class="token number">Infinity</span><span class="token punctuation">;</span>  <span class="token comment">// 设置一个变量用于记录最早的激活时间</span>

    <span class="token comment">// 遍历每一个输入条目</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">let</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> numberOfEntries<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">const</span> timeIndex <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token keyword">await</span> <span class="token function">readline</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">" "</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">const</span> activationTime <span class="token operator">=</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>timeIndex<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token number">10</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 读取激活时间</span>
        <span class="token keyword">const</span> engineIndex <span class="token operator">=</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>timeIndex<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token number">10</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 读取引擎索引</span>
        engineStatuses<span class="token punctuation">[</span>engineIndex<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span>  <span class="token comment">// 设置引擎的激活时间</span>
        earliestActivation <span class="token operator">=</span> Math<span class="token punctuation">.</span><span class="token function">min</span><span class="token punctuation">(</span>earliestActivation<span class="token punctuation">,</span> activationTime<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 更新最早的激活时间</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 根据最早的激活时间开始更新所有引擎的状态</span>
    <span class="token keyword">await</span> <span class="token function">updateEngineStatuses</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> earliestActivation<span class="token punctuation">)</span><span class="token punctuation">;</span>
    process<span class="token punctuation">.</span><span class="token function">exit</span><span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 执行完成后退出程序</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 检查是否有未激活的引擎</span>
<span class="token keyword">function</span> <span class="token function">hasInactiveEngines</span><span class="token punctuation">(</span><span class="token parameter">engineStatuses</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">return</span> engineStatuses<span class="token punctuation">.</span><span class="token function">some</span><span class="token punctuation">(</span><span class="token parameter">status</span> <span class="token operator">=&gt;</span> status <span class="token operator">===</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 使用 some 方法检查数组中是否存在未激活的引擎</span>
<span class="token punctuation">}</span>

<span class="token comment">// 激活指定引擎的相邻引擎</span>
<span class="token keyword">function</span> <span class="token function">activateAdjacentEngines</span><span class="token punctuation">(</span><span class="token parameter">engineStatuses<span class="token punctuation">,</span> currentEngine<span class="token punctuation">,</span> activationTime<span class="token punctuation">,</span> totalEngines</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">const</span> leftEngine <span class="token operator">=</span> currentEngine <span class="token operator">===</span> <span class="token number">0</span> <span class="token operator">?</span> totalEngines <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">:</span> currentEngine <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">;</span>  <span class="token comment">// 计算左侧相邻引擎的索引</span>
    <span class="token keyword">const</span> rightEngine <span class="token operator">=</span> currentEngine <span class="token operator">===</span> totalEngines <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">?</span> <span class="token number">0</span> <span class="token operator">:</span> currentEngine <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">;</span>  <span class="token comment">// 计算右侧相邻引擎的索引</span>
    <span class="token comment">// 如果相邻引擎未激活，则设置其激活时间</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span> <span class="token operator">===</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span> <span class="token operator">===</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 循环更新所有引擎的状态，直到没有未激活的引擎</span>
<span class="token keyword">async</span> <span class="token keyword">function</span> <span class="token function">updateEngineStatuses</span><span class="token punctuation">(</span><span class="token parameter">engineStatuses<span class="token punctuation">,</span> startTime</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">let</span> continueUpdate <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span>continueUpdate<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">let</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> engineStatuses<span class="token punctuation">.</span>length<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">===</span> startTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token function">activateAdjacentEngines</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> i<span class="token punctuation">,</span> startTime <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">,</span> engineStatuses<span class="token punctuation">.</span>length<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
        startTime<span class="token operator">++</span><span class="token punctuation">;</span>  <span class="token comment">// 增加时间步，继续检查和更新状态</span>
        continueUpdate <span class="token operator">=</span> <span class="token function">hasInactiveEngines</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 检查是否还有未激活的引擎</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">const</span> lastActivationTime <span class="token operator">=</span> Math<span class="token punctuation">.</span><span class="token function">max</span><span class="token punctuation">(</span><span class="token operator">...</span>engineStatuses<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 计算最后一个被激活的时间</span>
    <span class="token keyword">const</span> countActiveEngines <span class="token operator">=</span> engineStatuses<span class="token punctuation">.</span><span class="token function">filter</span><span class="token punctuation">(</span><span class="token parameter">status</span> <span class="token operator">=&gt;</span> status <span class="token operator">===</span> lastActivationTime<span class="token punctuation">)</span><span class="token punctuation">.</span>length<span class="token punctuation">;</span>  <span class="token comment">// 计算在最后一次激活时间激活的引擎数量</span>
    <span class="token keyword">const</span> enginesReport <span class="token operator">=</span> engineStatuses<span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter">status<span class="token punctuation">,</span> index</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> status <span class="token operator">===</span> lastActivationTime <span class="token operator">?</span> index <span class="token operator">:</span> <span class="token string">''</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">filter</span><span class="token punctuation">(</span>String<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">join</span><span class="token punctuation">(</span><span class="token string">' '</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 创建引擎索引报告</span>

    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>countActiveEngines<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输出激活的引擎数量</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>enginesReport<span class="token punctuation">.</span><span class="token function">trim</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输出激活的引擎索引</span>
<span class="token punctuation">}</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li></ul></pre> 
<h2><a name="t12"></a><a id="C_317"></a>C++</h2> 
<pre data-index="7" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;iostream&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;vector&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;algorithm&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;string&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;sstream&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;limits&gt;</span></span>
using namespace std<span class="token punctuation">;</span>

<span class="token comment">// 检查 vector 中是否有引擎处于未激活状态（即状态为 -1）</span>
bool <span class="token function">hasInactiveEngines</span><span class="token punctuation">(</span><span class="token keyword">const</span> vector<span class="token operator">&lt;</span><span class="token keyword">int</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> engineStatuses<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">return</span> <span class="token function">find</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">.</span><span class="token function">begin</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> engineStatuses<span class="token punctuation">.</span><span class="token function">end</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">!=</span> engineStatuses<span class="token punctuation">.</span><span class="token function">end</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// 激活指定引擎的相邻引擎</span>
<span class="token keyword">void</span> <span class="token function">activateAdjacentEngines</span><span class="token punctuation">(</span>vector<span class="token operator">&lt;</span><span class="token keyword">int</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> engineStatuses<span class="token punctuation">,</span> <span class="token keyword">int</span> currentEngine<span class="token punctuation">,</span> <span class="token keyword">int</span> activationTime<span class="token punctuation">,</span> <span class="token keyword">int</span> totalEngines<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> leftEngine <span class="token operator">=</span> currentEngine <span class="token operator">==</span> <span class="token number">0</span> <span class="token operator">?</span> totalEngines <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">:</span> currentEngine <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">;</span> <span class="token comment">// 计算左边相邻引擎的索引</span>
    <span class="token keyword">int</span> rightEngine <span class="token operator">=</span> currentEngine <span class="token operator">==</span> totalEngines <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">?</span> <span class="token number">0</span> <span class="token operator">:</span> currentEngine <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">;</span> <span class="token comment">// 计算右边相邻引擎的索引</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span> <span class="token comment">// 若左引擎未激活，则激活</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span> <span class="token comment">// 若右引擎未激活，则激活</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 更新所有引擎的激活状态，直到所有引擎都被激活</span>
<span class="token keyword">void</span> <span class="token function">updateEngineStatuses</span><span class="token punctuation">(</span>vector<span class="token operator">&lt;</span><span class="token keyword">int</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> engineStatuses<span class="token punctuation">,</span> <span class="token keyword">int</span> startTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    bool continueUpdate <span class="token operator">=</span> true<span class="token punctuation">;</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span>continueUpdate<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token class-name">size_t</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> engineStatuses<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> startTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token function">activateAdjacentEngines</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> i<span class="token punctuation">,</span> startTime <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">,</span> engineStatuses<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 激活当前引擎的相邻引擎</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
        startTime<span class="token operator">++</span><span class="token punctuation">;</span> <span class="token comment">// 增加时间步长，检查下一个时间点</span>
        continueUpdate <span class="token operator">=</span> <span class="token function">hasInactiveEngines</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 检查是否还有未激活的引擎</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">int</span> lastActivationTime <span class="token operator">=</span> <span class="token operator">*</span><span class="token function">max_element</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">.</span><span class="token function">begin</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> engineStatuses<span class="token punctuation">.</span><span class="token function">end</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 获取最后一个被激活的时间</span>
    <span class="token keyword">int</span> countActiveEngines <span class="token operator">=</span> <span class="token function">count</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">.</span><span class="token function">begin</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> engineStatuses<span class="token punctuation">.</span><span class="token function">end</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> lastActivationTime<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 计算最后激活时间的引擎数量</span>
    cout <span class="token operator">&lt;&lt;</span> countActiveEngines <span class="token operator">&lt;&lt;</span> endl<span class="token punctuation">;</span> <span class="token comment">// 输出最后激活时间的引擎数量</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token class-name">size_t</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> engineStatuses<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> lastActivationTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            cout <span class="token operator">&lt;&lt;</span> i <span class="token operator">&lt;&lt;</span> <span class="token string">" "</span><span class="token punctuation">;</span> <span class="token comment">// 输出最后激活时间的引擎编号</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
    cout <span class="token operator">&lt;&lt;</span> endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    string input<span class="token punctuation">;</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span><span class="token function">getline</span><span class="token punctuation">(</span>cin<span class="token punctuation">,</span> input<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        stringstream <span class="token function">ss</span><span class="token punctuation">(</span>input<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">int</span> numberOfEngines<span class="token punctuation">,</span> numberOfEntries<span class="token punctuation">;</span>
        ss <span class="token operator">&gt;&gt;</span> numberOfEngines <span class="token operator">&gt;&gt;</span> numberOfEntries<span class="token punctuation">;</span>

        vector<span class="token operator">&lt;</span><span class="token keyword">int</span><span class="token operator">&gt;</span> <span class="token function">engineStatuses</span><span class="token punctuation">(</span>numberOfEngines<span class="token punctuation">,</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 初始状态设置所有引擎为未激活</span>
        <span class="token keyword">int</span> earliestActivation <span class="token operator">=</span> numeric_limits<span class="token operator">&lt;</span><span class="token keyword">int</span><span class="token operator">&gt;</span><span class="token operator">::</span><span class="token function">max</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 设置最早的激活时间为最大值</span>

        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> numberOfEntries<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token function">getline</span><span class="token punctuation">(</span>cin<span class="token punctuation">,</span> input<span class="token punctuation">)</span><span class="token punctuation">;</span>
            stringstream <span class="token function">ss2</span><span class="token punctuation">(</span>input<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword">int</span> activationTime<span class="token punctuation">,</span> engineIndex<span class="token punctuation">;</span>
            ss2 <span class="token operator">&gt;&gt;</span> activationTime <span class="token operator">&gt;&gt;</span> engineIndex<span class="token punctuation">;</span>
            engineStatuses<span class="token punctuation">[</span>engineIndex<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span> <span class="token comment">// 设置激活时间</span>
            earliestActivation <span class="token operator">=</span> <span class="token function">min</span><span class="token punctuation">(</span>earliestActivation<span class="token punctuation">,</span> activationTime<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 更新最早的激活时间</span>
        <span class="token punctuation">}</span>
        <span class="token function">updateEngineStatuses</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> earliestActivation<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 根据最早的激活时间开始更新状态</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li></ul></pre> 
<h2><a name="t13"></a><a id="C_392"></a>C语言</h2> 
<pre data-index="8" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdio.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdlib.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;string.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;limits.h&gt;</span></span>

<span class="token comment">// 检查数组中是否有引擎处于未激活状态（即状态为 -1）</span>
<span class="token keyword">int</span> <span class="token function">hasInactiveEngines</span><span class="token punctuation">(</span><span class="token keyword">int</span> <span class="token operator">*</span>engineStatuses<span class="token punctuation">,</span> <span class="token keyword">int</span> totalEngines<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> totalEngines<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">return</span> <span class="token number">1</span><span class="token punctuation">;</span>  <span class="token comment">// 发现未激活的引擎，返回1表示"真"</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>  <span class="token comment">// 所有引擎都已激活，返回0表示"假"</span>
<span class="token punctuation">}</span>

<span class="token comment">// 激活指定引擎的相邻引擎</span>
<span class="token keyword">void</span> <span class="token function">activateAdjacentEngines</span><span class="token punctuation">(</span><span class="token keyword">int</span> <span class="token operator">*</span>engineStatuses<span class="token punctuation">,</span> <span class="token keyword">int</span> currentEngine<span class="token punctuation">,</span> <span class="token keyword">int</span> activationTime<span class="token punctuation">,</span> <span class="token keyword">int</span> totalEngines<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> leftEngine <span class="token operator">=</span> currentEngine <span class="token operator">==</span> <span class="token number">0</span> <span class="token operator">?</span> totalEngines <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">:</span> currentEngine <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">;</span>  <span class="token comment">// 计算左边相邻引擎的索引</span>
    <span class="token keyword">int</span> rightEngine <span class="token operator">=</span> currentEngine <span class="token operator">==</span> totalEngines <span class="token operator">-</span> <span class="token number">1</span> <span class="token operator">?</span> <span class="token number">0</span> <span class="token operator">:</span> currentEngine <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">;</span>  <span class="token comment">// 计算右边相邻引擎的索引</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        engineStatuses<span class="token punctuation">[</span>leftEngine<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span>  <span class="token comment">// 若左引擎未激活，则激活</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span> <span class="token operator">==</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        engineStatuses<span class="token punctuation">[</span>rightEngine<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span>  <span class="token comment">// 若右引擎未激活，则激活</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 更新所有引擎的激活状态，直到所有引擎都被激活</span>
<span class="token keyword">void</span> <span class="token function">updateEngineStatuses</span><span class="token punctuation">(</span><span class="token keyword">int</span> <span class="token operator">*</span>engineStatuses<span class="token punctuation">,</span> <span class="token keyword">int</span> startTime<span class="token punctuation">,</span> <span class="token keyword">int</span> totalEngines<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">int</span> continueUpdate <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span>continueUpdate<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> totalEngines<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> startTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token function">activateAdjacentEngines</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> i<span class="token punctuation">,</span> startTime <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">,</span> totalEngines<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 激活当前引擎的相邻引擎</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
        startTime<span class="token operator">++</span><span class="token punctuation">;</span>  <span class="token comment">// 增加时间步长，检查下一个时间点</span>
        continueUpdate <span class="token operator">=</span> <span class="token function">hasInactiveEngines</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> totalEngines<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 检查是否还有未激活的引擎</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">int</span> lastActivationTime <span class="token operator">=</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">;</span>
    <span class="token keyword">int</span> countActiveEngines <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> totalEngines<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">&gt;</span> lastActivationTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            lastActivationTime <span class="token operator">=</span> engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 更新最后一个被激活的时间</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> totalEngines<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> lastActivationTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            countActiveEngines<span class="token operator">++</span><span class="token punctuation">;</span>  <span class="token comment">// 计算最后激活时间的引擎数量</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
    <span class="token function">printf</span><span class="token punctuation">(</span><span class="token string">"%d\n"</span><span class="token punctuation">,</span> countActiveEngines<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输出最后激活时间的引擎数量</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> totalEngines<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>engineStatuses<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> lastActivationTime<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token function">printf</span><span class="token punctuation">(</span><span class="token string">"%d "</span><span class="token punctuation">,</span> i<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 输出这些引擎的编号</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
    <span class="token function">printf</span><span class="token punctuation">(</span><span class="token string">"\n"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">char</span> line<span class="token punctuation">[</span><span class="token number">1024</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    <span class="token keyword">while</span> <span class="token punctuation">(</span><span class="token function">fgets</span><span class="token punctuation">(</span>line<span class="token punctuation">,</span> <span class="token keyword">sizeof</span><span class="token punctuation">(</span>line<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token constant">stdin</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>  <span class="token comment">// 从标准输入读取一行</span>
        <span class="token keyword">int</span> numberOfEngines<span class="token punctuation">,</span> numberOfEntries<span class="token punctuation">;</span>
        <span class="token function">sscanf</span><span class="token punctuation">(</span>line<span class="token punctuation">,</span> <span class="token string">"%d %d"</span><span class="token punctuation">,</span> <span class="token operator">&amp;</span>numberOfEngines<span class="token punctuation">,</span> <span class="token operator">&amp;</span>numberOfEntries<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 解析引擎总数和输入的数据条数</span>

        <span class="token keyword">int</span> <span class="token operator">*</span>engineStatuses <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token keyword">int</span> <span class="token operator">*</span><span class="token punctuation">)</span><span class="token function">malloc</span><span class="token punctuation">(</span>numberOfEngines <span class="token operator">*</span> <span class="token keyword">sizeof</span><span class="token punctuation">(</span><span class="token keyword">int</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">memset</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">,</span> numberOfEngines <span class="token operator">*</span> <span class="token keyword">sizeof</span><span class="token punctuation">(</span><span class="token keyword">int</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 初始状态设置所有引擎为未激活</span>
        <span class="token keyword">int</span> earliestActivation <span class="token operator">=</span> INT_MAX<span class="token punctuation">;</span>

        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> numberOfEntries<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token function">fgets</span><span class="token punctuation">(</span>line<span class="token punctuation">,</span> <span class="token keyword">sizeof</span><span class="token punctuation">(</span>line<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token constant">stdin</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 读取每个激活信息</span>
            <span class="token keyword">int</span> activationTime<span class="token punctuation">,</span> engineIndex<span class="token punctuation">;</span>
            <span class="token function">sscanf</span><span class="token punctuation">(</span>line<span class="token punctuation">,</span> <span class="token string">"%d %d"</span><span class="token punctuation">,</span> <span class="token operator">&amp;</span>activationTime<span class="token punctuation">,</span> <span class="token operator">&amp;</span>engineIndex<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 解析激活时间和引擎索引</span>
            engineStatuses<span class="token punctuation">[</span>engineIndex<span class="token punctuation">]</span> <span class="token operator">=</span> activationTime<span class="token punctuation">;</span>  <span class="token comment">// 设置激活时间</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>activationTime <span class="token operator">&lt;</span> earliestActivation<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                earliestActivation <span class="token operator">=</span> activationTime<span class="token punctuation">;</span>  <span class="token comment">// 更新最早的激活时间</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
        <span class="token function">updateEngineStatuses</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">,</span> earliestActivation<span class="token punctuation">,</span> numberOfEngines<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 根据最早的激活时间开始更新状态</span>
        <span class="token function">free</span><span class="token punctuation">(</span>engineStatuses<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 释放动态分配的内存</span>
    <span class="token punctuation">}</span>
    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li><li style="color: rgb(153, 153, 153);">65</li><li style="color: rgb(153, 153, 153);">66</li><li style="color: rgb(153, 153, 153);">67</li><li style="color: rgb(153, 153, 153);">68</li><li style="color: rgb(153, 153, 153);">69</li><li style="color: rgb(153, 153, 153);">70</li><li style="color: rgb(153, 153, 153);">71</li><li style="color: rgb(153, 153, 153);">72</li><li style="color: rgb(153, 153, 153);">73</li><li style="color: rgb(153, 153, 153);">74</li><li style="color: rgb(153, 153, 153);">75</li><li style="color: rgb(153, 153, 153);">76</li><li style="color: rgb(153, 153, 153);">77</li><li style="color: rgb(153, 153, 153);">78</li><li style="color: rgb(153, 153, 153);">79</li><li style="color: rgb(153, 153, 153);">80</li><li style="color: rgb(153, 153, 153);">81</li><li style="color: rgb(153, 153, 153);">82</li><li style="color: rgb(153, 153, 153);">83</li><li style="color: rgb(153, 153, 153);">84</li></ul></pre>
                </div><div data-report-view="{&quot;mod&quot;:&quot;1585297308_001&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6548&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/banxia_frontend/article/details/141278327&quot;,&quot;extend1&quot;:&quot;pc&quot;,&quot;ab&quot;:&quot;new&quot;}"><div></div></div>
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/markdown_views-f23dff6052.css" rel="stylesheet">
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/style-c216769e99.css" rel="stylesheet">
        </div></html>