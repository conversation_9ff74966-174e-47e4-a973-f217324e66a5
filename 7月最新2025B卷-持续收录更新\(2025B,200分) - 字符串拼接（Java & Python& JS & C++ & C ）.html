<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box"><div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
             
                <div id="content_views" class="markdown_views prism-atom-one-light">
                    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
                        <path stroke-linecap="round" d="M5,0 0,2.5 5,5z" id="raphael-marker-block" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path>
                    </svg>
                    <h2><a name="t0"></a><a id="OD_1"></a>最新华为OD机试</h2> 

<h2><a name="t1"></a><a id="_100_7"></a>题目描述：构成指定长度字符串的个数 (本题分值100)</h2> 
<p>给定 M（0 &lt; M ≤ 30）个字符（a-z），从中取出任意字符（每个字符只能用一次）拼接成长度为 N（0 &lt; N ≤ 5）的字符串，</p> 
<p>要求相同的字符不能相邻，计算出给定的字符列表能拼接出多少种满足条件的字符串，</p> 
<p>输入非法或者无法拼接出满足条件的字符串则返回0。</p> 
<h2><a name="t2"></a><a id="_17"></a>输入描述</h2> 
<p>给定的字符列表和结果字符串长度，中间使用空格(" ")拼接</p> 
<h2><a name="t3"></a><a id="_23"></a>输出描述</h2> 
<p>满足条件的字符串个数</p> 
<h2><a name="t4"></a><a id="1_29"></a>用例1</h2> 
<p>输入</p> 
<pre data-index="0" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">aab 2
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>输出</p> 
<pre data-index="1" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">2
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<p>只能构成ab,ba。</p> 
<h2><a name="t5"></a><a id="2_49"></a>用例2</h2> 
<p>输入</p> 
<pre data-index="2" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">abc 2
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>输出</p> 
<pre data-index="3" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">6
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li></ul></pre> 
<p>说明</p> 
<p>可以构成：ab ac ba bc ca cb 。</p> 
<h2><a name="t6"></a><a id="_71"></a>解题思路</h2> 
<p>使用递归和回溯的思想来生成不同的字符串。具体的逻辑如下：</p> 
<ol><li> <p>首先，我们定义一个函数<code>generateDistinctStrings</code>，这个函数接收以下参数：可用字符集<code>s</code>，目标字符串长度<code>length</code>，当前已生成的字符串<code>current</code>，已生成的结果集<code>result</code>，以及一个标记数组<code>used</code>，用来记录每个字符是否已被使用。</p> </li><li> <p>在<code>generateDistinctStrings</code>函数中，首先检查当前已生成的字符串<code>current</code>的长度是否等于目标长度<code>length</code>。如果等于，说明我们已经生成了一个满足长度要求的字符串，将其添加到结果集<code>result</code>中，然后返回。</p> </li><li> <p>如果当前字符串<code>current</code>的长度还未达到目标长度<code>length</code>，我们就需要继续添加字符。此时，我们遍历可用字符集<code>s</code>中的每一个字符。对于每一个字符，我们首先检查它是否已经被使用（通过查看<code>used</code>数组），以及它是否与<code>current</code>的最后一个字符相同。如果字符已经被使用，或者与<code>current</code>的最后一个字符相同，我们就跳过这个字符，继续检查下一个字符。</p> </li><li> <p>如果一个字符未被使用，且与<code>current</code>的最后一个字符不同，我们就将它添加到<code>current</code>的末尾，然后标记这个字符为已使用，接着递归调用<code>generateDistinctStrings</code>函数，以生成下一个字符。</p> </li><li> <p>在递归调用返回后，我们需要取消对当前字符的使用标记，以便在后续的遍历中可以再次使用这个字符。这就是回溯的思想，即撤销之前的选择，尝试其他的选择。</p> </li></ol> 
<p>以下是对应的中文伪代码：</p> 
<pre data-index="4" class="set-code-show prettyprint"><code class="has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;">函数 generateDistinctStrings(s, length, current, result, used)
    如果 current的长度 等于 length
        将 current 添加到 result
        返回
    对于 s中的每一个字符 c
        如果 c已被使用 或者 c与current的最后一个字符相同
            继续下一次循环
        标记 c为已使用
        generateDistinctStrings(s, length, current + c, result, used)
        取消标记 c的使用状态
<div class="hljs-button {2}" data-title="复制"></div></code><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li></ul></pre> 
<h2><a name="t7"></a><a id="C_105"></a>C++</h2> 
<pre data-index="5" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;iostream&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;unordered_set&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;vector&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;sstream&gt;</span></span>

using namespace std<span class="token punctuation">;</span>

<span class="token comment">// 递归生成满足条件的不同字符串</span>
<span class="token keyword">void</span> <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span>string s<span class="token punctuation">,</span> <span class="token keyword">int</span> length<span class="token punctuation">,</span> string current<span class="token punctuation">,</span> unordered_set<span class="token operator">&lt;</span>string<span class="token operator">&gt;</span><span class="token operator">&amp;</span> result<span class="token punctuation">,</span> vector<span class="token operator">&lt;</span>bool<span class="token operator">&gt;</span><span class="token operator">&amp;</span> used<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 当生成的字符串长度等于指定长度时，将其加入到结果集中</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>current<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">==</span> length<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        result<span class="token punctuation">.</span><span class="token function">insert</span><span class="token punctuation">(</span>current<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword">return</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 遍历字符串中的字符</span>
    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> s<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 判断字符是否已经被使用，或者当前字符与前一个字符相同</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">||</span> <span class="token punctuation">(</span>current<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">0</span> <span class="token operator">&amp;&amp;</span> current<span class="token punctuation">.</span><span class="token function">back</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">==</span> s<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token keyword">continue</span><span class="token punctuation">;</span>  <span class="token comment">// 如果字符已被使用或与前一个字符相同，则跳过当前字符</span>
        <span class="token punctuation">}</span>
        used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> true<span class="token punctuation">;</span>  <span class="token comment">// 标记当前字符为已使用</span>
        <span class="token comment">// 递归调用生成下一个字符</span>
        <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span>s<span class="token punctuation">,</span> length<span class="token punctuation">,</span> current <span class="token operator">+</span> s<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">,</span> result<span class="token punctuation">,</span> used<span class="token punctuation">)</span><span class="token punctuation">;</span>
        used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> false<span class="token punctuation">;</span>  <span class="token comment">// 取消标记当前字符的使用状态，以便下一次遍历</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 计算满足条件的不同字符串的数量</span>
<span class="token keyword">int</span> <span class="token function">countDistinctStrings</span><span class="token punctuation">(</span>string s<span class="token punctuation">,</span> <span class="token keyword">int</span> length<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 创建一个集合来存储不同的字符串</span>
    unordered_set<span class="token operator">&lt;</span>string<span class="token operator">&gt;</span> distinctStrings<span class="token punctuation">;</span>
    <span class="token comment">// 创建一个列表来标记字符串中的字符是否已经被使用</span>
    vector<span class="token operator">&lt;</span>bool<span class="token operator">&gt;</span> <span class="token function">used</span><span class="token punctuation">(</span>s<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> false<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// 调用generateDistinctStrings方法生成满足条件的不同字符串</span>
    <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span>s<span class="token punctuation">,</span> length<span class="token punctuation">,</span> <span class="token string">""</span><span class="token punctuation">,</span> distinctStrings<span class="token punctuation">,</span> used<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// 打印生成的所有不同的字符串</span>
    <span class="token comment">// for (auto&amp; str : distinctStrings) {<!-- --></span>
       <span class="token comment">// cout &lt;&lt; str &lt;&lt; endl;</span>
    <span class="token comment">// }</span>
    <span class="token comment">// 返回不同字符串的数量</span>
    <span class="token keyword">return</span> distinctStrings<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    string input<span class="token punctuation">;</span>
    <span class="token function">getline</span><span class="token punctuation">(</span>cin<span class="token punctuation">,</span> input<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// 将输入的字符串按空格分割为两部分，分别为字符串和长度</span>
    string str<span class="token punctuation">;</span>
    <span class="token keyword">int</span> length<span class="token punctuation">;</span>
    istringstream <span class="token function">iss</span><span class="token punctuation">(</span>input<span class="token punctuation">)</span><span class="token punctuation">;</span>
    iss <span class="token operator">&gt;&gt;</span> str <span class="token operator">&gt;&gt;</span> length<span class="token punctuation">;</span>

    <span class="token comment">// 调用countDistinctStrings方法计算满足条件的不同字符串的数量</span>
    <span class="token keyword">int</span> count <span class="token operator">=</span> <span class="token function">countDistinctStrings</span><span class="token punctuation">(</span>str<span class="token punctuation">,</span> length<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// 输出计算结果</span>
    cout <span class="token operator">&lt;&lt;</span>  count <span class="token operator">&lt;&lt;</span> endl<span class="token punctuation">;</span>

    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>




<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li><li style="color: rgb(153, 153, 153);">62</li><li style="color: rgb(153, 153, 153);">63</li><li style="color: rgb(153, 153, 153);">64</li></ul></pre> 
<h2><a name="t8"></a><a id="Java_174"></a>Java</h2> 
<pre data-index="6" class="set-code-hide prettyprint"><code class="prism language-java has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token keyword">import</span> <span class="token import"><span class="token namespace">java<span class="token punctuation">.</span>util<span class="token punctuation">.</span></span><span class="token operator">*</span></span><span class="token punctuation">;</span>

<span class="token keyword">public</span> <span class="token keyword">class</span> <span class="token class-name">Main</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> args<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 创建一个Scanner对象来读取用户的输入</span>
        <span class="token class-name">Scanner</span> sc <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Scanner</span><span class="token punctuation">(</span><span class="token class-name">System</span><span class="token punctuation">.</span>in<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token comment">// 读取用户输入的字符串</span>
        <span class="token class-name">String</span> input <span class="token operator">=</span> sc<span class="token punctuation">.</span><span class="token function">nextLine</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token comment">// 将输入的字符串按空格分割为两部分，分别为字符串和长度</span>
        <span class="token class-name">String</span><span class="token punctuation">[</span><span class="token punctuation">]</span> parts <span class="token operator">=</span> input<span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">" "</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token class-name">String</span> str <span class="token operator">=</span> parts<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">;</span> <span class="token comment">// 获取输入的字符串</span>
        <span class="token keyword">int</span> length <span class="token operator">=</span> <span class="token class-name">Integer</span><span class="token punctuation">.</span><span class="token function">parseInt</span><span class="token punctuation">(</span>parts<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 将输入的长度部分转换为整数</span>

        <span class="token comment">// 调用countDistinctStrings方法计算满足条件的不同字符串的数量</span>
        <span class="token keyword">int</span> count <span class="token operator">=</span> <span class="token function">countDistinctStrings</span><span class="token punctuation">(</span>str<span class="token punctuation">,</span> length<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token comment">// 输出计算结果</span>
        <span class="token class-name">System</span><span class="token punctuation">.</span>out<span class="token punctuation">.</span><span class="token function">println</span><span class="token punctuation">(</span>count<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 计算满足条件的不同字符串的数量</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">int</span> <span class="token function">countDistinctStrings</span><span class="token punctuation">(</span><span class="token class-name">String</span> str<span class="token punctuation">,</span> <span class="token keyword">int</span> length<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 创建一个HashSet来存储不同的字符串</span>
        <span class="token class-name">HashSet</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">&gt;</span></span> set <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">HashSet</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token punctuation">&gt;</span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token comment">// 创建一个boolean数组来标记字符串中的字符是否已经被使用</span>
        <span class="token keyword">boolean</span><span class="token punctuation">[</span><span class="token punctuation">]</span> used <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token keyword">boolean</span><span class="token punctuation">[</span>str<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
        <span class="token comment">// 调用generateDistinctStrings方法生成满足条件的不同字符串</span>
        <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span>str<span class="token punctuation">,</span> length<span class="token punctuation">,</span> <span class="token string">""</span><span class="token punctuation">,</span> set<span class="token punctuation">,</span> used<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token comment">// 打印生成的所有不同的字符串</span>
        <span class="token comment">// for(String str1 : set){<!-- --></span>
           <span class="token comment">// System.out.println(str1);</span>
        <span class="token comment">// }</span>
        <span class="token comment">// 返回不同字符串的数量</span>
        <span class="token keyword">return</span> set<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 递归生成满足条件的不同字符串</span>
    <span class="token keyword">public</span> <span class="token keyword">static</span> <span class="token keyword">void</span> <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span><span class="token class-name">String</span> str<span class="token punctuation">,</span> <span class="token keyword">int</span> length<span class="token punctuation">,</span> <span class="token class-name">String</span> current<span class="token punctuation">,</span> <span class="token class-name">HashSet</span><span class="token generics"><span class="token punctuation">&lt;</span><span class="token class-name">String</span><span class="token punctuation">&gt;</span></span> set<span class="token punctuation">,</span> <span class="token keyword">boolean</span><span class="token punctuation">[</span><span class="token punctuation">]</span> used<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token comment">// 当生成的字符串长度等于指定长度时，将其加入到HashSet中</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>current<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">==</span> length<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            set<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span>current<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword">return</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// 遍历字符串中的字符</span>
        <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> str<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
            <span class="token comment">// 判断字符是否已经被使用，或者当前字符与前一个字符相同</span>
            <span class="token keyword">if</span> <span class="token punctuation">(</span>used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">||</span> <span class="token punctuation">(</span>current<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">0</span> <span class="token operator">&amp;&amp;</span> current<span class="token punctuation">.</span><span class="token function">charAt</span><span class="token punctuation">(</span>current<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">==</span> str<span class="token punctuation">.</span><span class="token function">charAt</span><span class="token punctuation">(</span>i<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
                <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果字符已被使用或与前一个字符相同，则跳过当前字符</span>
            <span class="token punctuation">}</span>
            used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span> <span class="token comment">// 标记当前字符为已使用</span>
            <span class="token comment">// 递归调用生成下一个字符</span>
            <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span>str<span class="token punctuation">,</span> length<span class="token punctuation">,</span> current <span class="token operator">+</span> str<span class="token punctuation">.</span><span class="token function">charAt</span><span class="token punctuation">(</span>i<span class="token punctuation">)</span><span class="token punctuation">,</span> set<span class="token punctuation">,</span> used<span class="token punctuation">)</span><span class="token punctuation">;</span>
            used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">;</span> <span class="token comment">// 取消标记当前字符的使用状态，以便下一次遍历</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li></ul></pre> 
<h2><a name="t9"></a><a id="javaScript_236"></a>javaScript</h2> 
<pre data-index="7" class="set-code-hide prettyprint"><code class="prism language-js has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token comment">// 导入所需的模块</span>
<span class="token keyword">const</span> readline <span class="token operator">=</span> <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">'readline'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 创建一个接口来读取用户的输入</span>
<span class="token keyword">const</span> rl <span class="token operator">=</span> readline<span class="token punctuation">.</span><span class="token function">createInterface</span><span class="token punctuation">(</span><span class="token punctuation">{<!-- --></span>
  <span class="token literal-property property">input</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdin<span class="token punctuation">,</span>
  <span class="token literal-property property">output</span><span class="token operator">:</span> process<span class="token punctuation">.</span>stdout
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 递归生成满足条件的不同字符串</span>
<span class="token keyword">function</span> <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span><span class="token parameter">str<span class="token punctuation">,</span> length<span class="token punctuation">,</span> current<span class="token punctuation">,</span> set<span class="token punctuation">,</span> used</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
  <span class="token comment">// 当生成的字符串长度等于指定长度时，将其加入到集合中</span>
  <span class="token keyword">if</span> <span class="token punctuation">(</span>current<span class="token punctuation">.</span>length <span class="token operator">===</span> length<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    set<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span>current<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword">return</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>

  <span class="token comment">// 遍历字符串中的字符</span>
  <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">let</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> str<span class="token punctuation">.</span>length<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token comment">// 判断字符是否已经被使用，或者当前字符与前一个字符相同</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">||</span> <span class="token punctuation">(</span>current<span class="token punctuation">.</span>length <span class="token operator">&gt;</span> <span class="token number">0</span> <span class="token operator">&amp;&amp;</span> current<span class="token punctuation">.</span><span class="token function">charAt</span><span class="token punctuation">(</span>current<span class="token punctuation">.</span>length <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">===</span> str<span class="token punctuation">.</span><span class="token function">charAt</span><span class="token punctuation">(</span>i<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
      <span class="token keyword">continue</span><span class="token punctuation">;</span> <span class="token comment">// 如果字符已被使用或与前一个字符相同，则跳过当前字符</span>
    <span class="token punctuation">}</span>
    used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span> <span class="token comment">// 标记当前字符为已使用</span>
    <span class="token comment">// 递归调用生成下一个字符</span>
    <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span>str<span class="token punctuation">,</span> length<span class="token punctuation">,</span> current <span class="token operator">+</span> str<span class="token punctuation">.</span><span class="token function">charAt</span><span class="token punctuation">(</span>i<span class="token punctuation">)</span><span class="token punctuation">,</span> set<span class="token punctuation">,</span> used<span class="token punctuation">)</span><span class="token punctuation">;</span>
    used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">;</span> <span class="token comment">// 取消标记当前字符的使用状态，以便下一次遍历</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// 计算满足条件的不同字符串的数量</span>
<span class="token keyword">function</span> <span class="token function">countDistinctStrings</span><span class="token punctuation">(</span><span class="token parameter">str<span class="token punctuation">,</span> length</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
  <span class="token comment">// 创建一个集合来存储不同的字符串</span>
  <span class="token keyword">const</span> set <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Set</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token comment">// 创建一个数组来标记字符串中的字符是否已经被使用</span>
  <span class="token keyword">const</span> used <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Array</span><span class="token punctuation">(</span>str<span class="token punctuation">.</span>length<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">fill</span><span class="token punctuation">(</span><span class="token boolean">false</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token comment">// 调用generateDistinctStrings方法生成满足条件的不同字符串</span>
  <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span>str<span class="token punctuation">,</span> length<span class="token punctuation">,</span> <span class="token string">""</span><span class="token punctuation">,</span> set<span class="token punctuation">,</span> used<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token comment">// 打印生成的所有不同的字符串</span>
  <span class="token comment">// for (let string of set) {<!-- --></span>
    <span class="token comment">// console.log(string);</span>
  <span class="token comment">// }</span>
  <span class="token comment">// 返回不同字符串的数量</span>
  <span class="token keyword">return</span> set<span class="token punctuation">.</span>size<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// 读取用户输入的字符串</span>
rl<span class="token punctuation">.</span><span class="token function">on</span><span class="token punctuation">(</span><span class="token string">'line'</span><span class="token punctuation">,</span> <span class="token punctuation">(</span><span class="token parameter">input</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{<!-- --></span>
  <span class="token comment">// 将输入的字符串按空格分割为两部分，分别为字符串和长度</span>
  <span class="token keyword">const</span> parts <span class="token operator">=</span> input<span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">" "</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token keyword">const</span> str <span class="token operator">=</span> parts<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">;</span> <span class="token comment">// 获取输入的字符串</span>
  <span class="token keyword">const</span> length <span class="token operator">=</span> <span class="token function">parseInt</span><span class="token punctuation">(</span>parts<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 将输入的长度部分转换为整数</span>

  <span class="token comment">// 调用countDistinctStrings方法计算满足条件的不同字符串的数量</span>
  <span class="token keyword">const</span> count <span class="token operator">=</span> <span class="token function">countDistinctStrings</span><span class="token punctuation">(</span>str<span class="token punctuation">,</span> length<span class="token punctuation">)</span><span class="token punctuation">;</span>
  <span class="token comment">// 输出计算结果</span>
  console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>count<span class="token punctuation">)</span><span class="token punctuation">;</span>

  rl<span class="token punctuation">.</span><span class="token function">close</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li><li style="color: rgb(153, 153, 153);">49</li><li style="color: rgb(153, 153, 153);">50</li><li style="color: rgb(153, 153, 153);">51</li><li style="color: rgb(153, 153, 153);">52</li><li style="color: rgb(153, 153, 153);">53</li><li style="color: rgb(153, 153, 153);">54</li><li style="color: rgb(153, 153, 153);">55</li><li style="color: rgb(153, 153, 153);">56</li><li style="color: rgb(153, 153, 153);">57</li><li style="color: rgb(153, 153, 153);">58</li><li style="color: rgb(153, 153, 153);">59</li><li style="color: rgb(153, 153, 153);">60</li><li style="color: rgb(153, 153, 153);">61</li></ul></pre> 
<h2><a name="t10"></a><a id="Python_306"></a>Python</h2> 
<pre data-index="8" class="set-code-hide prettyprint"><code class="prism language-python has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token comment"># 导入所需的模块</span>
<span class="token keyword">from</span> collections <span class="token keyword">import</span> defaultdict

<span class="token comment"># 递归生成满足条件的不同字符串</span>
<span class="token keyword">def</span> <span class="token function">generate_distinct_strings</span><span class="token punctuation">(</span>s<span class="token punctuation">,</span> length<span class="token punctuation">,</span> current<span class="token punctuation">,</span> result<span class="token punctuation">,</span> used<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token comment"># 当生成的字符串长度等于指定长度时，将其加入到结果集中</span>
    <span class="token keyword">if</span> <span class="token builtin">len</span><span class="token punctuation">(</span>current<span class="token punctuation">)</span> <span class="token operator">==</span> length<span class="token punctuation">:</span>
        result<span class="token punctuation">.</span>add<span class="token punctuation">(</span>current<span class="token punctuation">)</span>
        <span class="token keyword">return</span>

    <span class="token comment"># 遍历字符串中的字符</span>
    <span class="token keyword">for</span> i <span class="token keyword">in</span> <span class="token builtin">range</span><span class="token punctuation">(</span><span class="token builtin">len</span><span class="token punctuation">(</span>s<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
        <span class="token comment"># 判断字符是否已经被使用，或者当前字符与前一个字符相同</span>
        <span class="token keyword">if</span> used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token keyword">or</span> <span class="token punctuation">(</span><span class="token builtin">len</span><span class="token punctuation">(</span>current<span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">0</span> <span class="token keyword">and</span> current<span class="token punctuation">[</span><span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">]</span> <span class="token operator">==</span> s<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
            <span class="token keyword">continue</span>  <span class="token comment"># 如果字符已被使用或与前一个字符相同，则跳过当前字符</span>
        used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token boolean">True</span>  <span class="token comment"># 标记当前字符为已使用</span>
        <span class="token comment"># 递归调用生成下一个字符</span>
        generate_distinct_strings<span class="token punctuation">(</span>s<span class="token punctuation">,</span> length<span class="token punctuation">,</span> current <span class="token operator">+</span> s<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">,</span> result<span class="token punctuation">,</span> used<span class="token punctuation">)</span>
        used<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token boolean">False</span>  <span class="token comment"># 取消标记当前字符的使用状态，以便下一次遍历</span>

<span class="token comment"># 计算满足条件的不同字符串的数量</span>
<span class="token keyword">def</span> <span class="token function">count_distinct_strings</span><span class="token punctuation">(</span>s<span class="token punctuation">,</span> length<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token comment"># 创建一个集合来存储不同的字符串</span>
    distinct_strings <span class="token operator">=</span> <span class="token builtin">set</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
    <span class="token comment"># 创建一个列表来标记字符串中的字符是否已经被使用</span>
    used <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token boolean">False</span><span class="token punctuation">]</span> <span class="token operator">*</span> <span class="token builtin">len</span><span class="token punctuation">(</span>s<span class="token punctuation">)</span>
    <span class="token comment"># 调用generate_distinct_strings方法生成满足条件的不同字符串</span>
    generate_distinct_strings<span class="token punctuation">(</span>s<span class="token punctuation">,</span> length<span class="token punctuation">,</span> <span class="token string">""</span><span class="token punctuation">,</span> distinct_strings<span class="token punctuation">,</span> used<span class="token punctuation">)</span>
    <span class="token comment"># 打印生成的所有不同的字符串</span>
    <span class="token comment"># for string in distinct_strings:</span>
       <span class="token comment"># print(string)</span>
    <span class="token comment"># 返回不同字符串的数量</span>
    <span class="token keyword">return</span> <span class="token builtin">len</span><span class="token punctuation">(</span>distinct_strings<span class="token punctuation">)</span>

<span class="token comment"># 读取用户输入的字符串</span>
input_str <span class="token operator">=</span> <span class="token builtin">input</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
<span class="token comment"># 将输入的字符串按空格分割为两部分，分别为字符串和长度</span>
parts <span class="token operator">=</span> input_str<span class="token punctuation">.</span>split<span class="token punctuation">(</span><span class="token string">" "</span><span class="token punctuation">)</span>
s <span class="token operator">=</span> parts<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span>  <span class="token comment"># 获取输入的字符串</span>
length <span class="token operator">=</span> <span class="token builtin">int</span><span class="token punctuation">(</span>parts<span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span>  <span class="token comment"># 将输入的长度部分转换为整数</span>

<span class="token comment"># 调用count_distinct_strings方法计算满足条件的不同字符串的数量</span>
count <span class="token operator">=</span> count_distinct_strings<span class="token punctuation">(</span>s<span class="token punctuation">,</span> length<span class="token punctuation">)</span>
<span class="token comment"># 输出计算结果</span>
<span class="token keyword">print</span><span class="token punctuation">(</span>count<span class="token punctuation">)</span>

<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li></ul></pre> 
<h2><a name="t11"></a><a id="C_358"></a>C语言</h2> 
<pre data-index="9" class="set-code-hide prettyprint"><code class="prism language-c has-numbering" onclick="mdcp.copyCode(event)" style="position: unset;"><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdio.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;stdlib.h&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;string.h&gt;</span></span>

<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">define</span> <span class="token macro-name">MAX_SIZE</span> <span class="token expression"><span class="token number">31</span></span></span>

<span class="token keyword">char</span> inputString<span class="token punctuation">[</span>MAX_SIZE<span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 存储输入的字符串</span>
<span class="token keyword">int</span> stringLength<span class="token punctuation">;</span>            <span class="token comment">// 存储输入字符串的长度</span>
<span class="token keyword">int</span> targetLength<span class="token punctuation">;</span>            <span class="token comment">// 目标排列的长度</span>
<span class="token keyword">int</span> validCount <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>          <span class="token comment">// 符合条件的排列个数</span>

<span class="token comment">// 比较函数，用于qsort</span>
<span class="token keyword">int</span> <span class="token function">compare</span><span class="token punctuation">(</span><span class="token keyword">const</span> <span class="token keyword">void</span> <span class="token operator">*</span>a<span class="token punctuation">,</span> <span class="token keyword">const</span> <span class="token keyword">void</span> <span class="token operator">*</span>b<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">return</span> <span class="token punctuation">(</span><span class="token operator">*</span><span class="token punctuation">(</span><span class="token keyword">char</span> <span class="token operator">*</span><span class="token punctuation">)</span>a <span class="token operator">-</span> <span class="token operator">*</span><span class="token punctuation">(</span><span class="token keyword">char</span> <span class="token operator">*</span><span class="token punctuation">)</span>b<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword">void</span> <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span><span class="token keyword">int</span> lastUsedIndex<span class="token punctuation">,</span> <span class="token keyword">int</span> currentLength<span class="token punctuation">,</span> <span class="token keyword">int</span> usedFlags<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>currentLength <span class="token operator">==</span> targetLength<span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        validCount<span class="token operator">++</span><span class="token punctuation">;</span>
        <span class="token keyword">return</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword">for</span> <span class="token punctuation">(</span><span class="token keyword">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> stringLength<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>usedFlags<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token keyword">continue</span><span class="token punctuation">;</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>lastUsedIndex <span class="token operator">&gt;=</span> <span class="token number">0</span> <span class="token operator">&amp;&amp;</span> inputString<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> inputString<span class="token punctuation">[</span>lastUsedIndex<span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token keyword">continue</span><span class="token punctuation">;</span>
        <span class="token comment">// 优化的树层去重逻辑</span>
        <span class="token keyword">if</span> <span class="token punctuation">(</span>i <span class="token operator">&gt;</span> <span class="token number">0</span> <span class="token operator">&amp;&amp;</span> inputString<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">==</span> inputString<span class="token punctuation">[</span>i <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">]</span> <span class="token operator">&amp;&amp;</span> <span class="token operator">!</span>usedFlags<span class="token punctuation">[</span>i <span class="token operator">-</span> <span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token keyword">continue</span><span class="token punctuation">;</span>

        usedFlags<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>
        <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span>i<span class="token punctuation">,</span> currentLength <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">,</span> usedFlags<span class="token punctuation">)</span><span class="token punctuation">;</span>
        usedFlags<span class="token punctuation">[</span>i<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token keyword">int</span> <span class="token function">main</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{<!-- --></span>
    <span class="token function">scanf</span><span class="token punctuation">(</span><span class="token string">"%s %d"</span><span class="token punctuation">,</span> inputString<span class="token punctuation">,</span> <span class="token operator">&amp;</span>targetLength<span class="token punctuation">)</span><span class="token punctuation">;</span>
    stringLength <span class="token operator">=</span> <span class="token function">strlen</span><span class="token punctuation">(</span>inputString<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// 对输入字符串排序</span>
    <span class="token function">qsort</span><span class="token punctuation">(</span>inputString<span class="token punctuation">,</span> stringLength<span class="token punctuation">,</span> <span class="token keyword">sizeof</span><span class="token punctuation">(</span><span class="token keyword">char</span><span class="token punctuation">)</span><span class="token punctuation">,</span> compare<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword">int</span> usedFlags<span class="token punctuation">[</span>MAX_SIZE<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">{<!-- --></span><span class="token number">0</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
    <span class="token function">generateDistinctStrings</span><span class="token punctuation">(</span><span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">,</span> usedFlags<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token function">printf</span><span class="token punctuation">(</span><span class="token string">"%d\n"</span><span class="token punctuation">,</span> validCount<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword">return</span> <span class="token number">0</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
<div class="hljs-button {2}" data-title="复制"></div></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="https://csdnimg.cn/release/blogv2/dist/pc/img/newCodeMoreWhite.png" alt="" title=""></span></div><ul class="pre-numbering" style=""><li style="color: rgb(153, 153, 153);">1</li><li style="color: rgb(153, 153, 153);">2</li><li style="color: rgb(153, 153, 153);">3</li><li style="color: rgb(153, 153, 153);">4</li><li style="color: rgb(153, 153, 153);">5</li><li style="color: rgb(153, 153, 153);">6</li><li style="color: rgb(153, 153, 153);">7</li><li style="color: rgb(153, 153, 153);">8</li><li style="color: rgb(153, 153, 153);">9</li><li style="color: rgb(153, 153, 153);">10</li><li style="color: rgb(153, 153, 153);">11</li><li style="color: rgb(153, 153, 153);">12</li><li style="color: rgb(153, 153, 153);">13</li><li style="color: rgb(153, 153, 153);">14</li><li style="color: rgb(153, 153, 153);">15</li><li style="color: rgb(153, 153, 153);">16</li><li style="color: rgb(153, 153, 153);">17</li><li style="color: rgb(153, 153, 153);">18</li><li style="color: rgb(153, 153, 153);">19</li><li style="color: rgb(153, 153, 153);">20</li><li style="color: rgb(153, 153, 153);">21</li><li style="color: rgb(153, 153, 153);">22</li><li style="color: rgb(153, 153, 153);">23</li><li style="color: rgb(153, 153, 153);">24</li><li style="color: rgb(153, 153, 153);">25</li><li style="color: rgb(153, 153, 153);">26</li><li style="color: rgb(153, 153, 153);">27</li><li style="color: rgb(153, 153, 153);">28</li><li style="color: rgb(153, 153, 153);">29</li><li style="color: rgb(153, 153, 153);">30</li><li style="color: rgb(153, 153, 153);">31</li><li style="color: rgb(153, 153, 153);">32</li><li style="color: rgb(153, 153, 153);">33</li><li style="color: rgb(153, 153, 153);">34</li><li style="color: rgb(153, 153, 153);">35</li><li style="color: rgb(153, 153, 153);">36</li><li style="color: rgb(153, 153, 153);">37</li><li style="color: rgb(153, 153, 153);">38</li><li style="color: rgb(153, 153, 153);">39</li><li style="color: rgb(153, 153, 153);">40</li><li style="color: rgb(153, 153, 153);">41</li><li style="color: rgb(153, 153, 153);">42</li><li style="color: rgb(153, 153, 153);">43</li><li style="color: rgb(153, 153, 153);">44</li><li style="color: rgb(153, 153, 153);">45</li><li style="color: rgb(153, 153, 153);">46</li><li style="color: rgb(153, 153, 153);">47</li><li style="color: rgb(153, 153, 153);">48</li></ul></pre> 
<h2><a name="t12"></a><a id="_410"></a>完整用例</h2> 
<h3><a name="t13"></a><a id="1_411"></a>用例1</h3> 
<p>aabc 2</p> 
<h3><a name="t14"></a><a id="2_415"></a>用例2</h3> 
<p>aabb 4</p> 
<h3><a name="t15"></a><a id="3_418"></a>用例3</h3> 
<p>aab 3</p> 
<h3><a name="t16"></a><a id="4_421"></a>用例4</h3> 
<p>abcd 2</p> 
<h3><a name="t17"></a><a id="5_424"></a>用例5</h3> 
<p>abcd 4</p> 
<h3><a name="t18"></a><a id="6_427"></a>用例6</h3> 
<p>abc 4</p> 
<h3><a name="t19"></a><a id="7_430"></a>用例7</h3> 
<p>a 2</p> 
<h3><a name="t20"></a><a id="8_433"></a>用例8</h3> 
<p>a 1</p> 
<h3><a name="t21"></a><a id="9_438"></a>用例9</h3> 
<p>aaabbb 3</p> 
<h3><a name="t22"></a><a id="10_443"></a>用例10</h3> 
<p>abcdef 3<br> </p> 
<div class="toc"> 
 <h4><a name="t23"></a>文章目录</h4> 
 <ul><li><a href="#OD_1" rel="nofollow" target="_self">最新华为OD机试</a></li><li><a href="#_100_7" rel="nofollow" target="_self">题目描述：构成指定长度字符串的个数 (本题分值100)</a></li><li><a href="#_17" rel="nofollow" target="_self">输入描述</a></li><li><a href="#_23" rel="nofollow" target="_self">输出描述</a></li><li><a href="#1_29" rel="nofollow" target="_self">用例1</a></li><li><a href="#2_49" rel="nofollow" target="_self">用例2</a></li><li><a href="#_71" rel="nofollow" target="_self">解题思路</a></li><li><a href="#C_105" rel="nofollow" target="_self">C++</a></li><li><a href="#Java_174" rel="nofollow" target="_self">Java</a></li><li><a href="#javaScript_236" rel="nofollow" target="_self">javaScript</a></li><li><a href="#Python_306" rel="nofollow" target="_self">Python</a></li><li><a href="#C_358" rel="nofollow" target="_self">C语言</a></li><li><a href="#_410" rel="nofollow" target="_self">完整用例</a></li><li><ul><li><a href="#1_411" rel="nofollow" target="_self">用例1</a></li><li><a href="#2_415" rel="nofollow" target="_self">用例2</a></li><li><a href="#3_418" rel="nofollow" target="_self">用例3</a></li><li><a href="#4_421" rel="nofollow" target="_self">用例4</a></li><li><a href="#5_424" rel="nofollow" target="_self">用例5</a></li><li><a href="#6_427" rel="nofollow" target="_self">用例6</a></li><li><a href="#7_430" rel="nofollow" target="_self">用例7</a></li><li><a href="#8_433" rel="nofollow" target="_self">用例8</a></li><li><a href="#9_438" rel="nofollow" target="_self">用例9</a></li><li><a href="#10_443" rel="nofollow" target="_self">用例10</a></li></ul> 
 </li></ul> 
</div> 
<p></p> 
<p><img src="https://i-blog.csdnimg.cn/blog_migrate/b15e9ee8072c328080771f0a8be8d6c6.png" alt="doutub_gif"></p>
                </div><div data-report-view="{&quot;mod&quot;:&quot;1585297308_001&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6548&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/banxia_frontend/article/details/141961540&quot;,&quot;extend1&quot;:&quot;pc&quot;,&quot;ab&quot;:&quot;new&quot;}"><div></div></div>
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/markdown_views-f23dff6052.css" rel="stylesheet">
                <link href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/style-c216769e99.css" rel="stylesheet">
        </div></html>