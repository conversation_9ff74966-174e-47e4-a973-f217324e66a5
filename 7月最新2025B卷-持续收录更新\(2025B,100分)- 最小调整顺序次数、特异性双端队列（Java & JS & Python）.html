<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(A卷,100分)- 最小调整顺序次数、特异性双端队列（Java & JS & Python）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>有一个特异性的双端队列&#xff0c;该队列可以从头部或尾部添加数据&#xff0c;但是只能从头部移出数据。</p> 
<p>小A依次执行2n个指令往队列中添加数据和移出数据。其中n个指令是添加数据&#xff08;可能从头部添加、也可能从尾部添加&#xff09;&#xff0c;依次添加1到n&#xff1b;n个指令是移出数据。</p> 
<p>现在要求移除数据的顺序为1到n。</p> 
<p>为了满足最后输出的要求&#xff0c;小A可以在任何时候调整队列中数据的顺序。</p> 
<p></p> 
<p>请问 小A 最少需要调整几次才能够满足移除数据的顺序正好是1到n&#xff1b;</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>第一行一个数据n&#xff0c;表示数据的范围。</p> 
<p>接下来的2n行&#xff0c;其中有n行为添加数据&#xff0c;指令为&#xff1a;</p> 
<ul><li><strong>&#34;</strong>head add x&#34; 表示从头部添加数据 x&#xff0c;</li><li><strong>&#34;</strong>tail add x&#34; 表示从尾部添加数据x&#xff0c;</li></ul> 
<p>另外 n 行为移出数据指令&#xff0c;指令为&#xff1a;&#34;remove&#34; 的形式&#xff0c;表示移出1个数据&#xff1b;</p> 
<p>1 ≤ n ≤ 3 * 10^5。</p> 
<p>所有的数据均合法。</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>一个整数&#xff0c;表示 小A 要调整的最小次数。</p> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">5<br /> head add 1<br /> tail add 2<br /> remove<br /> head add 3<br /> tail add 4<br /> head add 5<br /> remove<br /> remove<br /> remove<br /> remove</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">1</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;">无</td></tr></tbody></table> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>本题重在题目意思理解&#xff0c;本题最后要求&#xff1a;最小的<span style="color:#fe2c24;">调整顺序</span>次数。而不是最小的交换次数。因此本题的难度大大降低了。</p> 
<p>比如用例&#xff1a;</p> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:39px;">1</td><td style="width:143px;">head add 1 </td><td style="width:392px;">queue &#61; [1]</td></tr><tr><td style="width:39px;">2</td><td style="width:143px;">tail add 2</td><td style="width:392px;">queue &#61; [1,2]</td></tr><tr><td style="width:39px;">3</td><td style="width:143px;">remove</td><td style="width:392px;">此时删除头部&#xff0c;顺序符合要求&#xff0c;因此不需要调整顺序&#xff0c;删除后&#xff0c;queue&#61;[2]</td></tr><tr><td style="width:39px;">4</td><td style="width:143px;">head add 3</td><td style="width:392px;">queue &#61; [3,2]</td></tr><tr><td style="width:39px;">5</td><td style="width:143px;">tail add 4 </td><td style="width:392px;">queue &#61; [3,2,4]</td></tr><tr><td style="width:39px;">6</td><td style="width:143px;">head add 5</td><td style="width:392px;">queue &#61; [5,3,2,4]</td></tr><tr><td style="width:39px;">7</td><td style="width:143px;">remove</td><td style="width:392px;">此时删除头部的元素应该是2&#xff0c;但实际是5&#xff0c;因此需要调整顺序&#xff0c;queue&#61;[2,3,4,5]&#xff0c;然后再删除头部&#xff0c;queue &#61; [3,4,5]</td></tr><tr><td style="width:39px;">8</td><td style="width:143px;">remove</td><td style="width:392px;">此时删除头部3</td></tr><tr><td style="width:39px;">9</td><td style="width:143px;">remove</td><td style="width:392px;">此时删除头部4</td></tr><tr><td style="width:39px;">10</td><td style="width:143px;">remove</td><td style="width:392px;">此时删除头部5</td></tr></tbody></table> 
<p>因此&#xff0c;只需要在第7步调整一次顺序。</p> 
<p></p> 
<p>本题不需要模拟出一个队列&#xff0c;因为那样需要频繁的验证队列元素顺序&#xff0c;以及调整顺序&#xff0c;非常不划算。</p> 
<p>我们可以总结规律&#xff1a;</p> 
<p>如果队列为空&#xff0c;即size&#61;&#61;&#61;0&#xff0c;此时无论head add&#xff0c;还是tail add&#xff0c;都不会破坏队列顺序性。</p> 
<p>如果队列不为空&#xff0c;即size!&#61;&#61;0&#xff0c;此时tail add不会破坏顺序性&#xff0c;head add会破坏顺序性。</p> 
<p>我们定义一个变量isSorted表示队列是否有序&#xff0c;初始时isSorted &#61; true&#xff0c;表示初始时队列有序。当有序性被破坏&#xff0c;即isSorted &#61; false。</p> 
<p></p> 
<p>head add和tail add会导致size&#43;&#43;&#xff0c;remove会导致size--。</p> 
<p>我们定义一个count变量来记录调整顺序的次数&#xff0c;初始为0。</p> 
<p>当remove时&#xff0c;如果isSorted为false&#xff0c;则我们需要调整顺序&#xff0c;即count&#43;&#43;&#xff0c;并更新isSorted &#61; true。</p> 
<p>当head add时&#xff0c;如果size为0&#xff0c;则不破坏顺序性&#xff0c;isSorted为true&#xff0c;如果size不为0&#xff0c;则会破坏顺序性&#xff0c;即isSorted&#61;false。另外size&#43;&#43;。</p> 
<p>当tail add时&#xff0c;仅size&#43;&#43;。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">/* JavaScript Node ACM模式 控制台输入获取 */
const readline &#61; require(&#34;readline&#34;);

const rl &#61; readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const lines &#61; [];
let n;
rl.on(&#34;line&#34;, (line) &#61;&gt; {
  lines.push(line);

  if (lines.length &#61;&#61;&#61; 1) {
    n &#61; parseInt(lines[0]);
  }

  if (n &amp;&amp; lines.length &#61;&#61;&#61; 1 &#43; 2 * n) {
    lines.shift();
    console.log(getResult(lines));
    lines.length &#61; 0;
  }
});

function getResult(cmds) {
  let size &#61; 0;
  let isSorted &#61; true;
  let count &#61; 0;

  for (let i &#61; 0; i &lt; cmds.length; i&#43;&#43;) {
    const cmd &#61; cmds[i];
    if (cmd.startsWith(&#34;head add&#34;)) {
      if (size &amp;&amp; isSorted) isSorted &#61; false;
      size&#43;&#43;;
    } else if (cmd.startsWith(&#34;tail add&#34;)) {
      size&#43;&#43;;
    } else {
      if (!size) continue;
      if (!isSorted) {
        count&#43;&#43;;
        isSorted &#61; true;
      }
      size--;
    }
  }

  return count;
}
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.Scanner;

public class Main {
  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    int n &#61; Integer.parseInt(sc.nextLine());
    int m &#61; n * 2;

    String[] cmds &#61; new String[m];
    for (int i &#61; 0; i &lt; m; i&#43;&#43;) {
      cmds[i] &#61; sc.nextLine();
    }

    System.out.println(getResult(cmds));
  }

  public static int getResult(String[] cmds) {
    int size &#61; 0;
    boolean isSorted &#61; true;
    int count &#61; 0;

    for (int i &#61; 0; i &lt; cmds.length; i&#43;&#43;) {
      String cmd &#61; cmds[i];
      if (cmd.startsWith(&#34;head add&#34;)) {
        if (size &gt; 0 &amp;&amp; isSorted) isSorted &#61; false;
        size&#43;&#43;;
      } else if (cmd.startsWith(&#34;tail add&#34;)) {
        size&#43;&#43;;
      } else {
        if (size &#61;&#61; 0) continue;
        if (!isSorted) {
          count&#43;&#43;;
          isSorted &#61; true;
        }
        size--;
      }
    }

    return count;
  }
}
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
n &#61; int(input())
cmds &#61; [input() for i in range(2 * n)]


# 算法入口
def getResult(cmds):
    size &#61; 0
    isSorted &#61; True
    count &#61; 0

    for cmd in cmds:
        if cmd.startswith(&#34;head add&#34;):
            if size &gt; 0 and isSorted:
                isSorted &#61; False
            size &#43;&#61; 1
        elif cmd.startswith(&#34;tail add&#34;):
            size &#43;&#61; 1
        else:
            if size &lt;&#61; 0:
                continue

            if not isSorted:
                count &#43;&#61; 1
                isSorted &#61; True

            size -&#61; 1

    return count


# 算法调用
print(getResult(cmds))
</code></pre>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>