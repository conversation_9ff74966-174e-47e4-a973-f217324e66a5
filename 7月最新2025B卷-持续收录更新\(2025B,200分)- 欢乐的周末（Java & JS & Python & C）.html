<html lang="zh-CN"><head><meta charset="UTF-8"><style>.nodata  main {width:1000px;margin: auto;}</style></head><body class="nodata " style=""><div class="main_father clearfix d-flex justify-content-center " style="height:100%;"> <div class="container clearfix " id="mainBox"><main><div class="blog-content-box">
<div class="article-header-box">
<div class="article-header">
<div class="article-title-box">
<h1 class="title-article" id="articleContentId">(C卷,200分)- 欢乐的周末（Java & JS & Python & C）</h1>
</div>
</div>
</div>
<div id="blogHuaweiyunAdvert"></div>

        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-044f2cf1dc.css">
                <div id="content_views" class="htmledit_views">
                    <h4 id="main-toc">题目描述</h4> 
<p>小华和小为是很要好的朋友&#xff0c;他们约定周末一起吃饭。</p> 
<p>通过手机交流&#xff0c;他们在地图上选择了多个聚餐地点&#xff08;由于自然地形等原因&#xff0c;部分聚餐地点不可达&#xff09;&#xff0c;求小华和小为都能到达的聚餐地点有多少个&#xff1f;</p> 
<p></p> 
<h4 id="%E8%BE%93%E5%85%A5%E6%8F%8F%E8%BF%B0">输入描述</h4> 
<p>第一行输入 m 和 n</p> 
<ul><li>m 代表地图的长度</li><li>n 代表地图的宽度</li></ul> 
<p>第二行开始具体输入地图信息&#xff0c;地图信息包含&#xff1a;</p> 
<ul><li>0 为通畅的道路</li><li>1 为障碍物&#xff08;且仅1为障碍物&#xff09;</li><li>2 为小华或者小为&#xff0c;地图中必定有且仅有2个 &#xff08;非障碍物&#xff09;</li><li>3 为被选中的聚餐地点&#xff08;非障碍物&#xff09;</li></ul> 
<p></p> 
<h4 id="%E8%BE%93%E5%87%BA%E6%8F%8F%E8%BF%B0">输出描述</h4> 
<p>可以被两方都到达的聚餐地点数量&#xff0c;行末无空格。</p> 
<p></p> 
<h4>备注</h4> 
<p>地图的长宽为 m 和 n&#xff0c;其中&#xff1a;</p> 
<ul><li>4 ≤ m ≤ 100</li><li>4 ≤ n ≤ 100</li></ul> 
<p>聚餐的地点数量为 k&#xff0c;则</p> 
<ul><li>1&lt; k ≤ 100</li></ul> 
<p></p> 
<h4 id="%E7%94%A8%E4%BE%8B">用例</h4> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">4 4<br /> 2 1 0 3<br /> 0 1 2 1<br /> 0 3 0 0<br /> 0 0 0 0</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">2</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;"> <p>第一行输入地图的长宽为3和4。</p> <p>第二行开始为具体的地图&#xff0c;其中&#xff1a;3代表小华和小明选择的聚餐地点&#xff1b;2代表小华或者小明&#xff08;确保有2个&#xff09;&#xff1b;0代表可以通行的位置&#xff1b;1代表不可以通行的位置。</p> <p>此时两者能都能到达的聚餐位置有2处。</p> </td></tr></tbody></table> 
<table border="1" cellpadding="1" cellspacing="1" style="width:500px;"><tbody><tr><td style="width:86px;">输入</td><td style="width:412px;">4 4<br /> 2 1 2 3<br /> 0 1 0 0<br /> 0 1 0 0<br /> 0 1 0 0</td></tr><tr><td style="width:86px;">输出</td><td style="width:412px;">0</td></tr><tr><td style="width:86px;">说明</td><td style="width:412px;"> <p>第一行输入地图的长宽为4和4。</p> <p>第二行开始为具体的地图&#xff0c;其中&#xff1a;3代表小华和小明选择的聚餐地点&#xff1b;2代表小华或者小明&#xff08;确保有2个&#xff09;&#xff1b;0代表可以通行的位置&#xff1b;1代表不可以通行的位置。</p> <p>由于图中小华和小为之间有个阻隔&#xff0c;此时&#xff0c;没有两人都能到达的聚餐地址&#xff0c;故而返回0。</p> </td></tr></tbody></table> 
<p></p> 
<p></p> 
<h4 id="%E9%A2%98%E7%9B%AE%E8%A7%A3%E6%9E%90">题目解析</h4> 
<p>本题可以使用并查集解题。</p> 
<p>小华和小为想去同一个餐厅&#xff0c;那么必然小华和小为和餐厅是可以连通&#xff0c;如果它们不能连通&#xff0c;则去不了同一个餐厅。</p> 
<p>因此&#xff0c;我们可以遍历矩阵中每一个元素&#xff0c;将它和其上下左右元素进行连接&#xff0c;需要注意的是如果遍历的元素本身是1&#xff0c;或者其上下左右的元素是1&#xff0c;则不进行连接。</p> 
<p>这样的话&#xff0c;遍历完矩阵后&#xff0c;就可以得到一个连通图。</p> 
<p>同时在遍历矩阵过程中&#xff0c;记录小华、小为&#xff08;值为2&#xff09;&#xff0c;以及餐厅&#xff08;值为3&#xff09;的位置&#xff0c;遍历结束后&#xff0c;首先看小华和小为是不是同一个祖先&#xff0c;若不是&#xff0c;则二者不可连通&#xff0c;就更别说去同一个餐厅了&#xff0c;因此返回0。若二者可以连通&#xff0c;则再看每一个餐厅的祖先是否和华为的祖先相同&#xff0c;若相同则计数&#43;&#43;&#xff0c;这样就可以得到小华&#xff0c;小为去的同一个餐厅的数量了。</p> 
<hr /> 
<p>2023.11.30 请特别注意下&#xff1a;</p> 
<p>本题输入中</p> 
<ul><li>m 代表地图的长度</li><li>n 代表地图的宽度</li></ul> 
<p>长度 m 指的是地图矩阵的行数&#xff0c;宽度 n 指的是地图矩阵的列数。</p> 
<p></p> 
<h4 id="%E7%AE%97%E6%B3%95%E6%BA%90%E7%A0%81">JavaScript算法源码</h4> 
<pre><code class="language-javascript">const rl &#61; require(&#34;readline&#34;).createInterface({ input: process.stdin });
var iter &#61; rl[Symbol.asyncIterator]();
const readline &#61; async () &#61;&gt; (await iter.next()).value;

void (async function () {
  // 长度m是行数&#xff0c; 宽度n是列数
  const [m, n] &#61; (await readline()).split(&#34; &#34;).map(Number);

  const matrix &#61; [];
  for (let i &#61; 0; i &lt; m; i&#43;&#43;) {
    matrix.push((await readline()).split(&#34; &#34;).map(Number));
  }

  console.log(getResult(matrix));
})();

function getResult(matrix) {
  const rows &#61; matrix.length;
  const cols &#61; matrix[0].length;

  const ufs &#61; new UnionFindSet(rows * cols);

  // 记录小华&#xff0c;小为的位置
  const huawei &#61; [];
  // 记录餐厅的位置
  const restrant &#61; [];

  // 上下左右四个方向偏移量
  const offsets &#61; [
    [-1, 0],
    [1, 0],
    [0, -1],
    [0, 1],
  ];

  for (let i &#61; 0; i &lt; rows; i&#43;&#43;) {
    for (let j &#61; 0; j &lt; cols; j&#43;&#43;) {
      if (matrix[i][j] !&#61;&#61; 1) {
        // 二维坐标(i, j) 转为 一维坐标pos
        const pos &#61; i * cols &#43; j;

        if (matrix[i][j] &#61;&#61;&#61; 2) {
          // 收集小华&#xff0c;小为的位置
          huawei.push(pos);
        } else if (matrix[i][j] &#61;&#61;&#61; 3) {
          // 收集餐厅的位置
          restrant.push(pos);
        }

        for (let [offsetX, offsetY] of offsets) {
          const newI &#61; i &#43; offsetX;
          const newJ &#61; j &#43; offsetY;
          if (
            newI &gt;&#61; 0 &amp;&amp;
            newI &lt; rows &amp;&amp;
            newJ &gt;&#61; 0 &amp;&amp;
            newJ &lt; cols &amp;&amp;
            matrix[newI][newJ] !&#61; 1
          ) {
            // 如果(i,j)和&#xff08;newI,newJ&#xff09;位置都是非1&#xff0c;则合并
            ufs.union(pos, newI * cols &#43; newJ);
          }
        }
      }
    }
  }

  const [hua, wei] &#61; huawei;

  // 小华所在连通分量的根
  const hua_fa &#61; ufs.find(hua);

  // 小为所在连通分量的根
  const wei_fa &#61; ufs.find(wei);

  // 如果小华和小为的不属于同一个连通分量&#xff0c;则二人无法去往相同餐厅
  if (hua_fa !&#61;&#61; wei_fa) {
    return 0;
  }

  // 找出和小华、小为在同一个连通分量里面的餐厅
  return restrant.filter((r) &#61;&gt; ufs.find(r) &#61;&#61;&#61; hua_fa).length;
}

// 并查集实现
class UnionFindSet {
  constructor(n) {
    this.fa &#61; new Array(n).fill(0).map((_, idx) &#61;&gt; idx);
  }

  find(x) {
    if (x !&#61;&#61; this.fa[x]) {
      return (this.fa[x] &#61; this.find(this.fa[x]));
    }
    return x;
  }

  union(x, y) {
    const x_fa &#61; this.find(x);
    const y_fa &#61; this.find(y);

    if (x_fa !&#61;&#61; y_fa) {
      this.fa[y_fa] &#61; x_fa;
    }
  }
}
</code></pre> 
<p></p> 
<h4>Java算法源码</h4> 
<pre><code class="language-java">import java.util.ArrayList;
import java.util.Scanner;

public class Main {

  public static void main(String[] args) {
    Scanner sc &#61; new Scanner(System.in);

    // 长度m表示行数
    int m &#61; sc.nextInt();
    // 宽度n表示列数
    int n &#61; sc.nextInt();

    int[][] matrix &#61; new int[m][n];

    for (int i &#61; 0; i &lt; m; i&#43;&#43;) {
      for (int j &#61; 0; j &lt; n; j&#43;&#43;) {
        matrix[i][j] &#61; sc.nextInt();
      }
    }

    System.out.println(getResult(matrix));
  }

  public static int getResult(int[][] matrix) {
    int rows &#61; matrix.length;
    int cols &#61; matrix[0].length;

    UnionFindSet ufs &#61; new UnionFindSet(rows * cols);

    // 记录小华&#xff0c;小为的位置
    ArrayList&lt;Integer&gt; huawei &#61; new ArrayList&lt;&gt;();
    // 记录餐厅的位置
    ArrayList&lt;Integer&gt; restaurants &#61; new ArrayList&lt;&gt;();

    // 上下左右四个方向偏移量
    int[][] offsets &#61; {<!-- -->{-1, 0}, {1, 0}, {0, -1}, {0, 1}};

    for (int i &#61; 0; i &lt; rows; i&#43;&#43;) {
      for (int j &#61; 0; j &lt; cols; j&#43;&#43;) {
        if (matrix[i][j] !&#61; 1) {
          // 二维坐标(i, j) 转为 一维坐标pos
          int pos &#61; i * cols &#43; j;

          if (matrix[i][j] &#61;&#61; 2) {
            // 收集小华&#xff0c;小为的位置
            huawei.add(pos);
          } else if (matrix[i][j] &#61;&#61; 3) {
            // 收集餐厅的位置
            restaurants.add(pos);
          }

          for (int[] offset : offsets) {
            int newI &#61; i &#43; offset[0];
            int newJ &#61; j &#43; offset[1];
            if (newI &gt;&#61; 0 &amp;&amp; newI &lt; rows &amp;&amp; newJ &gt;&#61; 0 &amp;&amp; newJ &lt; cols &amp;&amp; matrix[newI][newJ] !&#61; 1) {
              // 如果(i,j)和&#xff08;newI,newJ&#xff09;位置都是非1&#xff0c;则合并
              ufs.union(pos, newI * cols &#43; newJ);
            }
          }
        }
      }
    }

    // 小华所在连通分量的根
    int hua_fa &#61; ufs.find(huawei.get(0));
    // 小为所在连通分量的根
    int wei_fa &#61; ufs.find(huawei.get(1));

    // 如果小华和小为的不属于同一个连通分量&#xff0c;则二人无法去往相同餐厅
    if (hua_fa !&#61; wei_fa) {
      return 0;
    }

    // 找出和小华、小为在同一个连通分量里面的餐厅
    int ans &#61; 0;
    for (Integer restaurant : restaurants) {
      if (ufs.find(restaurant) &#61;&#61; hua_fa) {
        ans&#43;&#43;;
      }
    }

    return ans;
  }
}

// 并查集实现
class UnionFindSet {
  int[] fa;

  public UnionFindSet(int n) {
    fa &#61; new int[n];
    for (int i &#61; 0; i &lt; n; i&#43;&#43;) fa[i] &#61; i;
  }

  public int find(int x) {
    if (x !&#61; this.fa[x]) {
      this.fa[x] &#61; this.find(this.fa[x]);
      return this.fa[x];
    }
    return x;
  }

  public void union(int x, int y) {
    int x_fa &#61; this.find(x);
    int y_fa &#61; this.find(y);

    if (x_fa !&#61; y_fa) {
      this.fa[y_fa] &#61; x_fa;
    }
  }
}
</code></pre> 
<p></p> 
<h4>Python算法源码</h4> 
<pre><code class="language-python"># 输入获取
m, n &#61; map(int, input().split())  # 长度m是行数&#xff0c; 宽度n是列数
matrix &#61; [list(map(int, input().split())) for _ in range(m)]


# 并查集实现
class UnionFindSet:
    def __init__(self, n):
        self.fa &#61; [i for i in range(n)]

    def find(self, x):
        if x !&#61; self.fa[x]:
            self.fa[x] &#61; self.find(self.fa[x])
            return self.fa[x]
        return x

    def union(self, x, y):
        x_fa &#61; self.find(x)
        y_fa &#61; self.find(y)
        if x_fa !&#61; y_fa:
            self.fa[y_fa] &#61; x_fa


# 算法入口
def getResult():
    rows &#61; len(matrix)
    cols &#61; len(matrix[0])

    ufs &#61; UnionFindSet(rows * cols)

    #  记录小华&#xff0c;小为的位置
    huawei &#61; []
    # 记录餐厅的位置
    restaurants &#61; []
    # 上下左右四个方向偏移量
    offsets &#61; ((-1, 0), (1, 0), (0, -1), (0, 1))

    for i in range(rows):
        for j in range(cols):
            if matrix[i][j] !&#61; 1:
                # 二维坐标(i, j) 转为 一维坐标pos
                pos &#61; i * cols &#43; j

                if matrix[i][j] &#61;&#61; 2:
                    # 收集小华&#xff0c;小为的位置
                    huawei.append(pos)
                elif matrix[i][j] &#61;&#61; 3:
                    # 收集餐厅的位置
                    restaurants.append(pos)

                for offset in offsets:
                    newI &#61; i &#43; offset[0]
                    newJ &#61; j &#43; offset[1]

                    if 0 &lt;&#61; newI &lt; rows and 0 &lt;&#61; newJ &lt; cols and matrix[newI][newJ] !&#61; 1:
                        # 如果(i,j)和&#xff08;newI,newJ&#xff09;位置都是非1&#xff0c;则合并
                        ufs.union(pos, newI * cols &#43; newJ)

    # 小华所在连通分量的根
    hua_fa &#61; ufs.find(huawei[0])
    # 小为所在连通分量的根
    wei_fa &#61; ufs.find(huawei[1])

    # 如果小华和小为的不属于同一个连通分量&#xff0c;则二人无法去往相同餐厅
    if hua_fa !&#61; wei_fa:
        return 0

    # 找出和小华、小为在同一个连通分量里面的餐厅
    ans &#61; 0
    for r in restaurants:
        if ufs.find(r) &#61;&#61; hua_fa:
            ans &#43;&#61; 1

    return ans


# 算法调用
print(getResult())
</code></pre> 
<p></p> 
<h4>C算法源码</h4> 
<pre><code class="language-cpp">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;

/** 并查集定义 **/
typedef struct {
    int *fa;
    int count;
} UFS;

UFS *new_UFS(int n) {
    UFS *ufs &#61; (UFS *) malloc(sizeof(UFS));

    ufs-&gt;fa &#61; (int *) malloc(sizeof(int) * n);
    for (int i &#61; 0; i &lt; n; i&#43;&#43;) {
        ufs-&gt;fa[i] &#61; i;
    }

    ufs-&gt;count &#61; n;

    return ufs;
}

int find_UFS(UFS *ufs, int x) {
    if (x !&#61; ufs-&gt;fa[x]) {
        ufs-&gt;fa[x] &#61; find_UFS(ufs, ufs-&gt;fa[x]);
        return ufs-&gt;fa[x];
    }
    return x;
}

void union_UFS(UFS *ufs, int x, int y) {
    int x_fa &#61; find_UFS(ufs, x);
    int y_fa &#61; find_UFS(ufs, y);

    if (x_fa !&#61; y_fa) {
        ufs-&gt;fa[y_fa] &#61; x_fa;
        ufs-&gt;count--;
    }
}

int main() {
    // 长度m表示行数, 宽度n表示列数
    int m, n;
    scanf(&#34;%d %d&#34;, &amp;m, &amp;n);

    int matrix[m][n];
    for (int i &#61; 0; i &lt; m; i&#43;&#43;) {
        for (int j &#61; 0; j &lt; n; j&#43;&#43;) {
            scanf(&#34;%d&#34;, &amp;matrix[i][j]);
        }
    }

    UFS *ufs &#61; new_UFS(n * m);

    // 记录小华&#xff0c;小为的位置
    int huawei[2];
    int huawei_size &#61; 0;

    // 记录餐厅的位置
    int restaurants[m * n];
    int restaurants_size &#61; 0;

    // 上下左右四个方向偏移量
    int offsets[4][2] &#61; {<!-- -->{-1, 0},
                         {1,  0},
                         {0,  -1},
                         {0,  1}};

    for (int i &#61; 0; i &lt; m; i&#43;&#43;) {
        for (int j &#61; 0; j &lt; n; j&#43;&#43;) {
            if(matrix[i][j] &#61;&#61; 1) continue;

            // 二维坐标(i, j) 转为 一维坐标pos
            int pos &#61; i * n &#43; j;

            switch (matrix[i][j]) {
                case 2:
                    // 收集小华&#xff0c;小为的位置
                    huawei[huawei_size&#43;&#43;] &#61; pos;
                    break;
                case 3:
                    // 收集餐厅的位置
                    restaurants[restaurants_size&#43;&#43;] &#61; pos;
                    break;
            }

            for (int k &#61; 0; k &lt; 4; k&#43;&#43;) {
                int newI &#61; i &#43; offsets[k][0];
                int newJ &#61; j &#43; offsets[k][1];
                if (newI &gt;&#61; 0 &amp;&amp; newI &lt; m &amp;&amp; newJ &gt;&#61; 0 &amp;&amp; newJ &lt; n &amp;&amp; matrix[newI][newJ] !&#61; 1) {
                    // 如果(i,j)和&#xff08;newI,newJ&#xff09;位置都是非1&#xff0c;则合并
                    union_UFS(ufs, pos, newI * n &#43; newJ);
                }
            }
        }
    }

    // 小华所在连通分量的根
    int hua_fa &#61; find_UFS(ufs, huawei[0]);
    // 小为所在连通分量的根
    int wei_fa &#61; find_UFS(ufs, huawei[1]);

    if (hua_fa !&#61; wei_fa) {
        // 如果小华和小为的不属于同一个连通分量&#xff0c;则二人无法去往相同餐厅
        puts(&#34;0&#34;);
    } else {
        // 找出和小华、小为在同一个连通分量里面的餐厅
        int ans &#61; 0;
        for (int i &#61; 0; i &lt; restaurants_size; i&#43;&#43;) {
            if (find_UFS(ufs, restaurants[i]) &#61;&#61; hua_fa) {
                ans&#43;&#43;;
            }
        }
        printf(&#34;%d\n&#34;, ans);
    }

    return 0;
}</code></pre> 
<p></p>
                </div>
        </div>
        <div id="treeSkill"></div>
        <div id="blogExtensionBox" style="width:400px;margin:auto;margin-top:12px" class="blog-extension-box"></div>
    <script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div></html>